[{"name": "worksheet", "description": "工作表", "data": [{"name": "edit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "修改打印模板筛选条件", "comments": "*\n  * 修改打印模板筛选条件\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {array} args.filters 筛选条件\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "array", "name": "args.filters", "description": "筛选条件"}]}, {"name": "editTemplateDownloadPermission", "description": "修改模板下载权限", "comments": "*\n  * 修改模板下载权限\n  * @param {Object} args 请求参数\n  * @param {string} args.id 打印模板id\n  * @param {} args.allowDownloadPermission\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "打印模板id"}]}, {"name": "editPrintTemplateSort", "description": "修改打印模板排序", "comments": "*\n  * 修改打印模板排序\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.worksheetId\n  * @param {array} args.sortItems\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.sortItems", "description": ""}]}, {"name": "deletePrint", "description": "删除打印模板", "comments": "*\n  * 删除打印模板\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}]}, {"name": "getRowIndexes", "description": "获取 工作表 索引字段配置", "comments": "*\n  * 获取 工作表 索引字段配置\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表Id"}]}, {"name": "addRowIndex", "description": "新增 工作表行内容表索引", "comments": "*\n  * 新增 工作表行内容表索引\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.customeIndexName 自定义索引名称\n  * @param {array} args.indexFields 索引字段\n  * @param {boolean} args.uniqueIndex 是否 唯一索引\n  * @param {boolean} args.wildcardIndex 是否 通配符文本索引\n  * @param {boolean} args.sparseIndex 是否 稀疏索引\n  * @param {boolean} args.backgroundIndex 是否 后台索引\n  * @param {string} args.appId AppId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.customeIndexName", "description": "自定义索引名称"}, {"type": "array", "name": "args.indexFields", "description": "索引字段"}, {"type": "boolean", "name": "args.uniqueIndex", "description": "是否 唯一索引"}, {"type": "boolean", "name": "args.wildcardIndex", "description": "是否 通配符文本索引"}, {"type": "boolean", "name": "args.sparseIndex", "description": "是否 稀疏索引"}, {"type": "boolean", "name": "args.backgroundIndex", "description": "是否 后台索引"}, {"type": "string", "name": "args.appId", "description": "AppId"}]}, {"name": "updateRowIndex", "description": "更新 工作表行内容表索引", "comments": "*\n  * 更新 工作表行内容表索引\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.customeIndexName 自定义索引名称\n  * @param {array} args.indexFields 索引字段\n  * @param {boolean} args.uniqueIndex 是否 唯一索引\n  * @param {boolean} args.wildcardIndex 是否 通配符文本索引\n  * @param {boolean} args.sparseIndex 是否 稀疏索引\n  * @param {boolean} args.backgroundIndex 是否 后台索引\n  * @param {string} args.indexConfigId 索引配置Id\n（系统级索引可为空）\n  * @param {string} args.appId AppId\n  * @param {boolean} args.isSystemIndex 是否 系统级索引\n  * @param {string} args.systemIndexName 系统级索引名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.customeIndexName", "description": "自定义索引名称"}, {"type": "array", "name": "args.indexFields", "description": "索引字段"}, {"type": "boolean", "name": "args.uniqueIndex", "description": "是否 唯一索引"}, {"type": "boolean", "name": "args.wildcardIndex", "description": "是否 通配符文本索引"}, {"type": "boolean", "name": "args.sparseIndex", "description": "是否 稀疏索引"}, {"type": "boolean", "name": "args.backgroundIndex", "description": "是否 后台索引"}, {"type": "string", "name": "args.indexConfigId", "description": "索引配置Id"}, {"type": "string", "name": "args.appId", "description": "AppId"}, {"type": "boolean", "name": "args.isSystemIndex", "description": "是否 系统级索引"}, {"type": "string", "name": "args.systemIndexName", "description": "系统级索引名称"}]}, {"name": "updateRowIndexCustomeIndexName", "description": "更新 工作表行内容表索引名称", "comments": "*\n  * 更新 工作表行内容表索引名称\n  * @param {Object} args 请求参数\n  * @param {string} args.appId AppId\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.indexConfigId 索引配置Id\n  * @param {string} args.customeIndexName 自定义索引名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "AppId"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.indexConfigId", "description": "索引配置Id"}, {"type": "string", "name": "args.customeIndexName", "description": "自定义索引名称"}]}, {"name": "removeRowIndex", "description": "移除 工作表行内容表索引", "comments": "*\n  * 移除 工作表行内容表索引\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.indexConfigId 索引配置Id\n  * @param {boolean} args.isSystemIndex 是否 系统级索引\n  * @param {string} args.systemIndexName 系统级索引名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.indexConfigId", "description": "索引配置Id"}, {"type": "boolean", "name": "args.isSystemIndex", "description": "是否 系统级索引"}, {"type": "string", "name": "args.systemIndexName", "description": "系统级索引名称"}]}, {"name": "getLinkDetail", "description": "获取链接行记录", "comments": "*\n  * 获取链接行记录\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.id\n  * @param {string} args.password\n  * @param {} args.langType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.password", "description": ""}]}, {"name": "getFormSubmissionSettings", "description": "获取工作表创建记录表单提交设置信息", "comments": "*\n  * 获取工作表创建记录表单提交设置信息\n  * @param {Object} args 请求参数\n  * @param {string} args.workSheetId 工作表Id\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.workSheetId", "description": "工作表Id"}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "editWorksheetSetting", "description": "更新工作表创建记录表单设置信息", "comments": "*\n  * 更新工作表创建记录表单设置信息\n  * @param {Object} args 请求参数\n  * @param {string} args.workSheetId 工作表id\n  * @param {string} args.appId 应用id\n  * @param {object} args.advancedSetting 配置项数据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.workSheetId", "description": "工作表id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "object", "name": "args.advancedSetting", "description": "配置项数据"}]}, {"name": "getSwitch", "description": "获取功能系统开关配置", "comments": "*\n  * 获取功能系统开关配置\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}]}, {"name": "editSwitch", "description": "更新系统配置开关（单个）", "comments": "*\n  * 更新系统配置开关（单个）\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {boolean} args.state 开关\n  * @param {} args.type\n  * @param {} args.roleType\n  * @param {array} args.viewIds\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "boolean", "name": "args.state", "description": "开关"}, {"type": "array", "name": "args.viewIds", "description": ""}]}, {"name": "batchEditSwitch", "description": "更新系统配置开关（批量）", "comments": "*\n  * 更新系统配置开关（批量）\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.switchList\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.switchList", "description": ""}]}, {"name": "getSwitchPermit", "description": "获取功能系统开关（包含管理员判断）", "comments": "*\n  * 获取功能系统开关（包含管理员判断）\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用管理员\n  * @param {string} args.worksheetId 工作表id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用管理员"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}]}, {"name": "getWorksheetApiInfo", "description": "获取工作表信息", "comments": "*\n  * 获取工作表信息\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.appId 应用id\n  * @param {integer} args.version 版本  1=v1  2=v2\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.version", "description": "版本  1=v1  2=v2"}]}, {"name": "getCollectionsByAppId", "description": "获取应用下选项集", "comments": "*\n  * 获取应用下选项集\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "saveOptionsCollection", "description": "保存选项集", "comments": "*\n  * 保存选项集\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "updateOptionsCollectionAppId", "description": "更新选项集所属应用", "comments": "*\n  * 更新选项集所属应用\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "deleteOptionsCollection", "description": "删除选项集", "comments": "*\n  * 删除选项集\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "getCollectionByCollectId", "description": "获取选项集详细数据", "comments": "*\n  * 获取选项集详细数据\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "getCollectionsByCollectIds", "description": "批量获取选项集", "comments": "*\n  * 批量获取选项集\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "getQuoteControlsById", "description": "获取选项集引用的控件列表", "comments": "*\n  * 获取选项集引用的控件列表\n  * @param {Object} args 请求参数\n  * @param {string} args.collectionId\n  * @param {array} args.collectionIds\n  * @param {string} args.appId\n  * @param {string} args.worksheetId\n  * @param {array} args.options\n  * @param {string} args.name\n  * @param {boolean} args.colorful\n  * @param {boolean} args.enableScore\n  * @param {integer} args.status 0或者1：正常 9：停用,999：删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.collectionId", "description": ""}, {"type": "array", "name": "args.collectionIds", "description": ""}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "array", "name": "args.options", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.colorful", "description": ""}, {"type": "boolean", "name": "args.enableScore", "description": ""}, {"type": "integer", "name": "args.status", "description": "0或者1：正常 9：停用,999：删除"}]}, {"name": "addOrUpdateOptionSetApiInfo", "description": "获取添加选项接集接口信息", "comments": "*\n  * 获取添加选项接集接口信息\n  * @param {Object} args 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": []}, {"name": "optionSetListApiInfo", "description": "获取选项接集列表接口信息", "comments": "*\n  * 获取选项接集列表接口信息\n  * @param {Object} args 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": []}, {"name": "ocr", "description": "工作表OCR识别", "comments": "*\n  * 工作表OCR识别\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.controlId ocr控件id\n  * @param {array} args.data ocr映射url数组(不管单个还是多个批量,都是数组)\nremark:待识别文件url ，图片的 Url 地址。要求图片经Base64编码后不超过 7M，分辨率建议500*800以上，支持PNG、JPG、JPEG、BMP格式。建议卡片部分占据图片2/3以上。 建议图片存储于腾讯云，可保障更高的下载速度和稳定性\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.controlId", "description": "ocr控件id"}, {"type": "array", "name": "args.data", "description": "ocr映射url数组(不管单个还是多个批量,都是数组)"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "get单个工作表查询", "comments": "*\n  * get单个工作表查询\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}]}, {"name": "getQueryBySheetId", "description": "worksheetId 批量获取工作表查询", "comments": "*\n  * worksheetId 批量获取工作表查询\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}]}, {"name": "saveQuery", "description": "保存工作表查询", "comments": "*\n  * 保存工作表查询\n  * @param {Object} args 请求参数\n  * @param {string} args.id id 新建为空，修改传原值\n  * @param {string} args.worksheetId 本表id\n  * @param {string} args.controlId 默认值控件id\n  * @param {string} args.sourceId 来源id （这里值得工作表id）\n  * @param {integer} args.sourceType 1 = 本表，2 = 他表\n  * @param {array} args.items 筛选条件\n  * @param {array} args.configs 映射字段\n  * @param {integer} args.moreType 0 = 获取第一条时，按配置来，1= 不赋值\n  * @param {array} args.moreSort 排序\n  * @param {integer} args.queryCount 查询条数\n  * @param {integer} args.resultType 结果类型 0=查询到记录，1=仅查询到一条记录，2=查询到多条记录，3=未查询到记录\n  * @param {integer} args.eventType 0 = 常规字段默认值，1 = 表单事件\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "id 新建为空，修改传原值"}, {"type": "string", "name": "args.worksheetId", "description": "本表id"}, {"type": "string", "name": "args.controlId", "description": "默认值控件id"}, {"type": "string", "name": "args.sourceId", "description": "来源id （这里值得工作表id）"}, {"type": "integer", "name": "args.sourceType", "description": "1 = 本表，2 = 他表"}, {"type": "array", "name": "args.items", "description": "筛选条件"}, {"type": "array", "name": "args.configs", "description": "映射字段"}, {"type": "integer", "name": "args.moreType", "description": "0 = 获取第一条时，按配置来，1= 不赋值"}, {"type": "array", "name": "args.moreSort", "description": "排序"}, {"type": "integer", "name": "args.queryCount", "description": "查询条数"}, {"type": "integer", "name": "args.resultType", "description": "结果类型 0=查询到记录，1=仅查询到一条记录，2=查询到多条记录，3=未查询到记录"}, {"type": "integer", "name": "args.eventType", "description": "0 = 常规字段默认值，1 = 表单事件"}]}, {"name": "saveFiltersGroup", "description": "保存筛选组件", "comments": "*\n  * 保存筛选组件\n  * @param {Object} args 请求参数\n  * @param {string} args.filtersGroupId 筛选组件ID\n  * @param {string} args.name 名称\n  * @param {boolean} args.enableBtn 开启搜索按钮\n  * @param {array} args.filters filters\n  * @param {object} args.advancedSetting 视图高级配置\n  * @param {string} args.appId 应用ID\n  * @param {array} args.filtersGroupIds 批量获取和删除使用\n  * @param {string} args.pageId 自定义页面ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.filtersGroupId", "description": "筛选组件ID"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "boolean", "name": "args.enableBtn", "description": "开启搜索按钮"}, {"type": "array", "name": "args.filters", "description": "filters"}, {"type": "object", "name": "args.advancedSetting", "description": "视图高级配置"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "array", "name": "args.filtersGroupIds", "description": "批量获取和删除使用"}, {"type": "string", "name": "args.pageId", "description": "自定义页面ID"}]}, {"name": "getFiltersGroupByIds", "description": "获取筛选组件", "comments": "*\n  * 获取筛选组件\n  * @param {Object} args 请求参数\n  * @param {string} args.filtersGroupId 筛选组件ID\n  * @param {string} args.name 名称\n  * @param {boolean} args.enableBtn 开启搜索按钮\n  * @param {array} args.filters filters\n  * @param {object} args.advancedSetting 视图高级配置\n  * @param {string} args.appId 应用ID\n  * @param {array} args.filtersGroupIds 批量获取和删除使用\n  * @param {string} args.pageId 自定义页面ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.filtersGroupId", "description": "筛选组件ID"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "boolean", "name": "args.enableBtn", "description": "开启搜索按钮"}, {"type": "array", "name": "args.filters", "description": "filters"}, {"type": "object", "name": "args.advancedSetting", "description": "视图高级配置"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "array", "name": "args.filtersGroupIds", "description": "批量获取和删除使用"}, {"type": "string", "name": "args.pageId", "description": "自定义页面ID"}]}, {"name": "deleteFiltersGroupByIds", "description": "删除筛选组件", "comments": "*\n  * 删除筛选组件\n  * @param {Object} args 请求参数\n  * @param {string} args.filtersGroupId 筛选组件ID\n  * @param {string} args.name 名称\n  * @param {boolean} args.enableBtn 开启搜索按钮\n  * @param {array} args.filters filters\n  * @param {object} args.advancedSetting 视图高级配置\n  * @param {string} args.appId 应用ID\n  * @param {array} args.filtersGroupIds 批量获取和删除使用\n  * @param {string} args.pageId 自定义页面ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.filtersGroupId", "description": "筛选组件ID"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "boolean", "name": "args.enableBtn", "description": "开启搜索按钮"}, {"type": "array", "name": "args.filters", "description": "filters"}, {"type": "object", "name": "args.advancedSetting", "description": "视图高级配置"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "array", "name": "args.filtersGroupIds", "description": "批量获取和删除使用"}, {"type": "string", "name": "args.pageId", "description": "自定义页面ID"}]}, {"name": "excute<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "执行api查询", "comments": "*\n  * 执行api查询\n  * @param {Object} args 请求参数\n  * @param {object} args.data 执行api查询数据\n  * @param {string} args.projectId 组织id\n  * @param {string} args.workSheetId 工作表id\n  * @param {string} args.controlId 控件id\n  * @param {string} args.apiTemplateId api模板id\n  * @param {string} args.apkId 应用id\n  * @param {string} args.formId 公开表单id\n  * @param {string} args.apiEventId 动作事件id（不传默认识别为api查询字段）\n  * @param {string} args.authId 授权账户Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "object", "name": "args.data", "description": "执行api查询数据"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.workSheetId", "description": "工作表id"}, {"type": "string", "name": "args.controlId", "description": "控件id"}, {"type": "string", "name": "args.apiTemplateId", "description": "api模板id"}, {"type": "string", "name": "args.apkId", "description": "应用id"}, {"type": "string", "name": "args.formId", "description": "公开表单id"}, {"type": "string", "name": "args.apiEventId", "description": "动作事件id（不传默认识别为api查询字段）"}, {"type": "string", "name": "args.authId", "description": "授权账户Id"}]}, {"name": "getApiControlDetail", "description": "获取api模板消息信息", "comments": "*\n  * 获取api模板消息信息\n  * @param {Object} args 请求参数\n  * @param {string} args.apiTemplateId api模板id\n  * @param {integer} args.type 是否为请求参数模板 1-请求模板 2-响应模板 不传-请求响应\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.apiTemplateId", "description": "api模板id"}, {"type": "integer", "name": "args.type", "description": "是否为请求参数模板 1-请求模板 2-响应模板 不传-请求响应"}]}, {"name": "sortAttachment", "description": "更新附件排序", "comments": "*\n  * 更新附件排序\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 表id\n  * @param {string} args.rowId\n  * @param {string} args.controlId 附件控件id\n  * @param {string} args.viewId\n  * @param {array} args.fileIds 附件ids（排好序的）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "表id"}, {"type": "string", "name": "args.rowId", "description": ""}, {"type": "string", "name": "args.controlId", "description": "附件控件id"}, {"type": "string", "name": "args.viewId", "description": ""}, {"type": "array", "name": "args.fileIds", "description": "附件ids（排好序的）"}]}, {"name": "editAttachmentName", "description": "更新记录附件名", "comments": "*\n  * 更新记录附件名\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {boolean} args.getTemplate 是否获取模板\n  * @param {string} args.shareId 分享页获取关联记录iD\n  * @param {boolean} args.checkView 是否验证视图\n  * @param {string} args.relationWorksheetId 关联控件ID\n  * @param {string} args.fileId\n  * @param {string} args.fileName\n  * @param {string} args.controlId 附件的控件id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取模板"}, {"type": "string", "name": "args.shareId", "description": "分享页获取关联记录iD"}, {"type": "boolean", "name": "args.checkView", "description": "是否验证视图"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联控件ID"}, {"type": "string", "name": "args.fileId", "description": ""}, {"type": "string", "name": "args.fileName", "description": ""}, {"type": "string", "name": "args.controlId", "description": "附件的控件id"}]}, {"name": "getExportConfig", "description": "获取导出excel配置", "comments": "*\n  * 获取导出excel配置\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.viewId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.viewId", "description": ""}]}, {"name": "saveExportConfig", "description": "保存导出配置", "comments": "*\n  * 保存导出配置\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.viewId\n  * @param {array} args.exportExtIds 导出特殊列配置\n  * @param {array} args.controlIds 需要导出的控件ids\n  * @param {} args.type\n  * @param {} args.exportFieldType\n  * @param {boolean} args.getColumnRpt 是否导出列统计\n  * @param {boolean} args.edited 是否允许修改\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.viewId", "description": ""}, {"type": "array", "name": "args.exportExtIds", "description": "导出特殊列配置"}, {"type": "array", "name": "args.controlIds", "description": "需要导出的控件ids"}, {"type": "boolean", "name": "args.getColumnRpt", "description": "是否导出列统计"}, {"type": "boolean", "name": "args.edited", "description": "是否允许修改"}]}, {"name": "getViewPermission", "description": "获取视图权限", "comments": "*\n  * 获取视图权限\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "getAppExtendAttr", "description": "获取应用角色用户扩展属性", "comments": "*\n  * 获取应用角色用户扩展属性\n  * @param {Object} args 请求参数\n  * @param {string} args.appId AppId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "AppId"}]}, {"name": "getExtendAttrOptionalControl", "description": "获取工作表的扩展属性选项控件信息", "comments": "*\n  * 获取工作表的扩展属性选项控件信息\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表Id"}]}, {"name": "saveAppExtendAttr", "description": "保存应用角色用户扩展属性", "comments": "*\n  * 保存应用角色用户扩展属性\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.userControlId 用户控件\n  * @param {array} args.extendAttrs 扩展字段属性\n  * @param {integer} args.status 状态【9：关闭 1：正常】\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.userControlId", "description": "用户控件"}, {"type": "array", "name": "args.extendAttrs", "description": "扩展字段属性"}, {"type": "integer", "name": "args.status", "description": "状态【9：关闭 1：正常】"}]}, {"name": "copyWorksheet", "description": "复制表格", "comments": "*\n  * 复制表格\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.name 名称\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isCopyBtnName 是否复制按钮名称\n  * @param {boolean} args.isCopyDesc 是否复制描述\n  * @param {boolean} args.isCopyAdmin 是否复制管理员\n  * @param {boolean} args.isCopyRows 是否复制行数据\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 分组id\n  * @param {array} args.relationControlIds 复制的关联控件ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isCopyBtnName", "description": "是否复制按钮名称"}, {"type": "boolean", "name": "args.isCopyDesc", "description": "是否复制描述"}, {"type": "boolean", "name": "args.isCopyAdmin", "description": "是否复制管理员"}, {"type": "boolean", "name": "args.isCopyRows", "description": "是否复制行数据"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "分组id"}, {"type": "array", "name": "args.relationControlIds", "description": "复制的关联控件ID"}]}, {"name": "updateEntityName", "description": "修改表格行记录名", "comments": "*\n  * 修改表格行记录名\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.entityName 记录名\n  * @param {string} args.appID 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.entityName", "description": "记录名"}, {"type": "string", "name": "args.appID", "description": "应用Id"}]}, {"name": "editDeveloperNotes", "description": "修改工作表开发者备注", "comments": "*\n  * 修改工作表开发者备注\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.developerNotes 记录名\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.developerNotes", "description": "记录名"}]}, {"name": "updateWorksheetAlias", "description": "更新 工作表别名", "comments": "*\n  * 更新 工作表别名\n  * @param {Object} args 请求参数\n  * @param {string} args.appId AppId\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.alias 别名\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "AppId"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.alias", "description": "别名"}]}, {"name": "updateWorksheetDec", "description": "修改表格描述", "comments": "*\n  * 修改表格描述\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.dec 描述\n  * @param {string} args.resume\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.dec", "description": "描述"}, {"type": "string", "name": "args.resume", "description": ""}]}, {"name": "updateWorksheetShareRange", "description": "修改表格视图分享范围", "comments": "*\n  * 修改表格视图分享范围\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {string} args.rowId 行Id\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.viewId 视图Id\n  * @param {} args.shareRange\n  * @param {} args.objectType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.rowId", "description": "行Id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}]}, {"name": "getWorksheetInfo", "description": "工作表详情", "comments": "*\n  * 工作表详情\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.relationWorksheetId 关联表的id\n  * @param {boolean} args.getTemplate 是否获取Template\n  * @param {boolean} args.getViews 是否获取Views\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.handleDefault 处理默认值\n  * @param {} args.getControlType\n  * @param {array} args.worksheetIds 批量工作表id\n  * @param {boolean} args.handControlSource 是否处理关联的原始类型\n  * @param {boolean} args.getRules 是否需要验证规则\n  * @param {boolean} args.getSwitchPermit 是否获取功能开关\n  * @param {boolean} args.getRelationSearch 获取查下记录控件\n  * @param {integer} args.resultType 获取类型 0或者1：常规 2：简易模式\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联表的id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取Template"}, {"type": "boolean", "name": "args.getViews", "description": "是否获取Views"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.handleDefault", "description": "处理默认值"}, {"type": "array", "name": "args.worksheetIds", "description": "批量工作表id"}, {"type": "boolean", "name": "args.handControlSource", "description": "是否处理关联的原始类型"}, {"type": "boolean", "name": "args.getRules", "description": "是否需要验证规则"}, {"type": "boolean", "name": "args.getSwitchPermit", "description": "是否获取功能开关"}, {"type": "boolean", "name": "args.getRelationSearch", "description": "获取查下记录控件"}, {"type": "integer", "name": "args.resultType", "description": "获取类型 0或者1：常规 2：简易模式"}]}, {"name": "getWorksheetInfoByWorkItem", "description": "审批、填写获取子表信息及控件权限", "comments": "*\n  * 审批、填写获取子表信息及控件权限\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.relationWorksheetId 关联表的id\n  * @param {boolean} args.getTemplate 是否获取Template\n  * @param {boolean} args.getViews 是否获取Views\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.handleDefault 处理默认值\n  * @param {} args.getControlType\n  * @param {array} args.worksheetIds 批量工作表id\n  * @param {boolean} args.handControlSource 是否处理关联的原始类型\n  * @param {boolean} args.getRules 是否需要验证规则\n  * @param {boolean} args.getSwitchPermit 是否获取功能开关\n  * @param {boolean} args.getRelationSearch 获取查下记录控件\n  * @param {integer} args.resultType 获取类型 0或者1：常规 2：简易模式\n  * @param {string} args.controlId 子表的控件id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {string} args.linkId 工作流填写链接id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联表的id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取Template"}, {"type": "boolean", "name": "args.getViews", "description": "是否获取Views"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.handleDefault", "description": "处理默认值"}, {"type": "array", "name": "args.worksheetIds", "description": "批量工作表id"}, {"type": "boolean", "name": "args.handControlSource", "description": "是否处理关联的原始类型"}, {"type": "boolean", "name": "args.getRules", "description": "是否需要验证规则"}, {"type": "boolean", "name": "args.getSwitchPermit", "description": "是否获取功能开关"}, {"type": "boolean", "name": "args.getRelationSearch", "description": "获取查下记录控件"}, {"type": "integer", "name": "args.resultType", "description": "获取类型 0或者1：常规 2：简易模式"}, {"type": "string", "name": "args.controlId", "description": "子表的控件id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}, {"type": "string", "name": "args.linkId", "description": "工作流填写链接id"}]}, {"name": "getWorksheetShareUrl", "description": "获取工作表分享链接", "comments": "*\n  * 获取工作表分享链接\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.objectType\n  * @param {string} args.rowId 行Id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.password 密码code\n  * @param {string} args.validTime 有效时间\n  * @param {boolean} args.isEdit 是否为编辑,获取url时不传，编辑时传true\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行Id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.password", "description": "密码code"}, {"type": "string", "name": "args.validTime", "description": "有效时间"}, {"type": "boolean", "name": "args.isEdit", "description": "是否为编辑,获取url时不传，编辑时传true"}]}, {"name": "getShareInfoByShareId", "description": "根据shareid得到worksheetid", "comments": "*\n  * 根据shareid得到worksheetid\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.shareId 对外分享标识\n  * @param {string} args.password 密码\n  * @param {string} args.printId 打印模板id\n  * @param {} args.langType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.shareId", "description": "对外分享标识"}, {"type": "string", "name": "args.password", "description": "密码"}, {"type": "string", "name": "args.printId", "description": "打印模板id"}]}, {"name": "getRowByID", "description": "行详情", "comments": "*\n  * 行详情\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {boolean} args.getTemplate 是否获取模板\n  * @param {string} args.shareId 分享页获取关联记录iD\n  * @param {boolean} args.checkView 是否验证视图\n  * @param {string} args.relationWorksheetId 关联控件ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取模板"}, {"type": "string", "name": "args.shareId", "description": "分享页获取关联记录iD"}, {"type": "boolean", "name": "args.checkView", "description": "是否验证视图"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联控件ID"}]}, {"name": "getAttachmentDetail", "description": "获取 附件详情", "comments": "*\n  * 获取 附件详情\n  * @param {Object} args 请求参数\n  * @param {string} args.attachmentShareId 附件分享Id\n  * @param {} args.getType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.attachmentShareId", "description": "附件分享Id"}]}, {"name": "getAttachmentShareId", "description": "获取 附件分享Id", "comments": "*\n  * 获取 附件分享Id\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.rowId 行记录Id\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.fileId 附件Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.rowId", "description": "行记录Id"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.fileId", "description": "附件Id"}]}, {"name": "getRowDetail", "description": "获取记录详情", "comments": "*\n  * 获取记录详情\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {boolean} args.getTemplate 是否获取模板\n  * @param {string} args.shareId 分享页获取关联记录iD\n  * @param {boolean} args.checkView 是否验证视图\n  * @param {string} args.relationWorksheetId 关联控件ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取模板"}, {"type": "string", "name": "args.shareId", "description": "分享页获取关联记录iD"}, {"type": "boolean", "name": "args.checkView", "description": "是否验证视图"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联控件ID"}]}, {"name": "getWorkItem", "description": "根据工作流实例信息获取工作表信息", "comments": "*\n  * 根据工作流实例信息获取工作表信息\n  * @param {Object} args 请求参数\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}]}, {"name": "getRowRelationRows", "description": "获取记录关联记录", "comments": "*\n  * 获取记录关联记录\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {string} args.controlId 控件id\n  * @param {integer} args.pageIndex 页码\n  * @param {integer} args.pageSize 页大小\n  * @param {boolean} args.getWorksheet 是否获取工作表信息\n  * @param {string} args.sortId\n  * @param {boolean} args.isAsc\n  * @param {string} args.shareId 分享ID\n  * @param {string} args.keywords 关键词\n  * @param {string} args.linkId 链接分享id\n  * @param {string} args.viewId\n  * @param {array} args.filterControls\n  * @param {boolean} args.getRules\n  * @param {} args.getType\n  * @param {array} args.fastFilters 快递筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.controlId", "description": "控件id"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "boolean", "name": "args.getWorksheet", "description": "是否获取工作表信息"}, {"type": "string", "name": "args.sortId", "description": ""}, {"type": "boolean", "name": "args.isAsc", "description": ""}, {"type": "string", "name": "args.shareId", "description": "分享ID"}, {"type": "string", "name": "args.keywords", "description": "关键词"}, {"type": "string", "name": "args.linkId", "description": "链接分享id"}, {"type": "string", "name": "args.viewId", "description": ""}, {"type": "array", "name": "args.filterControls", "description": ""}, {"type": "boolean", "name": "args.getRules", "description": ""}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}]}, {"name": "addWorksheetRow", "description": "添加行", "comments": "*\n  * 添加行\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.receiveControls 该行所有的cell\n  * @param {array} args.receiveRows 批量新增所有rows\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.btnId 自定义按钮ID\n  * @param {string} args.btnRemark 按钮备注\n  * @param {string} args.btnWorksheetId 点击按钮对应的工作表ID\n  * @param {string} args.btnRowId 点击按钮对应的行记录ID\n  * @param {} args.masterRecord\n  * @param {string} args.pushUniqueId 推送ID\n  * @param {string} args.verifyCode 验证码【根据配置来校验是否必填】\n  * @param {integer} args.rowStatus 1：正常 21：草稿箱\n  * @param {string} args.draftRowId 草稿ID\n  * @param {string} args.clientId 未登录用户临时登录凭据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.receiveControls", "description": "该行所有的cell"}, {"type": "array", "name": "args.receiveRows", "description": "批量新增所有rows"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.btnId", "description": "自定义按钮ID"}, {"type": "string", "name": "args.btnRemark", "description": "按钮备注"}, {"type": "string", "name": "args.btnWorksheetId", "description": "点击按钮对应的工作表ID"}, {"type": "string", "name": "args.btnRowId", "description": "点击按钮对应的行记录ID"}, {"type": "string", "name": "args.pushUniqueId", "description": "推送ID"}, {"type": "string", "name": "args.verifyCode", "description": "验证码【根据配置来校验是否必填】"}, {"type": "integer", "name": "args.rowStatus", "description": "1：正常 21：草稿箱"}, {"type": "string", "name": "args.draftRowId", "description": "草稿ID"}, {"type": "string", "name": "args.clientId", "description": "未登录用户临时登录凭据"}]}, {"name": "addWSRowsBatch", "description": "批量添加行", "comments": "*\n  * 批量添加行\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.receiveControls 该行所有的cell\n  * @param {array} args.receiveRows 批量新增所有rows\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.btnId 自定义按钮ID\n  * @param {string} args.btnRemark 按钮备注\n  * @param {string} args.btnWorksheetId 点击按钮对应的工作表ID\n  * @param {string} args.btnRowId 点击按钮对应的行记录ID\n  * @param {} args.masterRecord\n  * @param {string} args.pushUniqueId 推送ID\n  * @param {string} args.verifyCode 验证码【根据配置来校验是否必填】\n  * @param {integer} args.rowStatus 1：正常 21：草稿箱\n  * @param {string} args.draftRowId 草稿ID\n  * @param {string} args.clientId 未登录用户临时登录凭据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.receiveControls", "description": "该行所有的cell"}, {"type": "array", "name": "args.receiveRows", "description": "批量新增所有rows"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.btnId", "description": "自定义按钮ID"}, {"type": "string", "name": "args.btnRemark", "description": "按钮备注"}, {"type": "string", "name": "args.btnWorksheetId", "description": "点击按钮对应的工作表ID"}, {"type": "string", "name": "args.btnRowId", "description": "点击按钮对应的行记录ID"}, {"type": "string", "name": "args.pushUniqueId", "description": "推送ID"}, {"type": "string", "name": "args.verifyCode", "description": "验证码【根据配置来校验是否必填】"}, {"type": "integer", "name": "args.rowStatus", "description": "1：正常 21：草稿箱"}, {"type": "string", "name": "args.draftRowId", "description": "草稿ID"}, {"type": "string", "name": "args.clientId", "description": "未登录用户临时登录凭据"}]}, {"name": "updateWorksheetRow", "description": "修改行", "comments": "*\n  * 修改行\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {array} args.newOldControl 要修改的cell\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {string} args.btnId 自定义按钮ID\n  * @param {string} args.btnRemark 按钮备注\n  * @param {string} args.btnWorksheetId 点击按钮对应的工作表ID\n  * @param {string} args.btnRowId 点击按钮对应的行记录ID\n  * @param {string} args.pushUniqueId 推送ID\n  * @param {integer} args.rowStatus 1：正常 11：草稿箱\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "array", "name": "args.newOldControl", "description": "要修改的cell"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}, {"type": "string", "name": "args.btnId", "description": "自定义按钮ID"}, {"type": "string", "name": "args.btnRemark", "description": "按钮备注"}, {"type": "string", "name": "args.btnWorksheetId", "description": "点击按钮对应的工作表ID"}, {"type": "string", "name": "args.btnRowId", "description": "点击按钮对应的行记录ID"}, {"type": "string", "name": "args.pushUniqueId", "description": "推送ID"}, {"type": "integer", "name": "args.rowStatus", "description": "1：正常 11：草稿箱"}]}, {"name": "checkFieldUnique", "description": "验证字段唯一性", "comments": "*\n  * 验证字段唯一性\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.controlId 需要验证的控件id\n  * @param {} args.controlType\n  * @param {string} args.controlValue 新输入的值\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.controlId", "description": "需要验证的控件id"}, {"type": "string", "name": "args.controlValue", "description": "新输入的值"}]}, {"name": "updateWorksheetRows", "description": "批量修改", "comments": "*\n  * 批量修改\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.cells\n  * @param {string} args.viewId 视图Id\n  * @param {array} args.rowIds 行id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.isAll 是否全部\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快递筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup\n  * @param {string} args.btnId 自定义按钮ID\n  * @param {string} args.btnRemark 按钮备注\n  * @param {string} args.btnWorksheetId 点击按钮对应的工作表ID\n  * @param {string} args.btnRowId 点击按钮对应的行记录ID\n  * @param {string} args.pushUniqueId 推送ID\n  * @param {array} args.controls 批量编辑\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "array", "name": "args.rowIds", "description": "行id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.isAll", "description": "是否全部"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": ""}, {"type": "string", "name": "args.btnId", "description": "自定义按钮ID"}, {"type": "string", "name": "args.btnRemark", "description": "按钮备注"}, {"type": "string", "name": "args.btnWorksheetId", "description": "点击按钮对应的工作表ID"}, {"type": "string", "name": "args.btnRowId", "description": "点击按钮对应的行记录ID"}, {"type": "string", "name": "args.pushUniqueId", "description": "推送ID"}, {"type": "array", "name": "args.controls", "description": "批量编辑"}]}, {"name": "updateRowRelationRows", "description": "编辑记录关联记录", "comments": "*\n  * 编辑记录关联记录\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {array} args.rowIds 行ids\n  * @param {boolean} args.isAdd isAdd\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {} args.updateType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "boolean", "name": "args.isAdd", "description": "isAdd"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}]}, {"name": "replaceRowRelationRows", "description": "编辑", "comments": "*\n  * 编辑\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.fromRowId 老的上级RowId\n  * @param {string} args.toRowId 新的上级RowId\n  * @param {array} args.rowIds 行ids\n  * @param {string} args.controlId 关联控件ID\n  * @param {string} args.viewId 视图Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.fromRowId", "description": "老的上级RowId"}, {"type": "string", "name": "args.toRowId", "description": "新的上级RowId"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "string", "name": "args.controlId", "description": "关联控件ID"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}]}, {"name": "refreshSummary", "description": "刷新汇总控件", "comments": "*\n  * 刷新汇总控件\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {array} args.rowIds 行ids\n  * @param {boolean} args.isAdd isAdd\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 流程实例id\n  * @param {string} args.workId 运行节点id\n  * @param {} args.updateType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "boolean", "name": "args.isAdd", "description": "isAdd"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.workId", "description": "运行节点id"}]}, {"name": "refreshWorksheetRows", "description": "批量刷新行记录", "comments": "*\n  * 批量刷新行记录\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.cells\n  * @param {string} args.viewId 视图Id\n  * @param {array} args.rowIds 行id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.isAll 是否全部\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快递筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup\n  * @param {string} args.btnId 自定义按钮ID\n  * @param {string} args.btnRemark 按钮备注\n  * @param {string} args.btnWorksheetId 点击按钮对应的工作表ID\n  * @param {string} args.btnRowId 点击按钮对应的行记录ID\n  * @param {string} args.pushUniqueId 推送ID\n  * @param {array} args.controls 批量编辑\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "array", "name": "args.rowIds", "description": "行id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.isAll", "description": "是否全部"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": ""}, {"type": "string", "name": "args.btnId", "description": "自定义按钮ID"}, {"type": "string", "name": "args.btnRemark", "description": "按钮备注"}, {"type": "string", "name": "args.btnWorksheetId", "description": "点击按钮对应的工作表ID"}, {"type": "string", "name": "args.btnRowId", "description": "点击按钮对应的行记录ID"}, {"type": "string", "name": "args.pushUniqueId", "description": "推送ID"}, {"type": "array", "name": "args.controls", "description": "批量编辑"}]}, {"name": "deleteWorksheetRows", "description": "删除行", "comments": "*\n  * 删除行\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.rowIds 行id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.isAll 是否全选\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快速筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup\n  * @param {boolean} args.thoroughDelete 彻底删除\n  * @param {} args.deleteType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.rowIds", "description": "行id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.isAll", "description": "是否全选"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": ""}, {"type": "boolean", "name": "args.thoroughDelete", "description": "彻底删除"}]}, {"name": "restoreWorksheetRows", "description": "恢复行", "comments": "*\n  * 恢复行\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.rowIds 行ids\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.restoreRelation 恢复关联\n  * @param {string} args.copyRelationControlId\n  * @param {boolean} args.isAll 是否全选\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快速筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.restoreRelation", "description": "恢复关联"}, {"type": "string", "name": "args.copyRelationControlId", "description": ""}, {"type": "boolean", "name": "args.isAll", "description": "是否全选"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}]}, {"name": "removeWorksheetRows", "description": "彻底删除", "comments": "*\n  * 彻底删除\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.rowIds 行ids\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.isAll 是否全选\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快速筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.isAll", "description": "是否全选"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}]}, {"name": "getFilterRows", "description": "过滤查找", "comments": "*\n  * 过滤查找\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.getType\n  * @param {array} args.filterControls 查询列\n  * @param {array} args.fastFilters 快速筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup 筛选组件筛选\n  * @param {array} args.sortControls 排序列\n  * @param {string} args.keyWords 关键词\n  * @param {integer} args.pageSize 页大小\n  * @param {integer} args.pageIndex 页码\n  * @param {} args.searchType\n  * @param {} args.status\n  * @param {boolean} args.isUnRead 是否已读\n  * @param {boolean} args.isGetWorksheet 是否查询工作表的详情\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.relationWorksheetId relationWorksheetId\n  * @param {string} args.relationViewId RelationViewId\n  * @param {string} args.rowId 行id\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.kanbanKey 全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,\n  * @param {integer} args.layer 层级视图加载层数\n  * @param {string} args.beginTime 开始时间 日历视图\n  * @param {string} args.endTime 结束时间 日历视图\n  * @param {integer} args.kanbanSize 页大小\n  * @param {integer} args.kanbanIndex 页码\n  * @param {string} args.formId 公开表单ID\n  * @param {string} args.linkId 填写链接id\n  * @param {string} args.reportId 统计图ID\n  * @param {boolean} args.notGetTotal 不获取总记录数\n  * @param {object} args.requestParams 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterControls", "description": "查询列"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": "筛选组件筛选"}, {"type": "array", "name": "args.sortControls", "description": "排序列"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "关键词"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "boolean", "name": "args.isUnRead", "description": "是否已读"}, {"type": "boolean", "name": "args.isGetWorksheet", "description": "是否查询工作表的详情"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "relationWorksheetId"}, {"type": "string", "name": "args.relationViewId", "description": "RelationViewId"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.kanban<PERSON>", "description": "全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,"}, {"type": "integer", "name": "args.layer", "description": "层级视图加载层数"}, {"type": "string", "name": "args.beginTime", "description": "开始时间 日历视图"}, {"type": "string", "name": "args.endTime", "description": "结束时间 日历视图"}, {"type": "integer", "name": "args.kanbanSize", "description": "页大小"}, {"type": "integer", "name": "args.kanbanIndex", "description": "页码"}, {"type": "string", "name": "args.formId", "description": "公开表单ID"}, {"type": "string", "name": "args.linkId", "description": "填写链接id"}, {"type": "string", "name": "args.reportId", "description": "统计图ID"}, {"type": "boolean", "name": "args.notGetTotal", "description": "不获取总记录数"}, {"type": "object", "name": "args.requestParams", "description": "请求参数"}]}, {"name": "getFilterRowsByQueryDefault", "description": "工作表查询默认值获取", "comments": "*\n  * 工作表查询默认值获取\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.getType\n  * @param {array} args.filterControls 查询列\n  * @param {array} args.fastFilters 快速筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup 筛选组件筛选\n  * @param {array} args.sortControls 排序列\n  * @param {string} args.keyWords 关键词\n  * @param {integer} args.pageSize 页大小\n  * @param {integer} args.pageIndex 页码\n  * @param {} args.searchType\n  * @param {} args.status\n  * @param {boolean} args.isUnRead 是否已读\n  * @param {boolean} args.isGetWorksheet 是否查询工作表的详情\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.relationWorksheetId relationWorksheetId\n  * @param {string} args.relationViewId RelationViewId\n  * @param {string} args.rowId 行id\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.kanbanKey 全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,\n  * @param {integer} args.layer 层级视图加载层数\n  * @param {string} args.beginTime 开始时间 日历视图\n  * @param {string} args.endTime 结束时间 日历视图\n  * @param {integer} args.kanbanSize 页大小\n  * @param {integer} args.kanbanIndex 页码\n  * @param {string} args.formId 公开表单ID\n  * @param {string} args.linkId 填写链接id\n  * @param {string} args.reportId 统计图ID\n  * @param {boolean} args.notGetTotal 不获取总记录数\n  * @param {object} args.requestParams 请求参数\n  * @param {string} args.id 工作表查询id\n  * @param {boolean} args.getAllControls 是否返回所有控件返回值\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterControls", "description": "查询列"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": "筛选组件筛选"}, {"type": "array", "name": "args.sortControls", "description": "排序列"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "关键词"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "boolean", "name": "args.isUnRead", "description": "是否已读"}, {"type": "boolean", "name": "args.isGetWorksheet", "description": "是否查询工作表的详情"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "relationWorksheetId"}, {"type": "string", "name": "args.relationViewId", "description": "RelationViewId"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.kanban<PERSON>", "description": "全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,"}, {"type": "integer", "name": "args.layer", "description": "层级视图加载层数"}, {"type": "string", "name": "args.beginTime", "description": "开始时间 日历视图"}, {"type": "string", "name": "args.endTime", "description": "结束时间 日历视图"}, {"type": "integer", "name": "args.kanbanSize", "description": "页大小"}, {"type": "integer", "name": "args.kanbanIndex", "description": "页码"}, {"type": "string", "name": "args.formId", "description": "公开表单ID"}, {"type": "string", "name": "args.linkId", "description": "填写链接id"}, {"type": "string", "name": "args.reportId", "description": "统计图ID"}, {"type": "boolean", "name": "args.notGetTotal", "description": "不获取总记录数"}, {"type": "object", "name": "args.requestParams", "description": "请求参数"}, {"type": "string", "name": "args.id", "description": "工作表查询id"}, {"type": "boolean", "name": "args.getAllControls", "description": "是否返回所有控件返回值"}]}, {"name": "getFilterRowsTotalNum", "description": "获取行记录总数", "comments": "*\n  * 获取行记录总数\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.worksheetId 工作表id\n  * @param {} args.getType\n  * @param {array} args.filterControls 查询列\n  * @param {array} args.fastFilters 快速筛选\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup 筛选组件筛选\n  * @param {array} args.sortControls 排序列\n  * @param {string} args.keyWords 关键词\n  * @param {integer} args.pageSize 页大小\n  * @param {integer} args.pageIndex 页码\n  * @param {} args.searchType\n  * @param {} args.status\n  * @param {boolean} args.isUnRead 是否已读\n  * @param {boolean} args.isGetWorksheet 是否查询工作表的详情\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.relationWorksheetId relationWorksheetId\n  * @param {string} args.relationViewId RelationViewId\n  * @param {string} args.rowId 行id\n  * @param {string} args.controlId 控件Id\n  * @param {string} args.kanbanKey 全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,\n  * @param {integer} args.layer 层级视图加载层数\n  * @param {string} args.beginTime 开始时间 日历视图\n  * @param {string} args.endTime 结束时间 日历视图\n  * @param {integer} args.kanbanSize 页大小\n  * @param {integer} args.kanbanIndex 页码\n  * @param {string} args.formId 公开表单ID\n  * @param {string} args.linkId 填写链接id\n  * @param {string} args.reportId 统计图ID\n  * @param {boolean} args.notGetTotal 不获取总记录数\n  * @param {object} args.requestParams 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterControls", "description": "查询列"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": "筛选组件筛选"}, {"type": "array", "name": "args.sortControls", "description": "排序列"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "关键词"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "boolean", "name": "args.isUnRead", "description": "是否已读"}, {"type": "boolean", "name": "args.isGetWorksheet", "description": "是否查询工作表的详情"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "relationWorksheetId"}, {"type": "string", "name": "args.relationViewId", "description": "RelationViewId"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "string", "name": "args.controlId", "description": "控件Id"}, {"type": "string", "name": "args.kanban<PERSON>", "description": "全部看板，&#34;-1&#34;:无等于或无选项单看板，&#34;key&#34;:单看板数据,"}, {"type": "integer", "name": "args.layer", "description": "层级视图加载层数"}, {"type": "string", "name": "args.beginTime", "description": "开始时间 日历视图"}, {"type": "string", "name": "args.endTime", "description": "结束时间 日历视图"}, {"type": "integer", "name": "args.kanbanSize", "description": "页大小"}, {"type": "integer", "name": "args.kanbanIndex", "description": "页码"}, {"type": "string", "name": "args.formId", "description": "公开表单ID"}, {"type": "string", "name": "args.linkId", "description": "填写链接id"}, {"type": "string", "name": "args.reportId", "description": "统计图ID"}, {"type": "boolean", "name": "args.notGetTotal", "description": "不获取总记录数"}, {"type": "object", "name": "args.requestParams", "description": "请求参数"}]}, {"name": "getFilterRowsReport", "description": "工作表最下方统计", "comments": "*\n  * 工作表最下方统计\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.filterControls 查询列\n  * @param {array} args.columnRpts 列排序\n  * @param {} args.searchType\n  * @param {string} args.keyWords 关键词\n  * @param {string} args.controlId\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {array} args.fastFilters\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup 筛选组件\n  * @param {object} args.requestParams 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterControls", "description": "查询列"}, {"type": "array", "name": "args.columnRpts", "description": "列排序"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "关键词"}, {"type": "string", "name": "args.controlId", "description": ""}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "array", "name": "args.fastFilters", "description": ""}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": "筛选组件"}, {"type": "object", "name": "args.requestParams", "description": "请求参数"}]}, {"name": "getLogs", "description": "获取日志", "comments": "*\n  * 获取日志\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {integer} args.pageSize 页大小\n  * @param {integer} args.pageIndex 页码\n  * @param {string} args.rowId 行id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "string", "name": "args.rowId", "description": "行id"}]}, {"name": "getWorksheetOperationLogs", "description": "获取工作表操作日志", "comments": "*\n  * 获取工作表操作日志\n  * @param {Object} args 请求参数\n  * @param {integer} args.pageSize 分页大小\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.objectType 日志对象类型 1:工作表 2:行记录 3:视图 4:按钮 5:业务规则 99:其他\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 记录id\n  * @param {array} args.filterUniqueIds 根据唯一码筛选\n  * @param {array} args.controlIds 筛选控件或属性ID\n  * @param {array} args.opeartorIds 筛选操作人\n  * @param {string} args.startDate 开始时间\n  * @param {string} args.endDate 结束时间\n  * @param {string} args.lastMark 最后标记时间\n  * @param {boolean} args.isGlobaLog 是否为全局日志获取记录日志\n  * @param {integer} args.requestType 日志操作类型 1：手动 2：工作流 3：按钮\n  * @param {string} args.archiveId 归档ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.pageSize", "description": "分页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.objectType", "description": "日志对象类型 1:工作表 2:行记录 3:视图 4:按钮 5:业务规则 99:其他"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "记录id"}, {"type": "array", "name": "args.filterUniqueIds", "description": "根据唯一码筛选"}, {"type": "array", "name": "args.controlIds", "description": "筛选控件或属性ID"}, {"type": "array", "name": "args.opeartorIds", "description": "筛选操作人"}, {"type": "string", "name": "args.startDate", "description": "开始时间"}, {"type": "string", "name": "args.endDate", "description": "结束时间"}, {"type": "string", "name": "args.lastMark", "description": "最后标记时间"}, {"type": "boolean", "name": "args.isGlobaLog", "description": "是否为全局日志获取记录日志"}, {"type": "integer", "name": "args.requestType", "description": "日志操作类型 1：手动 2：工作流 3：按钮"}, {"type": "string", "name": "args.archiveId", "description": "归档ID"}]}, {"name": "getDetailTableLog", "description": "获取子表日志详情", "comments": "*\n  * 获取子表日志详情\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行记录id\n  * @param {string} args.uniqueId 唯一id\n  * @param {string} args.createTime 创建时间\n  * @param {} args.log\n  * @param {string} args.lastMark 最后标记时间\n  * @param {integer} args.objectType 对象类型\n  * @param {integer} args.requestType 请求类型\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.archiveId 归档ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行记录id"}, {"type": "string", "name": "args.uniqueId", "description": "唯一id"}, {"type": "string", "name": "args.createTime", "description": "创建时间"}, {"type": "string", "name": "args.lastMark", "description": "最后标记时间"}, {"type": "integer", "name": "args.objectType", "description": "对象类型"}, {"type": "integer", "name": "args.requestType", "description": "请求类型"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.archiveId", "description": "归档ID"}]}, {"name": "batchGetWorksheetOperationLogs", "description": "批量获取工作表日志", "comments": "*\n  * 批量获取工作表日志\n  * @param {Object} args 请求参数\n  * @param {integer} args.pageSize 分页大小\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.objectType 日志对象类型 1:工作表 2:行记录 3:视图 4:按钮 5:业务规则 99:其他\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 记录id\n  * @param {array} args.filterUniqueIds 根据唯一码筛选\n  * @param {array} args.controlIds 筛选控件或属性ID\n  * @param {array} args.opeartorIds 筛选操作人\n  * @param {string} args.startDate 开始时间\n  * @param {string} args.endDate 结束时间\n  * @param {string} args.lastMark 最后标记时间\n  * @param {boolean} args.isGlobaLog 是否为全局日志获取记录日志\n  * @param {integer} args.requestType 日志操作类型 1：手动 2：工作流 3：按钮\n  * @param {string} args.archiveId 归档ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.pageSize", "description": "分页大小"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.objectType", "description": "日志对象类型 1:工作表 2:行记录 3:视图 4:按钮 5:业务规则 99:其他"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "记录id"}, {"type": "array", "name": "args.filterUniqueIds", "description": "根据唯一码筛选"}, {"type": "array", "name": "args.controlIds", "description": "筛选控件或属性ID"}, {"type": "array", "name": "args.opeartorIds", "description": "筛选操作人"}, {"type": "string", "name": "args.startDate", "description": "开始时间"}, {"type": "string", "name": "args.endDate", "description": "结束时间"}, {"type": "string", "name": "args.lastMark", "description": "最后标记时间"}, {"type": "boolean", "name": "args.isGlobaLog", "description": "是否为全局日志获取记录日志"}, {"type": "integer", "name": "args.requestType", "description": "日志操作类型 1：手动 2：工作流 3：按钮"}, {"type": "string", "name": "args.archiveId", "description": "归档ID"}]}, {"name": "updateWorksheetRowShareRange", "description": "工作表记录分享范围修改", "comments": "*\n  * 工作表记录分享范围修改\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.rowId 行id\n  * @param {} args.shareRange\n  * @param {} args.objectType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.rowId", "description": "行id"}]}, {"name": "getRowsShortUrl", "description": "获取记录短链", "comments": "*\n  * 获取记录短链\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.rowIds 行ids\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "copyRow", "description": "复制行记录", "comments": "*\n  * 复制行记录\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.rowIds 行ids\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.restoreRelation 恢复关联\n  * @param {string} args.copyRelationControlId\n  * @param {boolean} args.isAll 是否全选\n  * @param {array} args.excludeRowIds 需要排除的rowIds\n  * @param {array} args.filterControls 筛选条件\n  * @param {string} args.keyWords 搜索关键字\n  * @param {array} args.fastFilters 快速筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.rowIds", "description": "行ids"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.restoreRelation", "description": "恢复关联"}, {"type": "string", "name": "args.copyRelationControlId", "description": ""}, {"type": "boolean", "name": "args.isAll", "description": "是否全选"}, {"type": "array", "name": "args.excludeRowIds", "description": "需要排除的rowIds"}, {"type": "array", "name": "args.filterControls", "description": "筛选条件"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索关键字"}, {"type": "array", "name": "args.fastFilters", "description": "快速筛选"}]}, {"name": "getNavGroup", "description": "获取分组导航", "comments": "*\n  * 获取分组导航\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.filterControls 查询列\n  * @param {array} args.columnRpts 列排序\n  * @param {} args.searchType\n  * @param {string} args.keyWords 关键词\n  * @param {string} args.controlId\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {array} args.fastFilters\n  * @param {array} args.navGroupFilters 导航分组筛选\n  * @param {array} args.filtersGroup 筛选组件\n  * @param {object} args.requestParams 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterControls", "description": "查询列"}, {"type": "array", "name": "args.columnRpts", "description": "列排序"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "关键词"}, {"type": "string", "name": "args.controlId", "description": ""}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "array", "name": "args.fastFilters", "description": ""}, {"type": "array", "name": "args.navGroupFilters", "description": "导航分组筛选"}, {"type": "array", "name": "args.filtersGroup", "description": "筛选组件"}, {"type": "object", "name": "args.requestParams", "description": "请求参数"}]}, {"name": "getWorksheetArchives", "description": "获取工作表归档列表", "comments": "*\n  * 获取工作表归档列表\n  * @param {Object} args 请求参数\n  * @param {integer} args.type 1：行记录日志\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.type", "description": "1：行记录日志"}]}, {"name": "saveWorksheetFilter", "description": "保存筛选器", "comments": "*\n  * 保存筛选器\n  * @param {Object} args 请求参数\n  * @param {string} args.name 筛选器名称\n  * @param {string} args.worksheetId 工作表id\n  * @param {integer} args.type 视图类型 1：个人 2：公共\n  * @param {array} args.items\n  * @param {string} args.filterId 筛选条件编号\n  * @param {string} args.appId 应用Id\n  * @param {integer} args.module 1:工作表 2:统计\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.name", "description": "筛选器名称"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "integer", "name": "args.type", "description": "视图类型 1：个人 2：公共"}, {"type": "array", "name": "args.items", "description": ""}, {"type": "string", "name": "args.filterId", "description": "筛选条件编号"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "integer", "name": "args.module", "description": "1:工作表 2:统计"}]}, {"name": "getWorksheetFilters", "description": "获取可见筛选器", "comments": "*\n  * 获取可见筛选器\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.controlId 控件ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}]}, {"name": "getWorksheetFilterById", "description": "获取筛选器详情", "comments": "*\n  * 获取筛选器详情\n  * @param {Object} args 请求参数\n  * @param {string} args.filterId 筛选器Id\n  * @param {array} args.items FilterSort\n  * @param {string} args.projectId 网络Id\n  * @param {string} args.worksheetId 工作表ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.filterId", "description": "筛选器Id"}, {"type": "array", "name": "args.items", "description": "FilterSort"}, {"type": "string", "name": "args.projectId", "description": "网络Id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表ID"}]}, {"name": "deleteWorksheetFilter", "description": "删除筛选器", "comments": "*\n  * 删除筛选器\n  * @param {Object} args 请求参数\n  * @param {string} args.filterId 筛选器Id\n  * @param {string} args.appId 应用ID\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.filterId", "description": "筛选器Id"}, {"type": "string", "name": "args.appId", "description": "应用ID"}]}, {"name": "sortWorksheetFilters", "description": "筛选器排序", "comments": "*\n  * 筛选器排序\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.filterIds 筛选器Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.filterIds", "description": "筛选器Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "saveWorksheetView", "description": "保存视图", "comments": "*\n  * 保存视图\n  * @param {Object} args 请求参数\n  * @param {string} args.name 视图名称\n  * @param {string} args.worksheetId 工作表Id\n  * @param {string} args.sortCid 排序字段Id\n  * @param {integer} args.sortType 排序类型\n  * @param {integer} args.rowHeight 行高 0：紧凑 1：中等 2：高 3：超高\n  * @param {array} args.controls controls\n  * @param {array} args.filters filters\n  * @param {array} args.fastFilters fastfilters\n  * @param {array} args.moreSort 排序\n  * @param {array} args.navGroup 导航分组\n  * @param {array} args.displayControls 显示字段\n  * @param {array} args.showControls Web显示字段\n  * @param {array} args.controlsSorts 字段排序\n  * @param {array} args.layersName 层级名称\n  * @param {boolean} args.customDisplay 是否配置自定义显示列\n  * @param {string} args.viewId 视图id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.unRead unRead\n  * @param {integer} args.viewType 0:列表 1：看板 2：层级\n  * @param {integer} args.childType 1：单表层级 2：多表层级\n  * @param {string} args.viewControl 视图维度ID(分组ID)\n  * @param {array} args.viewControls 多表层级视图控件\n  * @param {string} args.coverCid 封面字段\n  * @param {integer} args.coverType 0：填满 1：完整显示\n  * @param {boolean} args.showControlName 显示控件名称\n  * @param {object} args.advancedSetting 视图高级配置\n  * @param {array} args.editAttrs 编辑属性\n  * @param {array} args.editAdKeys 编辑AdvancedSetting属性keys\n  * @param {string} args.pluginId 视图插件id\n  * @param {string} args.pluginName 视图插件名称\n  * @param {string} args.pluginIcon 视图插件图标\n  * @param {string} args.pluginIconColor 插件插件图标颜色\n  * @param {integer} args.pluginSource 插件来源\n  * @param {string} args.projectId 组织id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.name", "description": "视图名称"}, {"type": "string", "name": "args.worksheetId", "description": "工作表Id"}, {"type": "string", "name": "args.sortCid", "description": "排序字段Id"}, {"type": "integer", "name": "args.sortType", "description": "排序类型"}, {"type": "integer", "name": "args.row<PERSON><PERSON>ght", "description": "行高 0：紧凑 1：中等 2：高 3：超高"}, {"type": "array", "name": "args.controls", "description": "controls"}, {"type": "array", "name": "args.filters", "description": "filters"}, {"type": "array", "name": "args.fastFilters", "description": "fastfilters"}, {"type": "array", "name": "args.moreSort", "description": "排序"}, {"type": "array", "name": "args.navGroup", "description": "导航分组"}, {"type": "array", "name": "args.displayControls", "description": "显示字段"}, {"type": "array", "name": "args.showControls", "description": "Web显示字段"}, {"type": "array", "name": "args.controlsSorts", "description": "字段排序"}, {"type": "array", "name": "args.layersName", "description": "层级名称"}, {"type": "boolean", "name": "args.customDisplay", "description": "是否配置自定义显示列"}, {"type": "string", "name": "args.viewId", "description": "视图id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.unRead", "description": "unRead"}, {"type": "integer", "name": "args.viewType", "description": "0:列表 1：看板 2：层级"}, {"type": "integer", "name": "args.childType", "description": "1：单表层级 2：多表层级"}, {"type": "string", "name": "args.viewControl", "description": "视图维度ID(分组ID)"}, {"type": "array", "name": "args.viewControls", "description": "多表层级视图控件"}, {"type": "string", "name": "args.coverCid", "description": "封面字段"}, {"type": "integer", "name": "args.coverType", "description": "0：填满 1：完整显示"}, {"type": "boolean", "name": "args.showControlName", "description": "显示控件名称"}, {"type": "object", "name": "args.advancedSetting", "description": "视图高级配置"}, {"type": "array", "name": "args.editAttrs", "description": "编辑属性"}, {"type": "array", "name": "args.editAd<PERSON>eys", "description": "编辑AdvancedSetting属性keys"}, {"type": "string", "name": "args.pluginId", "description": "视图插件id"}, {"type": "string", "name": "args.pluginName", "description": "视图插件名称"}, {"type": "string", "name": "args.pluginIcon", "description": "视图插件图标"}, {"type": "string", "name": "args.pluginIconColor", "description": "插件插件图标颜色"}, {"type": "integer", "name": "args.pluginSource", "description": "插件来源"}, {"type": "string", "name": "args.projectId", "description": "组织id"}]}, {"name": "getWorksheetViews", "description": "获取可见视图", "comments": "*\n  * 获取可见视图\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "deleteWorksheetView", "description": "删除视图", "comments": "*\n  * 删除视图\n  * @param {Object} args 请求参数\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "copyWorksheetView", "description": "获取工作表API", "comments": "*\n  * 获取工作表API\n  * @param {Object} args 请求参数\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "sortWorksheetViews", "description": "视图排序", "comments": "*\n  * 视图排序\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {array} args.viewIds 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "array", "name": "args.viewIds", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "getWorksheetBtns", "description": "获取按钮列表", "comments": "*\n  * 获取按钮列表\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用ID\n  * @param {string} args.viewId 视图ID\n  * @param {string} args.rowId 行记录ID\n  * @param {string} args.worksheetId 工作表ID\n  * @param {string} args.btnId\n  * @param {integer} args.status 状态 1：正常 9：回收站\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.viewId", "description": "视图ID"}, {"type": "string", "name": "args.rowId", "description": "行记录ID"}, {"type": "string", "name": "args.worksheetId", "description": "工作表ID"}, {"type": "string", "name": "args.btnId", "description": ""}, {"type": "integer", "name": "args.status", "description": "状态 1：正常 9：回收站"}]}, {"name": "getWorksheetBtnByID", "description": "获取按钮详情", "comments": "*\n  * 获取按钮详情\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用ID\n  * @param {string} args.viewId 视图ID\n  * @param {string} args.rowId 行记录ID\n  * @param {string} args.worksheetId 工作表ID\n  * @param {string} args.btnId\n  * @param {integer} args.status 状态 1：正常 9：回收站\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.viewId", "description": "视图ID"}, {"type": "string", "name": "args.rowId", "description": "行记录ID"}, {"type": "string", "name": "args.worksheetId", "description": "工作表ID"}, {"type": "string", "name": "args.btnId", "description": ""}, {"type": "integer", "name": "args.status", "description": "状态 1：正常 9：回收站"}]}, {"name": "optionWorksheetBtn", "description": "操作按钮", "comments": "*\n  * 操作按钮\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用iD\n  * @param {string} args.viewId 视图ID\n  * @param {string} args.btnId 按钮ID\n  * @param {string} args.worksheetId 工作表ID\n  * @param {} args.optionType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用iD"}, {"type": "string", "name": "args.viewId", "description": "视图ID"}, {"type": "string", "name": "args.btnId", "description": "按钮ID"}, {"type": "string", "name": "args.worksheetId", "description": "工作表ID"}]}, {"name": "saveWorksheetBtn", "description": "保存按钮", "comments": "*\n  * 保存按钮\n  * @param {Object} args 请求参数\n  * @param {string} args.btnId\n  * @param {string} args.name\n  * @param {string} args.worksheetId\n  * @param {integer} args.showType 1:一直 2：满足筛选条件\n  * @param {array} args.filters 筛选条件\n  * @param {array} args.displayViews 显示视图\n  * @param {integer} args.clickType 1：立即执行 2：二次确认 3：填写\n  * @param {string} args.confirmMsg 确认信息\n  * @param {string} args.sureName 确认按钮\n  * @param {string} args.cancelName 取消按钮\n  * @param {integer} args.writeObject 对象 1：本记录 2：关联记录\n  * @param {integer} args.writeType 类型 1：填写字段 2：新建关联记录\n  * @param {string} args.relationControl 关联记录ID\n  * @param {string} args.addRelationControlId 新建关联记录ID\n  * @param {integer} args.workflowType 1:执行 2：不执行\n  * @param {string} args.workflowId 工作流ID\n  * @param {array} args.writeControls 填写控件 type - 1：只读 2：填写 3：必填\n  * @param {string} args.appId 应用ID\n  * @param {string} args.color 颜色\n  * @param {string} args.icon 图标\n  * @param {string} args.desc 描述\n  * @param {integer} args.isAllView\n  * @param {array} args.editAttrs 编辑属性\n  * @param {boolean} args.verifyPwd\n  * @param {boolean} args.enableConfirm\n  * @param {object} args.advancedSetting\n  * @param {boolean} args.isBatch\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.btnId", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "integer", "name": "args.showType", "description": "1:一直 2：满足筛选条件"}, {"type": "array", "name": "args.filters", "description": "筛选条件"}, {"type": "array", "name": "args.displayViews", "description": "显示视图"}, {"type": "integer", "name": "args.clickType", "description": "1：立即执行 2：二次确认 3：填写"}, {"type": "string", "name": "args.confirmMsg", "description": "确认信息"}, {"type": "string", "name": "args.sure<PERSON>ame", "description": "确认按钮"}, {"type": "string", "name": "args.cancelName", "description": "取消按钮"}, {"type": "integer", "name": "args.writeObject", "description": "对象 1：本记录 2：关联记录"}, {"type": "integer", "name": "args.writeType", "description": "类型 1：填写字段 2：新建关联记录"}, {"type": "string", "name": "args.relationControl", "description": "关联记录ID"}, {"type": "string", "name": "args.addRelationControlId", "description": "新建关联记录ID"}, {"type": "integer", "name": "args.workflowType", "description": "1:执行 2：不执行"}, {"type": "string", "name": "args.workflowId", "description": "工作流ID"}, {"type": "array", "name": "args.writeControls", "description": "填写控件 type - 1：只读 2：填写 3：必填"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.color", "description": "颜色"}, {"type": "string", "name": "args.icon", "description": "图标"}, {"type": "string", "name": "args.desc", "description": "描述"}, {"type": "integer", "name": "args.isAllView", "description": ""}, {"type": "array", "name": "args.editAttrs", "description": "编辑属性"}, {"type": "boolean", "name": "args.verifyPwd", "description": ""}, {"type": "boolean", "name": "args.enableConfirm", "description": ""}, {"type": "object", "name": "args.advancedSetting", "description": ""}, {"type": "boolean", "name": "args.isBatch", "description": ""}]}, {"name": "copyWorksheetBtn", "description": "复制按钮", "comments": "*\n  * 复制按钮\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用iD\n  * @param {string} args.viewId 视图ID\n  * @param {string} args.btnId 按钮ID\n  * @param {string} args.worksheetId 工作表ID\n  * @param {} args.optionType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用iD"}, {"type": "string", "name": "args.viewId", "description": "视图ID"}, {"type": "string", "name": "args.btnId", "description": "按钮ID"}, {"type": "string", "name": "args.worksheetId", "description": "工作表ID"}]}, {"name": "getControlRules", "description": "获取规则列表", "comments": "*\n  * 获取规则列表\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.ruleId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.ruleId", "description": ""}]}, {"name": "saveControlRule", "description": "保存规则", "comments": "*\n  * 保存规则\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.ruleId\n  * @param {array} args.ruleIds\n  * @param {string} args.name\n  * @param {boolean} args.disabled\n  * @param {array} args.filters\n  * @param {array} args.ruleItems\n  * @param {array} args.editAttrs\n  * @param {integer} args.type 0:交互  1：验证 2：锁定\n  * @param {integer} args.checkType 0：前端  1：前后端\n  * @param {integer} args.hintType 0：输入和提交 1：仅提交\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.ruleId", "description": ""}, {"type": "array", "name": "args.ruleIds", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.disabled", "description": ""}, {"type": "array", "name": "args.filters", "description": ""}, {"type": "array", "name": "args.ruleItems", "description": ""}, {"type": "array", "name": "args.editAttrs", "description": ""}, {"type": "integer", "name": "args.type", "description": "0:交互  1：验证 2：锁定"}, {"type": "integer", "name": "args.checkType", "description": "0：前端  1：前后端"}, {"type": "integer", "name": "args.hintType", "description": "0：输入和提交 1：仅提交"}]}, {"name": "sortControlRules", "description": "@param {Object} args 请求参数", "comments": "*\n  * \n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.ruleId\n  * @param {array} args.ruleIds\n  * @param {string} args.name\n  * @param {boolean} args.disabled\n  * @param {array} args.filters\n  * @param {array} args.ruleItems\n  * @param {array} args.editAttrs\n  * @param {integer} args.type 0:交互  1：验证 2：锁定\n  * @param {integer} args.checkType 0：前端  1：前后端\n  * @param {integer} args.hintType 0：输入和提交 1：仅提交\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.ruleId", "description": ""}, {"type": "array", "name": "args.ruleIds", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "boolean", "name": "args.disabled", "description": ""}, {"type": "array", "name": "args.filters", "description": ""}, {"type": "array", "name": "args.ruleItems", "description": ""}, {"type": "array", "name": "args.editAttrs", "description": ""}, {"type": "integer", "name": "args.type", "description": "0:交互  1：验证 2：锁定"}, {"type": "integer", "name": "args.checkType", "description": "0：前端  1：前后端"}, {"type": "integer", "name": "args.hintType", "description": "0：输入和提交 1：仅提交"}]}, {"name": "saveWorksheetControls", "description": "保存表控件", "comments": "*\n  * 保存表控件\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "addWorksheetControls", "description": "添加表控件", "comments": "*\n  * 添加表控件\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "getWorksheetControls", "description": "获取表控件", "comments": "*\n  * 获取表控件\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.relationWorksheetId 关联表的id\n  * @param {boolean} args.getTemplate 是否获取Template\n  * @param {boolean} args.getViews 是否获取Views\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.handleDefault 处理默认值\n  * @param {} args.getControlType\n  * @param {array} args.worksheetIds 批量工作表id\n  * @param {boolean} args.handControlSource 是否处理关联的原始类型\n  * @param {boolean} args.getRules 是否需要验证规则\n  * @param {boolean} args.getSwitchPermit 是否获取功能开关\n  * @param {boolean} args.getRelationSearch 获取查下记录控件\n  * @param {integer} args.resultType 获取类型 0或者1：常规 2：简易模式\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联表的id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取Template"}, {"type": "boolean", "name": "args.getViews", "description": "是否获取Views"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.handleDefault", "description": "处理默认值"}, {"type": "array", "name": "args.worksheetIds", "description": "批量工作表id"}, {"type": "boolean", "name": "args.handControlSource", "description": "是否处理关联的原始类型"}, {"type": "boolean", "name": "args.getRules", "description": "是否需要验证规则"}, {"type": "boolean", "name": "args.getSwitchPermit", "description": "是否获取功能开关"}, {"type": "boolean", "name": "args.getRelationSearch", "description": "获取查下记录控件"}, {"type": "integer", "name": "args.resultType", "description": "获取类型 0或者1：常规 2：简易模式"}]}, {"name": "getAiFieldRecommendation", "description": "获取工作表字段智能建议", "comments": "*\n  * 获取工作表字段智能建议\n  * @param {Object} args 请求参数\n  * @param {string} args.prompt 提示词\n  * @param {} args.lang\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.prompt", "description": "提示词"}]}, {"name": "getWorksheetsControls", "description": "批量获取表控件", "comments": "*\n  * 批量获取表控件\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.relationWorksheetId 关联表的id\n  * @param {boolean} args.getTemplate 是否获取Template\n  * @param {boolean} args.getViews 是否获取Views\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.handleDefault 处理默认值\n  * @param {} args.getControlType\n  * @param {array} args.worksheetIds 批量工作表id\n  * @param {boolean} args.handControlSource 是否处理关联的原始类型\n  * @param {boolean} args.getRules 是否需要验证规则\n  * @param {boolean} args.getSwitchPermit 是否获取功能开关\n  * @param {boolean} args.getRelationSearch 获取查下记录控件\n  * @param {integer} args.resultType 获取类型 0或者1：常规 2：简易模式\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联表的id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取Template"}, {"type": "boolean", "name": "args.getViews", "description": "是否获取Views"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.handleDefault", "description": "处理默认值"}, {"type": "array", "name": "args.worksheetIds", "description": "批量工作表id"}, {"type": "boolean", "name": "args.handControlSource", "description": "是否处理关联的原始类型"}, {"type": "boolean", "name": "args.getRules", "description": "是否需要验证规则"}, {"type": "boolean", "name": "args.getSwitchPermit", "description": "是否获取功能开关"}, {"type": "boolean", "name": "args.getRelationSearch", "description": "获取查下记录控件"}, {"type": "integer", "name": "args.resultType", "description": "获取类型 0或者1：常规 2：简易模式"}]}, {"name": "editControlsAlias", "description": "编辑控件别名", "comments": "*\n  * 编辑控件别名\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "editGenerateControlsDefaultAlias", "description": "生成控件默认别名", "comments": "*\n  * 生成控件默认别名\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.worksheetId 工作表id\n  * @param {integer} args.version 版本号\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "integer", "name": "args.version", "description": "版本号"}]}, {"name": "editWorksheetControls", "description": "保存表控件", "comments": "*\n  * 保存表控件\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "resetControlIncrease", "description": "重置自动编号", "comments": "*\n  * 重置自动编号\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "deleteWorksheetAutoID", "description": "删除autoid", "comments": "*\n  * 删除autoid\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.relationWorksheetId 关联表的id\n  * @param {boolean} args.getTemplate 是否获取Template\n  * @param {boolean} args.getViews 是否获取Views\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.handleDefault 处理默认值\n  * @param {} args.getControlType\n  * @param {array} args.worksheetIds 批量工作表id\n  * @param {boolean} args.handControlSource 是否处理关联的原始类型\n  * @param {boolean} args.getRules 是否需要验证规则\n  * @param {boolean} args.getSwitchPermit 是否获取功能开关\n  * @param {boolean} args.getRelationSearch 获取查下记录控件\n  * @param {integer} args.resultType 获取类型 0或者1：常规 2：简易模式\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.relationWorksheetId", "description": "关联表的id"}, {"type": "boolean", "name": "args.getTemplate", "description": "是否获取Template"}, {"type": "boolean", "name": "args.getViews", "description": "是否获取Views"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.handleDefault", "description": "处理默认值"}, {"type": "array", "name": "args.worksheetIds", "description": "批量工作表id"}, {"type": "boolean", "name": "args.handControlSource", "description": "是否处理关联的原始类型"}, {"type": "boolean", "name": "args.getRules", "description": "是否需要验证规则"}, {"type": "boolean", "name": "args.getSwitchPermit", "description": "是否获取功能开关"}, {"type": "boolean", "name": "args.getRelationSearch", "description": "获取查下记录控件"}, {"type": "integer", "name": "args.resultType", "description": "获取类型 0或者1：常规 2：简易模式"}]}, {"name": "editControlsStatus", "description": "编辑控件状态", "comments": "*\n  * 编辑控件状态\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 兼容老数据\n  * @param {string} args.worksheetId WorksheetId\n  * @param {integer} args.version 版本号\n  * @param {array} args.controls 控件集合\n  * @param {string} args.appId 应用ID\n  * @param {string} args.controlId 控件ID\n  * @param {array} args.controlIds 控件IDs\n  * @param {integer} args.status 状态 1:恢复 999：彻底删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "兼容老数据"}, {"type": "string", "name": "args.worksheetId", "description": "WorksheetId"}, {"type": "integer", "name": "args.version", "description": "版本号"}, {"type": "array", "name": "args.controls", "description": "控件集合"}, {"type": "string", "name": "args.appId", "description": "应用ID"}, {"type": "string", "name": "args.controlId", "description": "控件ID"}, {"type": "array", "name": "args.controlIds", "description": "控件IDs"}, {"type": "integer", "name": "args.status", "description": "状态 1:恢复 999：彻底删除"}]}, {"name": "getPrintList", "description": "获取系统打印列表", "comments": "*\n  * 获取系统打印列表\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId\n  * @param {string} args.viewId\n  * @param {array} args.rowIds\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.viewId", "description": ""}, {"type": "array", "name": "args.rowIds", "description": ""}]}, {"name": "getFormComponent", "description": "获取 表单组件", "comments": "*\n  * 获取 表单组件\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 工作表Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "工作表Id"}]}, {"name": "getPrint", "description": "获取单个打印模板", "comments": "*\n  * 获取单个打印模板\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.projectId\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {integer} args.pageIndex 页码\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.sortId\n  * @param {boolean} args.isAsc\n  * @param {string} args.keywords 关键词\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 通过工作流审批打印时必传\n  * @param {string} args.workId 通过工作流审批打印时必传\n  * @param {array} args.filterControls\n  * @param {array} args.fastFilters 快递筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.sortId", "description": ""}, {"type": "boolean", "name": "args.isAsc", "description": ""}, {"type": "string", "name": "args.keywords", "description": "关键词"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "通过工作流审批打印时必传"}, {"type": "string", "name": "args.workId", "description": "通过工作流审批打印时必传"}, {"type": "array", "name": "args.filterControls", "description": ""}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}]}, {"name": "getCodePrint", "description": "获取单个打印模板", "comments": "*\n  * 获取单个打印模板\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.projectId\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {integer} args.pageIndex 页码\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.sortId\n  * @param {boolean} args.isAsc\n  * @param {string} args.keywords 关键词\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 通过工作流审批打印时必传\n  * @param {string} args.workId 通过工作流审批打印时必传\n  * @param {array} args.filterControls\n  * @param {array} args.fastFilters 快递筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.sortId", "description": ""}, {"type": "boolean", "name": "args.isAsc", "description": ""}, {"type": "string", "name": "args.keywords", "description": "关键词"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "通过工作流审批打印时必传"}, {"type": "string", "name": "args.workId", "description": "通过工作流审批打印时必传"}, {"type": "array", "name": "args.filterControls", "description": ""}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}]}, {"name": "getPrintTemplate", "description": "新建生成打印模板", "comments": "*\n  * 新建生成打印模板\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.projectId\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.rowId 行id\n  * @param {integer} args.pageIndex 页码\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.sortId\n  * @param {boolean} args.isAsc\n  * @param {string} args.keywords 关键词\n  * @param {} args.getType\n  * @param {string} args.viewId 视图Id\n  * @param {string} args.appId 应用Id\n  * @param {string} args.instanceId 通过工作流审批打印时必传\n  * @param {string} args.workId 通过工作流审批打印时必传\n  * @param {array} args.filterControls\n  * @param {array} args.fastFilters 快递筛选\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.rowId", "description": "行id"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.sortId", "description": ""}, {"type": "boolean", "name": "args.isAsc", "description": ""}, {"type": "string", "name": "args.keywords", "description": "关键词"}, {"type": "string", "name": "args.viewId", "description": "视图Id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.instanceId", "description": "通过工作流审批打印时必传"}, {"type": "string", "name": "args.workId", "description": "通过工作流审批打印时必传"}, {"type": "array", "name": "args.filterControls", "description": ""}, {"type": "array", "name": "args.fastFilters", "description": "快递筛选"}]}, {"name": "editPrint", "description": "保存系统打印模板", "comments": "*\n  * 保存系统打印模板\n  * @param {Object} args 请求参数\n  * @param {string} args.id 模板id (空=新建 非空=修改)\n  * @param {} args.data\n  * @param {array} args.saveControls 勾选保存的控件\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "模板id (空=新建 非空=修改)"}, {"type": "array", "name": "args.saveControls", "description": "勾选保存的控件"}]}, {"name": "saveRecordCodePrintConfig", "description": "保存记录二维码打印模板配置", "comments": "*\n  * 保存记录二维码打印模板配置\n  * @param {Object} args 请求参数\n  * @param {string} args.id 模板id\n  * @param {string} args.projectId 组织id\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.name 模板名称\n  * @param {integer} args.type 3-二维码打印 4-条码打印\n  * @param {integer} args.range 使用范围\n  * @param {array} args.views 视图id\n  * @param {} args.config\n  * @param {array} args.advanceSettings 额外配置\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "模板id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.name", "description": "模板名称"}, {"type": "integer", "name": "args.type", "description": "3-二维码打印 4-条码打印"}, {"type": "integer", "name": "args.range", "description": "使用范围"}, {"type": "array", "name": "args.views", "description": "视图id"}, {"type": "array", "name": "args.advanceSettings", "description": "额外配置"}]}, {"name": "editPrintName", "description": "修改打印模板名称", "comments": "*\n  * 修改打印模板名称\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.name\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.name", "description": ""}]}, {"name": "editPrintRange", "description": "修改打印模板范围", "comments": "*\n  * 修改打印模板范围\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {} args.range\n  * @param {array} args.viewsIds 视图Ids\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "array", "name": "args.viewsIds", "description": "视图Ids"}]}]}, {"name": "appManagement", "description": "应用管理", "data": [{"name": "restoreData", "description": "还原数据", "comments": "*\n  * 还原数据\n  * @param {Object} args 请求参数\n  * @param {string} args.id 任务id\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appId 应用id\n  * @param {string} args.fileUrl 文件链接\n  * @param {string} args.fileName 文件名称\n  * @param {boolean} args.backupCurrentVersion 备份当前版本\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "任务id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.fileUrl", "description": "文件链接"}, {"type": "string", "name": "args.fileName", "description": "文件名称"}, {"type": "boolean", "name": "args.backupCurrentVersion", "description": "备份当前版本"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}, {"name": "backup", "description": "备份应用", "comments": "*\n  * 备份应用\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.containData 是否备份数据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.containData", "description": "是否备份数据"}]}, {"name": "checkRestoreFile", "description": "校验还原文件", "comments": "*\n  * 校验还原文件\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.fileUrl 文件url\n  * @param {string} args.fileName 文件名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.fileUrl", "description": "文件url"}, {"type": "string", "name": "args.fileName", "description": "文件名称"}]}, {"name": "getTarTaskInfo", "description": "获取tar文件上传状态", "comments": "*\n  * 获取tar文件上传状态\n  * @param {Object} args 请求参数\n  * @param {string} args.id 任务id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "任务id"}]}, {"name": "allUsageOverviewStatistics", "description": "使用情况统计分析", "comments": "*\n  * 使用情况统计分析\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.departmentId 部门id\n  * @param {boolean} args.depFlag true表示仅当强部门，false表示部门树\n  * @param {string} args.appId 应用id\n  * @param {integer} args.dayRange 天数范围 0 = 最近7天，1 = 最近一个月，2=最近一个季度，3=最近半年，4=最近一年\n  * @param {string} args.dateDemension &#34;1h&#34;:1小时 &#34;1d&#34;:1天 &#34;1w&#34;:1周 &#34;1M&#34;:1月 &#34;1q&#34;:1季度 &#34;1y&#34;:1年\n  * @param {boolean} args.isApp 表示是否是应用的使用分析\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.departmentId", "description": "部门id"}, {"type": "boolean", "name": "args.depFlag", "description": "true表示仅当强部门，false表示部门树"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.day<PERSON><PERSON>e", "description": "天数范围 0 = 最近7天，1 = 最近一个月，2=最近一个季度，3=最近半年，4=最近一年"}, {"type": "string", "name": "args.dateDemension", "description": "&#34;1h&#34;:1小时 &#34;1d&#34;:1天 &#34;1w&#34;:1周 &#34;1M&#34;:1月 &#34;1q&#34;:1季度 &#34;1y&#34;:1年"}, {"type": "boolean", "name": "args.isApp", "description": "表示是否是应用的使用分析"}]}, {"name": "appUsageOverviewStatistics", "description": "应用汇总概览", "comments": "*\n  * 应用汇总概览\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.keyWord 关键字搜索\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.sortFiled 排序字段\n  * @param {boolean} args.sorted 排序方式 true--asc false--desc\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.key<PERSON>ord", "description": "关键字搜索"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.sortFiled", "description": "排序字段"}, {"type": "boolean", "name": "args.sorted", "description": "排序方式 true--asc false--desc"}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "usageStatisticsForDimension", "description": "不同维度使用情况统计(按应用，按成员)", "comments": "*\n  * 不同维度使用情况统计(按应用，按成员)\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {integer} args.dayRange 天数范围 0 = 最近7天，1 = 最近一个月，2=最近一个季度，3=最近半年，4=最近一年\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {integer} args.dimension 维度 1-应用 2-用户\n  * @param {string} args.sortFiled 排序字段（返回结果的列名，例如:appAccess）\n  * @param {boolean} args.sorted 排序方式\n  * @param {string} args.keyword 关键词查询\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "integer", "name": "args.day<PERSON><PERSON>e", "description": "天数范围 0 = 最近7天，1 = 最近一个月，2=最近一个季度，3=最近半年，4=最近一年"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "integer", "name": "args.dimension", "description": "维度 1-应用 2-用户"}, {"type": "string", "name": "args.sortFiled", "description": "排序字段（返回结果的列名，例如:appAccess）"}, {"type": "boolean", "name": "args.sorted", "description": "排序方式"}, {"type": "string", "name": "args.keyword", "description": "关键词查询"}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "getGlobalLogs", "description": "获取应用日志", "comments": "*\n  * 获取应用日志\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {} args.queryType\n  * @param {array} args.operators 操作人id数组\n  * @param {array} args.appIds 应用id数组\n  * @param {array} args.worksheetIds 工作表id数组\n  * @param {array} args.modules 所属日志模块\n  * @param {array} args.operationTypes 操作类型\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {array} args.columnNames 列名称\n  * @param {string} args.menuName 菜单名称\n  * @param {string} args.startDateTime 开始时间\n  * @param {string} args.endDateTime 结束时间\n  * @param {boolean} args.confirmExport 是否确认导出(超量的情况下传)\n  * @param {boolean} args.isSingle 是否是单个应用\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "array", "name": "args.operators", "description": "操作人id数组"}, {"type": "array", "name": "args.appIds", "description": "应用id数组"}, {"type": "array", "name": "args.worksheetIds", "description": "工作表id数组"}, {"type": "array", "name": "args.modules", "description": "所属日志模块"}, {"type": "array", "name": "args.operationTypes", "description": "操作类型"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "array", "name": "args.columnNames", "description": "列名称"}, {"type": "string", "name": "args.menuName", "description": "菜单名称"}, {"type": "string", "name": "args.startDateTime", "description": "开始时间"}, {"type": "string", "name": "args.endDateTime", "description": "结束时间"}, {"type": "boolean", "name": "args.confirmExport", "description": "是否确认导出(超量的情况下传)"}, {"type": "boolean", "name": "args.isSingle", "description": "是否是单个应用"}]}, {"name": "getWorksheetsUnderTheApp", "description": "获取应用下工作表信息", "comments": "*\n  * 获取应用下工作表信息\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {array} args.appIds 应用ids\n  * @param {boolean} args.isFilterCustomPage 是否过滤自定义页面\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "array", "name": "args.appIds", "description": "应用ids"}, {"type": "boolean", "name": "args.isFilterCustomPage", "description": "是否过滤自定义页面"}]}, {"name": "addLock", "description": "开启密码锁", "comments": "*\n  * 开启密码锁\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId\n  * @param {string} args.password\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.password", "description": ""}]}, {"name": "unlock", "description": "map解锁", "comments": "*\n  * map解锁\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId\n  * @param {string} args.password\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.password", "description": ""}]}, {"name": "editLockPassword", "description": "修改锁定密码", "comments": "*\n  * 修改锁定密码\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId\n  * @param {string} args.password\n  * @param {string} args.newPassword\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.password", "description": ""}, {"type": "string", "name": "args.newPassword", "description": ""}]}, {"name": "resetLock", "description": "重新锁定", "comments": "*\n  * 重新锁定\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId 应用id\n  * @param {boolean} args.getSection 是否获取分组信息\n  * @param {boolean} args.getManager 是否获取管理员列表信息\n  * @param {boolean} args.getProject 获取组织信息\n  * @param {boolean} args.getLang 是否获取应用语种信息\n  * @param {boolean} args.isMobile 是否是移动端\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.getSection", "description": "是否获取分组信息"}, {"type": "boolean", "name": "args.getManager", "description": "是否获取管理员列表信息"}, {"type": "boolean", "name": "args.getProject", "description": "获取组织信息"}, {"type": "boolean", "name": "args.getLang", "description": "是否获取应用语种信息"}, {"type": "boolean", "name": "args.isMobile", "description": "是否是移动端"}]}, {"name": "closeLock", "description": "关闭应用锁", "comments": "*\n  * 关闭应用锁\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId 应用id\n  * @param {boolean} args.getSection 是否获取分组信息\n  * @param {boolean} args.getManager 是否获取管理员列表信息\n  * @param {boolean} args.getProject 获取组织信息\n  * @param {boolean} args.getLang 是否获取应用语种信息\n  * @param {boolean} args.isMobile 是否是移动端\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.getSection", "description": "是否获取分组信息"}, {"type": "boolean", "name": "args.getManager", "description": "是否获取管理员列表信息"}, {"type": "boolean", "name": "args.getProject", "description": "获取组织信息"}, {"type": "boolean", "name": "args.getLang", "description": "是否获取应用语种信息"}, {"type": "boolean", "name": "args.isMobile", "description": "是否是移动端"}]}, {"name": "checkUpgrade", "description": "校验升级文件", "comments": "*\n  * 校验升级文件\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.url 文件url\n  * @param {string} args.password 密码\n  * @param {string} args.fileName 文件名\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.url", "description": "文件url"}, {"type": "string", "name": "args.password", "description": "密码"}, {"type": "string", "name": "args.fileName", "description": "文件名"}]}, {"name": "getWorksheetUpgrade", "description": "获取表升级详情", "comments": "*\n  * 获取表升级详情\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.worksheetId 工作表id\n  * @param {string} args.appId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "string", "name": "args.appId", "description": ""}]}, {"name": "upgrade", "description": "升级", "comments": "*\n  * 升级\n  * @param {Object} args 请求参数\n  * @param {string} args.id 批次id\n  * @param {string} args.appId 应用id\n  * @param {string} args.url 导入文件链接（不带token的）\n  * @param {array} args.worksheets 勾选的升级的表\n  * @param {array} args.workflows 勾选升级的流\n  * @param {array} args.pages 勾选升级的页面\n  * @param {array} args.roles 勾选升级的角色\n  * @param {boolean} args.backupCurrentVersion 备份当前版本\n  * @param {boolean} args.matchOffice 是否匹配用户\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "批次id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.url", "description": "导入文件链接（不带token的）"}, {"type": "array", "name": "args.worksheets", "description": "勾选的升级的表"}, {"type": "array", "name": "args.workflows", "description": "勾选升级的流"}, {"type": "array", "name": "args.pages", "description": "勾选升级的页面"}, {"type": "array", "name": "args.roles", "description": "勾选升级的角色"}, {"type": "boolean", "name": "args.backupCurrentVersion", "description": "备份当前版本"}, {"type": "boolean", "name": "args.matchOffice", "description": "是否匹配用户"}]}, {"name": "getUpgradeLogs", "description": "获取应用升级记录", "comments": "*\n  * 获取应用升级记录\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}]}, {"name": "getMdyInfo", "description": "获取mdy文件相关密码", "comments": "*\n  * 获取mdy文件相关密码\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.url 文件url不带token\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.url", "description": "文件url不带token"}]}, {"name": "getAppLangs", "description": "获取应用语种列表", "comments": "*\n  * 获取应用语种列表\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "createAppLang", "description": "创建应用语言", "comments": "*\n  * 创建应用语言\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {array} args.langTypes\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "array", "name": "args.langTypes", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "deleteAppLang", "description": "删除应用语言", "comments": "*\n  * 删除应用语言\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.id\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "getAppLangDetail", "description": "获取应用语言详情", "comments": "*\n  * 获取应用语言详情\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.appLangId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.appLangId", "description": ""}]}, {"name": "editAppLang", "description": "编辑应用语言详情", "comments": "*\n  * 编辑应用语言详情\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.langId\n  * @param {string} args.id\n  * @param {string} args.parentId\n  * @param {string} args.correlationId\n  * @param {} args.type\n  * @param {object} args.data\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.langId", "description": ""}, {"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.parentId", "description": ""}, {"type": "string", "name": "args.correlationId", "description": ""}, {"type": "object", "name": "args.data", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "machineTranslation", "description": "机器翻译", "comments": "*\n  * 机器翻译\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.comparisonLangId\n  * @param {string} args.targetLangId\n  * @param {} args.fillType\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.comparisonLangId", "description": ""}, {"type": "string", "name": "args.targetLangId", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "getAppStructureForER", "description": "@param {Object} args 请求参数", "comments": "*\n  * \n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "getProjectLangs", "description": "获取组织语言", "comments": "*\n  * 获取组织语言\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {array} args.correlationIds 业务模块id（不需要筛选业务，不传就行）\n  * @param {integer} args.type 业务模块，0 = 组织名称，20 = 应用分组（不需要筛选业务，不传就行）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "array", "name": "args.correlationIds", "description": "业务模块id（不需要筛选业务，不传就行）"}, {"type": "integer", "name": "args.type", "description": "业务模块，0 = 组织名称，20 = 应用分组（不需要筛选业务，不传就行）"}]}, {"name": "getsByProjectIds", "description": "批量获取业务类型组织语言", "comments": "*\n  * 批量获取业务类型组织语言\n  * @param {Object} args 请求参数\n  * @param {array} args.projectIds\n  * @param {integer} args.type 业务模块，0 = 组织名称，20 = 应用分组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.projectIds", "description": ""}, {"type": "integer", "name": "args.type", "description": "业务模块，0 = 组织名称，20 = 应用分组"}]}, {"name": "editProjectLangs", "description": "编辑组织语言", "comments": "*\n  * 编辑组织语言\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.correlationId 业务模块id\n  * @param {integer} args.type 业务模块，0 = 组织名称，20 = 应用分组\n  * @param {array} args.data 翻译数据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.correlationId", "description": "业务模块id"}, {"type": "integer", "name": "args.type", "description": "业务模块，0 = 组织名称，20 = 应用分组"}, {"type": "array", "name": "args.data", "description": "翻译数据"}]}, {"name": "getProjectLang", "description": "获取组织名称多语言(只能获取名称)", "comments": "*\n  * 获取组织名称多语言(只能获取名称)\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "addRole", "description": "添加角色", "comments": "*\n  * 添加角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.name 名称\n  * @param {boolean} args.hideAppForMembers 该角色成员不可见当前应用\n  * @param {string} args.description 描述\n  * @param {integer} args.permissionWay 角色类型（0:自定义、10:只读、50::成员、100:管理员）\n  * @param {string} args.projectId 网络id\n  * @param {array} args.sheets 工作表权限集合\n  * @param {array} args.userIds 角色成员id集合\n  * @param {array} args.pages 自定义页面\n  * @param {array} args.extendAttrs 用户扩展权限字段\n  * @param {} args.generalAdd\n  * @param {} args.gneralShare\n  * @param {} args.generalImport\n  * @param {} args.generalExport\n  * @param {} args.generalDiscussion\n  * @param {} args.generalSystemPrinting\n  * @param {} args.generalAttachmentDownload\n  * @param {} args.generalLogging\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "boolean", "name": "args.hideAppForMembers", "description": "该角色成员不可见当前应用"}, {"type": "string", "name": "args.description", "description": "描述"}, {"type": "integer", "name": "args.<PERSON><PERSON>ay", "description": "角色类型（0:自定义、10:只读、50::成员、100:管理员）"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "array", "name": "args.sheets", "description": "工作表权限集合"}, {"type": "array", "name": "args.userIds", "description": "角色成员id集合"}, {"type": "array", "name": "args.pages", "description": "自定义页面"}, {"type": "array", "name": "args.extendAttrs", "description": "用户扩展权限字段"}]}, {"name": "removeRole", "description": "删除角色(并把人员移动到其他角色)", "comments": "*\n  * 删除角色(并把人员移动到其他角色)\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {string} args.resultRoleId 目标角色id\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "string", "name": "args.resultRoleId", "description": "目标角色id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "addRoleMembers", "description": "添加角色成员", "comments": "*\n  * 添加角色成员\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {array} args.userIds 用户\n  * @param {array} args.departmentIds 部门\n  * @param {array} args.departmentTreeIds 部门树\n  * @param {array} args.projectOrganizeIds 网络角色\n  * @param {array} args.jobIds 职位ids\n  * @param {string} args.projectId 网络id\n  * @param {} args.enableGeneralAdd\n  * @param {} args.enableGneralShare\n  * @param {} args.enableGeneralImport\n  * @param {} args.enableGeneralExport\n  * @param {} args.enableGeneralDiscussion\n  * @param {} args.enableGeneralSystemPrinting\n  * @param {} args.enableGeneralAttachmentDownload\n  * @param {} args.enableGeneralLogging\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "array", "name": "args.userIds", "description": "用户"}, {"type": "array", "name": "args.departmentIds", "description": "部门"}, {"type": "array", "name": "args.departmentTreeIds", "description": "部门树"}, {"type": "array", "name": "args.projectOrganizeIds", "description": "网络角色"}, {"type": "array", "name": "args.jobIds", "description": "职位ids"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "removeRoleMembers", "description": "移除角色成员", "comments": "*\n  * 移除角色成员\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {boolean} args.selectAll 是否全选\n  * @param {array} args.userIds 用户\n  * @param {array} args.departmentIds 部门\n  * @param {array} args.jobIds 职位\n  * @param {array} args.departmentTreeIds 部门树\n  * @param {array} args.projectOrganizeIds 网络角色\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "boolean", "name": "args.selectAll", "description": "是否全选"}, {"type": "array", "name": "args.userIds", "description": "用户"}, {"type": "array", "name": "args.departmentIds", "description": "部门"}, {"type": "array", "name": "args.jobIds", "description": "职位"}, {"type": "array", "name": "args.departmentTreeIds", "description": "部门树"}, {"type": "array", "name": "args.projectOrganizeIds", "description": "网络角色"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "设置 角色负责人", "comments": "*\n  * 设置 角色负责人\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {string} args.projectId 网络id\n  * @param {string} args.memberId 成员Id（用户Id、部门Id、部门树的部门Id、职位Id、组织角色Id、全组织 的 组织Id）\n  * @param {integer} args.memberCategory 成员类型（用户 = 10、部门 = 20、部门树 = 21、职位 = 30、组织角色 = 40、网络（全组织） = 50）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "string", "name": "args.memberId", "description": "成员Id（用户Id、部门Id、部门树的部门Id、职位Id、组织角色Id、全组织 的 组织Id）"}, {"type": "integer", "name": "args.memberCategory", "description": "成员类型（用户 = 10、部门 = 20、部门树 = 21、职位 = 30、组织角色 = 40、网络（全组织） = 50）"}]}, {"name": "cancelRoleCharger", "description": "取消设置 角色负责人", "comments": "*\n  * 取消设置 角色负责人\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {string} args.projectId 网络id\n  * @param {string} args.memberId 成员Id（用户Id、部门Id、部门树的部门Id、职位Id、组织角色Id、全组织 的 组织Id）\n  * @param {integer} args.memberCategory 成员类型（用户 = 10、部门 = 20、部门树 = 21、职位 = 30、组织角色 = 40、网络（全组织） = 50）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "string", "name": "args.memberId", "description": "成员Id（用户Id、部门Id、部门树的部门Id、职位Id、组织角色Id、全组织 的 组织Id）"}, {"type": "integer", "name": "args.memberCategory", "description": "成员类型（用户 = 10、部门 = 20、部门树 = 21、职位 = 30、组织角色 = 40、网络（全组织） = 50）"}]}, {"name": "quitAppForRole", "description": "退出应用单个角色", "comments": "*\n  * 退出应用单个角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}]}, {"name": "quitRole", "description": "退出应用下所有角色", "comments": "*\n  * 退出应用下所有角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "editAppRole", "description": "配置角色权限", "comments": "*\n  * 配置角色权限\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {} args.appRoleModel\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "removeUserToRole", "description": "把人员移动到其他角色", "comments": "*\n  * 把人员移动到其他角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.sourceAppRoleId 来源角色id\n  * @param {array} args.resultAppRoleIds 目标角色id\n  * @param {boolean} args.selectAll 是否全选\n  * @param {array} args.userIds 用户id集合\n  * @param {array} args.departmentIds 部门id集合\n  * @param {array} args.jobIds 职位id集合\n  * @param {string} args.projectId 网络id\n  * @param {array} args.departmentTreeIds 部门树\n  * @param {array} args.projectOrganizeIds 网络角色\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.sourceAppRoleId", "description": "来源角色id"}, {"type": "array", "name": "args.resultAppRoleIds", "description": "目标角色id"}, {"type": "boolean", "name": "args.selectAll", "description": "是否全选"}, {"type": "array", "name": "args.userIds", "description": "用户id集合"}, {"type": "array", "name": "args.departmentIds", "description": "部门id集合"}, {"type": "array", "name": "args.jobIds", "description": "职位id集合"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "array", "name": "args.departmentTreeIds", "description": "部门树"}, {"type": "array", "name": "args.projectOrganizeIds", "description": "网络角色"}]}, {"name": "updateMemberStatus", "description": "设置 开启/关闭 普通成员 是否可见角色列表", "comments": "*\n  * 设置 开启/关闭 普通成员 是否可见角色列表\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {} args.status\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "updateAppRoleNotify", "description": "设置 开启/关闭 应用角色通知", "comments": "*\n  * 设置 开启/关闭 应用角色通知\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用 Id\n  * @param {boolean} args.notify 通知\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用 Id"}, {"type": "boolean", "name": "args.notify", "description": "通知"}]}, {"name": "updateAppDebugModel", "description": "设置 开启/关闭 Debug模式", "comments": "*\n  * 设置 开启/关闭 Debug模式\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用 Id\n  * @param {boolean} args.isDebug 通知\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用 Id"}, {"type": "boolean", "name": "args.isDebug", "description": "通知"}]}, {"name": "setDebugRoles", "description": "当前用户 设置调试的 角色", "comments": "*\n  * 当前用户 设置调试的 角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用 Id\n  * @param {array} args.roleIds 调试/模拟的 角色Ids（不传 则退出 调试）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用 Id"}, {"type": "array", "name": "args.roleIds", "description": "调试/模拟的 角色Ids（不传 则退出 调试）"}]}, {"name": "copyRole", "description": "复制角色", "comments": "*\n  * 复制角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {string} args.roleName 新角色名称\n  * @param {boolean} args.copyPortalRole 是否是复制的外部门户角色\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}, {"type": "string", "name": "args.roleName", "description": "新角色名称"}, {"type": "boolean", "name": "args.copyPortalRole", "description": "是否是复制的外部门户角色"}]}, {"name": "copyRoleToExternalPortal", "description": "复制角色到外部门户", "comments": "*\n  * 复制角色到外部门户\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色Id\n  * @param {string} args.roleName 角色名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色Id"}, {"type": "string", "name": "args.roleName", "description": "角色名称"}]}, {"name": "copyExternalRolesToInternal", "description": "复制外部门户角色到内部", "comments": "*\n  * 复制外部门户角色到内部\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色Id\n  * @param {string} args.roleName 角色名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色Id"}, {"type": "string", "name": "args.roleName", "description": "角色名称"}]}, {"name": "sortRoles", "description": "角色排序", "comments": "*\n  * 角色排序\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {array} args.roleIds 排序后的角色ids\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "array", "name": "args.roleIds", "description": "排序后的角色ids"}]}, {"name": "getAppRoleSetting", "description": "获取 应用角色设置", "comments": "*\n  * 获取 应用角色设置\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {boolean} args.notOnSettingPage 不是在 配置页面（ 当为 ture 时，代表是在 前台/非管理 页面，此时 需要验证 角色负责人）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.notOnSettingPage", "description": "不是在 配置页面（ 当为 ture 时，代表是在 前台/非管理 页面，此时 需要验证 角色负责人）"}]}, {"name": "getRolesWithUsers", "description": "获取应用下所用角色基本信息（不含具体权限）", "comments": "*\n  * 获取应用下所用角色基本信息（不含具体权限）\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "getTotalMember", "description": "分页获取 全部成员", "comments": "*\n  * 分页获取 全部成员\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {integer} args.pageIndex 分页面码 = 默认1\n  * @param {integer} args.pageSize 分页 页大小\n  * @param {string} args.keywords 查询 关键词（现仅 支持 成员名称）\n  * @param {integer} args.searchMemberType 搜索 成员类型（默认=0、用户/人员=10、部门=20，组织角色=30，职位=40）\n  * @param {array} args.sort 排序参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "integer", "name": "args.pageIndex", "description": "分页面码 = 默认1"}, {"type": "integer", "name": "args.pageSize", "description": "分页 页大小"}, {"type": "string", "name": "args.keywords", "description": "查询 关键词（现仅 支持 成员名称）"}, {"type": "integer", "name": "args.searchMemberType", "description": "搜索 成员类型（默认=0、用户/人员=10、部门=20，组织角色=30，职位=40）"}, {"type": "array", "name": "args.sort", "description": "排序参数"}]}, {"name": "getRolesByMemberId", "description": "获取 成员的 角色Id和名称", "comments": "*\n  * 获取 成员的 角色Id和名称\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.memberId\n  * @param {} args.memberType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.memberId", "description": ""}]}, {"name": "getOutsourcingMembers", "description": "分页获取 外协成员", "comments": "*\n  * 分页获取 外协成员\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {integer} args.pageIndex 分页面码 = 默认1\n  * @param {integer} args.pageSize 分页 页大小\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "integer", "name": "args.pageIndex", "description": "分页面码 = 默认1"}, {"type": "integer", "name": "args.pageSize", "description": "分页 页大小"}]}, {"name": "getAppRoleSummary", "description": "获取 角色列表（包含 我加入的角色标识）", "comments": "*\n  * 获取 角色列表（包含 我加入的角色标识）\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.allJoinRoles 查看所有加入的角色\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.allJoin<PERSON><PERSON>s", "description": "查看所有加入的角色"}]}, {"name": "getDebugRoles", "description": "获取 调试模式 的可选角色", "comments": "*\n  * 获取 调试模式 的可选角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "getMembersByRole", "description": "根据角色 分页获取 角色下的用户集", "comments": "*\n  * 根据角色 分页获取 角色下的用户集\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {string} args.roleId 角色Id\n  * @param {integer} args.pageIndex 分页面码 = 默认1\n  * @param {integer} args.pageSize 分页 页大小\n  * @param {string} args.keywords 查询 关键词（现仅 支持 成员名称）\n  * @param {integer} args.searchMemberType 搜索 成员类型（默认=0、用户/人员=10、部门=20，组织角色=30，职位=40）\n  * @param {array} args.sort 排序参数  （其中 FieldType值为： 默认[时间] = 0、时间 = 10、类型 = 20）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "string", "name": "args.roleId", "description": "角色Id"}, {"type": "integer", "name": "args.pageIndex", "description": "分页面码 = 默认1"}, {"type": "integer", "name": "args.pageSize", "description": "分页 页大小"}, {"type": "string", "name": "args.keywords", "description": "查询 关键词（现仅 支持 成员名称）"}, {"type": "integer", "name": "args.searchMemberType", "description": "搜索 成员类型（默认=0、用户/人员=10、部门=20，组织角色=30，职位=40）"}, {"type": "array", "name": "args.sort", "description": "排序参数  （其中 FieldType值为： 默认[时间] = 0、时间 = 10、类型 = 20）"}]}, {"name": "batchEditMemberRole", "description": "批量编辑用户角色", "comments": "*\n  * 批量编辑用户角色\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {array} args.dstRoleIds 目标角色Ids\n  * @param {} args.selectMember\n  * @param {boolean} args.selectAll 是否全选\n  * @param {boolean} args.isOutsourcing 是否全选外协\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "array", "name": "args.dstRoleIds", "description": "目标角色Ids"}, {"type": "boolean", "name": "args.selectAll", "description": "是否全选"}, {"type": "boolean", "name": "args.isOutsourcing", "description": "是否全选外协"}]}, {"name": "batchMemberQuitApp", "description": "批量成员退出应用", "comments": "*\n  * 批量成员退出应用\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用Id\n  * @param {} args.selectMember\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用Id"}]}, {"name": "getRoleDetail", "description": "获取应用下某个角色的具体权限信息", "comments": "*\n  * 获取应用下某个角色的具体权限信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.roleId 角色id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.roleId", "description": "角色id"}]}, {"name": "getAddRoleTemplate", "description": "获取应用下所有工作表信息生成添加角色模板", "comments": "*\n  * 获取应用下所有工作表信息生成添加角色模板\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "getAppForManager", "description": "获取网络下用户为应用管理员的应用信息", "comments": "*\n  * 获取网络下用户为应用管理员的应用信息\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {} args.type\n  * @param {boolean} args.containsLinks 是否包含链接类型\n  * @param {boolean} args.getLock 是否获取锁定应用（默认不获取）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含链接类型"}, {"type": "boolean", "name": "args.getLock", "description": "是否获取锁定应用（默认不获取）"}]}, {"name": "getManagerApps", "description": "网络下用户为管理员的应用集合", "comments": "*\n  * 网络下用户为管理员的应用集合\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.containsLinks 是否包含链接类型\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含链接类型"}]}, {"name": "refresh", "description": "刷新权限缓存", "comments": "*\n  * 刷新权限缓存\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}]}, {"name": "getUserIdApps", "description": "获取以用户方式加入的应用", "comments": "*\n  * 获取以用户方式加入的应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.userId 交接用户id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.userId", "description": "交接用户id"}]}, {"name": "replaceRoleMemberForApps", "description": "交接应用角色", "comments": "*\n  * 交接应用角色\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.removeUserId 要移除的 用户Id\n  * @param {string} args.addUserId &gt;新添加的用户Id（可空，空时 = 仅移除）\n  * @param {array} args.roles\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.removeUserId", "description": "要移除的 用户Id"}, {"type": "string", "name": "args.addUserId", "description": "&gt;新添加的用户Id（可空，空时 = 仅移除）"}, {"type": "array", "name": "args.roles", "description": ""}]}, {"name": "getAppsForProject", "description": "获取网络下应用", "comments": "*\n  * 获取网络下应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {integer} args.status 应用状态  0=关闭 1=启用  可空\n  * @param {} args.order\n  * @param {integer} args.pageIndex 页数（从1开始）\n  * @param {integer} args.pageSize 每页显示数\n  * @param {string} args.keyword 搜索关键字（支持名称和拥有者名称）\n  * @param {integer} args.sourceType 来源 默认0=全部，2=过滤分发平台\n  * @param {} args.filterType\n  * @param {boolean} args.containsLinks 是否包含链接类型\n  * @param {integer} args.filterDBType 数据筛选类型（0：全部，1= 默认数据库，2 =专属数据库，DbInstanceId传具体id）\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "integer", "name": "args.status", "description": "应用状态  0=关闭 1=启用  可空"}, {"type": "integer", "name": "args.pageIndex", "description": "页数（从1开始）"}, {"type": "integer", "name": "args.pageSize", "description": "每页显示数"}, {"type": "string", "name": "args.keyword", "description": "搜索关键字（支持名称和拥有者名称）"}, {"type": "integer", "name": "args.sourceType", "description": "来源 默认0=全部，2=过滤分发平台"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含链接类型"}, {"type": "integer", "name": "args.filterDBType", "description": "数据筛选类型（0：全部，1= 默认数据库，2 =专属数据库，DbInstanceId传具体id）"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}, {"name": "getAppsByProject", "description": "分页获取网络下应用信息", "comments": "*\n  * 分页获取网络下应用信息\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {integer} args.status 应用状态  0=关闭 1=启用  可空\n  * @param {} args.order\n  * @param {integer} args.pageIndex 页数（从1开始）\n  * @param {integer} args.pageSize 每页显示数\n  * @param {string} args.keyword 搜索关键字（支持名称和拥有者名称）\n  * @param {integer} args.sourceType 来源 默认0=全部，2=过滤分发平台\n  * @param {} args.filterType\n  * @param {boolean} args.containsLinks 是否包含链接类型\n  * @param {integer} args.filterDBType 数据筛选类型（0：全部，1= 默认数据库，2 =专属数据库，DbInstanceId传具体id）\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "integer", "name": "args.status", "description": "应用状态  0=关闭 1=启用  可空"}, {"type": "integer", "name": "args.pageIndex", "description": "页数（从1开始）"}, {"type": "integer", "name": "args.pageSize", "description": "每页显示数"}, {"type": "string", "name": "args.keyword", "description": "搜索关键字（支持名称和拥有者名称）"}, {"type": "integer", "name": "args.sourceType", "description": "来源 默认0=全部，2=过滤分发平台"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含链接类型"}, {"type": "integer", "name": "args.filterDBType", "description": "数据筛选类型（0：全部，1= 默认数据库，2 =专属数据库，DbInstanceId传具体id）"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}, {"name": "getApps", "description": "获取应用信息（批量）", "comments": "*\n  * 获取应用信息（批量）\n  * @param {Object} args 请求参数\n  * @param {array} args.appIds\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.appIds", "description": ""}]}, {"name": "getToken", "description": "获取导出相关功能模块token", "comments": "*\n  * 获取导出相关功能模块token\n  * @param {Object} args 请求参数\n  * @param {} args.tokenType\n  * @param {string} args.worksheetId\n  * @param {string} args.viewId\n  * @param {string} args.projectId 网络id ，TokenType = 4或6时，这个必穿\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": ""}, {"type": "string", "name": "args.viewId", "description": ""}, {"type": "string", "name": "args.projectId", "description": "网络id ，TokenType = 4或6时，这个必穿"}]}, {"name": "editAppStatus", "description": "更新应用状态", "comments": "*\n  * 更新应用状态\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id（原应用包id）\n  * @param {integer} args.status 状态  0=关闭 1=启用 2=删除\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id（原应用包id）"}, {"type": "integer", "name": "args.status", "description": "状态  0=关闭 1=启用 2=删除"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "checkIsAppAdmin", "description": "检测是否是网络后台应用管理员", "comments": "*\n  * 检测是否是网络后台应用管理员\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "checkAppAdminForUser", "description": "验证用户是否在应用管理员中", "comments": "*\n  * 验证用户是否在应用管理员中\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "addRoleMemberForAppAdmin", "description": "把自己加入应用管理员(后台)", "comments": "*\n  * 把自己加入应用管理员(后台)\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "removeWorkSheetAscription", "description": "移动分组下项到另外一个分组（如果是同一应用下应用id相同即可）", "comments": "*\n  * 移动分组下项到另外一个分组（如果是同一应用下应用id相同即可）\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceAppId 来源应用id\n  * @param {string} args.resultAppId 目标应用id\n  * @param {string} args.sourceAppSectionId 来源应用分组id\n  * @param {string} args.resultAppSectionId 目标应用分组id\n  * @param {array} args.workSheetsInfo 基础信息集合\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceAppId", "description": "来源应用id"}, {"type": "string", "name": "args.resultAppId", "description": "目标应用id"}, {"type": "string", "name": "args.sourceAppSectionId", "description": "来源应用分组id"}, {"type": "string", "name": "args.resultAppSectionId", "description": "目标应用分组id"}, {"type": "array", "name": "args.workSheetsInfo", "description": "基础信息集合"}]}, {"name": "removeWorkSheetForApp", "description": "删除应用分组下项(工作表，自定义页面)", "comments": "*\n  * 删除应用分组下项(工作表，自定义页面)\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appSectionId 应用分组id\n  * @param {string} args.workSheetId id\n  * @param {integer} args.type 类型 0=工作表，1=自定义页面\n  * @param {boolean} args.isPermanentlyDelete 是否永久删除 true-表示永久删除 false-表示到回收站\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appSectionId", "description": "应用分组id"}, {"type": "string", "name": "args.workSheetId", "description": "id"}, {"type": "integer", "name": "args.type", "description": "类型 0=工作表，1=自定义页面"}, {"type": "boolean", "name": "args.isPermanentlyDelete", "description": "是否永久删除 true-表示永久删除 false-表示到回收站"}]}, {"name": "getAppItemRecoveryList", "description": "分页获取应用项回收站列表", "comments": "*\n  * 分页获取应用项回收站列表\n  * @param {Object} args 请求参数\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appId 应用id\n  * @param {string} args.keyword 关键字搜索\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.keyword", "description": "关键字搜索"}]}, {"name": "appItemRecovery", "description": "@param {Object} args 请求参数", "comments": "*\n  * \n  * @param {Object} args 请求参数\n  * @param {string} args.id 应用项回收站记录id\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "应用项回收站记录id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "editWorkSheetInfoForApp", "description": "修改分组下实体名称和图标", "comments": "*\n  * 修改分组下实体名称和图标\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 应用分组id\n  * @param {string} args.workSheetId id\n  * @param {string} args.workSheetName 名称\n  * @param {string} args.icon 图标\n  * @param {integer} args.type 类型\n  * @param {string} args.urlTemplate 链接\n  * @param {object} args.configuration 链接配置\n  * @param {string} args.resume 摘要\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "应用分组id"}, {"type": "string", "name": "args.workSheetId", "description": "id"}, {"type": "string", "name": "args.workSheetName", "description": "名称"}, {"type": "string", "name": "args.icon", "description": "图标"}, {"type": "integer", "name": "args.type", "description": "类型"}, {"type": "string", "name": "args.urlTemplate", "description": "链接"}, {"type": "object", "name": "args.configuration", "description": "链接配置"}, {"type": "string", "name": "args.resume", "description": "摘要"}]}, {"name": "updateAppOwner", "description": "变更应用拥有者", "comments": "*\n  * 变更应用拥有者\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.memberId 新的应用拥有者\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.memberId", "description": "新的应用拥有者"}]}, {"name": "addWorkSheet", "description": "应用分组下新增项", "comments": "*\n  * 应用分组下新增项\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId SectionId\n  * @param {string} args.name 名称\n  * @param {string} args.icon Logo\n  * @param {integer} args.type 类型 0=工作表 1=自定义页面\n  * @param {integer} args.createType 创建类型（创建自定义页面得时候需要传）0-表示普通 1-表示外部链接\n  * @param {string} args.urlTemplate 链接\n  * @param {object} args.configuration 链接配置\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "SectionId"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.icon", "description": "Logo"}, {"type": "integer", "name": "args.type", "description": "类型 0=工作表 1=自定义页面"}, {"type": "integer", "name": "args.createType", "description": "创建类型（创建自定义页面得时候需要传）0-表示普通 1-表示外部链接"}, {"type": "string", "name": "args.urlTemplate", "description": "链接"}, {"type": "object", "name": "args.configuration", "description": "链接配置"}]}, {"name": "addSheet", "description": "新增工作表（级联数据源及子表）", "comments": "*\n  * 新增工作表（级联数据源及子表）\n  * @param {Object} args 请求参数\n  * @param {string} args.worksheetId 原始工作表id\n  * @param {string} args.name\n  * @param {integer} args.worksheetType 1：普通表 2：子表\n  * @param {boolean} args.createLayer 直接创建层级视图\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.worksheetId", "description": "原始工作表id"}, {"type": "string", "name": "args.name", "description": ""}, {"type": "integer", "name": "args.worksheetType", "description": "1：普通表 2：子表"}, {"type": "boolean", "name": "args.create<PERSON>ayer", "description": "直接创建层级视图"}]}, {"name": "changeSheet", "description": "转换工作表", "comments": "*\n  * 转换工作表\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceWorksheetId 来源工作表id\n  * @param {string} args.worksheetId 子表id\n  * @param {string} args.name 子表名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceWorksheetId", "description": "来源工作表id"}, {"type": "string", "name": "args.worksheetId", "description": "子表id"}, {"type": "string", "name": "args.name", "description": "子表名称"}]}, {"name": "copyCustomPage", "description": "复制自定义页面", "comments": "*\n  * 复制自定义页面\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId SectionId\n  * @param {string} args.name 名称\n  * @param {string} args.id 自定义页面id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "SectionId"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.id", "description": "自定义页面id"}]}, {"name": "addAuthorize", "description": "新增应用授权", "comments": "*\n  * 新增应用授权\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {integer} args.type 权限范围类型 1=全部，2=只读\n  * @param {boolean} args.viewNull 不传视图id不返回数据配置\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.type", "description": "权限范围类型 1=全部，2=只读"}, {"type": "boolean", "name": "args.viewNull", "description": "不传视图id不返回数据配置"}]}, {"name": "getAuthorizes", "description": "获取应用授权", "comments": "*\n  * 获取应用授权\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "editAuthorizeStatus", "description": "编辑应用授权类型", "comments": "*\n  * 编辑应用授权类型\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appKey 应用key\n  * @param {integer} args.type 权限范围类型 1=全部，2=只读\n  * @param {boolean} args.viewNull 不传视图id不返回数据配置\n  * @param {integer} args.status 授权状态 1-开启 2-关闭 3-删除\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appKey", "description": "应用key"}, {"type": "integer", "name": "args.type", "description": "权限范围类型 1=全部，2=只读"}, {"type": "boolean", "name": "args.viewNull", "description": "不传视图id不返回数据配置"}, {"type": "integer", "name": "args.status", "description": "授权状态 1-开启 2-关闭 3-删除"}]}, {"name": "deleteAuthorizeStatus", "description": "删除应用授权类型", "comments": "*\n  * 删除应用授权类型\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appKey 应用key\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appKey", "description": "应用key"}]}, {"name": "editAuthorizeRemark", "description": "编辑备注", "comments": "*\n  * 编辑备注\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.appKey\n  * @param {string} args.remark 备注\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.appKey", "description": ""}, {"type": "string", "name": "args.remark", "description": "备注"}]}, {"name": "getWeiXinBindingInfo", "description": "获取绑定的微信公众号信息", "comments": "*\n  * 获取绑定的微信公众号信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId AppId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "AppId"}]}, {"name": "getAppApplyInfo", "description": "获取当前应用的的申请信息", "comments": "*\n  * 获取当前应用的的申请信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {integer} args.pageIndex 页码\n  * @param {integer} args.size 页大小\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.pageIndex", "description": "页码"}, {"type": "integer", "name": "args.size", "description": "页大小"}]}, {"name": "addAppApply", "description": "申请加入应用", "comments": "*\n  * 申请加入应用\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.remark 申请说明\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.remark", "description": "申请说明"}]}, {"name": "editAppApplyStatus", "description": "更新应用申请状态", "comments": "*\n  * 更新应用申请状态\n  * @param {Object} args 请求参数\n  * @param {array} args.ids 申请信息的id\n  * @param {string} args.appId 应用id\n  * @param {integer} args.status 状态 2=通过，3=拒绝\n  * @param {string} args.roleId 角色id（拒绝时可空）\n  * @param {string} args.remark 备注，拒绝理由\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.ids", "description": "申请信息的id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.status", "description": "状态 2=通过，3=拒绝"}, {"type": "string", "name": "args.roleId", "description": "角色id（拒绝时可空）"}, {"type": "string", "name": "args.remark", "description": "备注，拒绝理由"}]}, {"name": "getIcon", "description": "获取icon（包含系统和自定义）", "comments": "*\n  * 获取icon（包含系统和自定义）\n  * @param {Object} args 请求参数\n  * @param {array} args.fileNames 自定义图标名称\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isLine 线性图标或者面性图标 true表示线性，false表示面性，默认值为true\n  * @param {boolean} args.iconType 图标类型 true-表示系统图标 false-自定义图标\n  * @param {array} args.categories 分类数组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.fileNames", "description": "自定义图标名称"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isLine", "description": "线性图标或者面性图标 true表示线性，false表示面性，默认值为true"}, {"type": "boolean", "name": "args.iconType", "description": "图标类型 true-表示系统图标 false-自定义图标"}, {"type": "array", "name": "args.categories", "description": "分类数组"}]}, {"name": "addCustomIcon", "description": "添加自定义图标", "comments": "*\n  * 添加自定义图标\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {array} args.data icon数据\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "array", "name": "args.data", "description": "icon数据"}]}, {"name": "deleteCustomIcon", "description": "删除自定义图标", "comments": "*\n  * 删除自定义图标\n  * @param {Object} args 请求参数\n  * @param {array} args.fileNames 自定义图标名称\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isLine 线性图标或者面性图标 true表示线性，false表示面性，默认值为true\n  * @param {boolean} args.iconType 图标类型 true-表示系统图标 false-自定义图标\n  * @param {array} args.categories 分类数组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.fileNames", "description": "自定义图标名称"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isLine", "description": "线性图标或者面性图标 true表示线性，false表示面性，默认值为true"}, {"type": "boolean", "name": "args.iconType", "description": "图标类型 true-表示系统图标 false-自定义图标"}, {"type": "array", "name": "args.categories", "description": "分类数组"}]}, {"name": "getCustomIconByProject", "description": "获取自定义图标", "comments": "*\n  * 获取自定义图标\n  * @param {Object} args 请求参数\n  * @param {array} args.fileNames 自定义图标名称\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isLine 线性图标或者面性图标 true表示线性，false表示面性，默认值为true\n  * @param {boolean} args.iconType 图标类型 true-表示系统图标 false-自定义图标\n  * @param {array} args.categories 分类数组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.fileNames", "description": "自定义图标名称"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isLine", "description": "线性图标或者面性图标 true表示线性，false表示面性，默认值为true"}, {"type": "boolean", "name": "args.iconType", "description": "图标类型 true-表示系统图标 false-自定义图标"}, {"type": "array", "name": "args.categories", "description": "分类数组"}]}, {"name": "getAppsCategoryInfo", "description": "获取分类和首页信息", "comments": "*\n  * 获取分类和首页信息\n  * @param {Object} args 请求参数\n  * @param {boolean} args.isCategory 是否只加载分类信息\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "boolean", "name": "args.isCategory", "description": "是否只加载分类信息"}]}, {"name": "getAppsLibraryInfo", "description": "获取分类下应用库模板列表", "comments": "*\n  * 获取分类下应用库模板列表\n  * @param {Object} args 请求参数\n  * @param {string} args.categoryId 分类id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.categoryId", "description": "分类id"}]}, {"name": "installApp", "description": "安装应用", "comments": "*\n  * 安装应用\n  * @param {Object} args 请求参数\n  * @param {string} args.libraryId 应用库id\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.libraryId", "description": "应用库id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "getAppLibraryDetail", "description": "获取单个应用库模板详情", "comments": "*\n  * 获取单个应用库模板详情\n  * @param {Object} args 请求参数\n  * @param {string} args.libraryId 应用库id\n  * @param {string} args.projectId 网络ud\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.libraryId", "description": "应用库id"}, {"type": "string", "name": "args.projectId", "description": "网络ud"}]}, {"name": "getLibraryToken", "description": "获取应用库FileUrl Token", "comments": "*\n  * 获取应用库FileUrl Token\n  * @param {Object} args 请求参数\n  * @param {string} args.libraryId\n  * @param {string} args.projectId 安装目标网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.libraryId", "description": ""}, {"type": "string", "name": "args.projectId", "description": "安装目标网络id"}]}, {"name": "getLogs", "description": "获取日志", "comments": "*\n  * 获取日志\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.keyword 搜索关键字\n  * @param {integer} args.handleType 操作类型 1=创建 2=开启 3=关闭 4=删除 5=导出 6=导入\n  * @param {string} args.start 开始时间\n  * @param {string} args.end 结束时间\n  * @param {integer} args.pageIndex\n  * @param {integer} args.pageSize\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.keyword", "description": "搜索关键字"}, {"type": "integer", "name": "args.handleType", "description": "操作类型 1=创建 2=开启 3=关闭 4=删除 5=导出 6=导入"}, {"type": "string", "name": "args.start", "description": "开始时间"}, {"type": "string", "name": "args.end", "description": "结束时间"}, {"type": "integer", "name": "args.pageIndex", "description": ""}, {"type": "integer", "name": "args.pageSize", "description": ""}]}, {"name": "getExportsByApp", "description": "获取导出记录", "comments": "*\n  * 获取导出记录\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {integer} args.pageIndex\n  * @param {integer} args.pageSize\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "integer", "name": "args.pageIndex", "description": ""}, {"type": "integer", "name": "args.pageSize", "description": ""}]}, {"name": "getExportPassword", "description": "导出密码", "comments": "*\n  * 导出密码\n  * @param {Object} args 请求参数\n  * @param {string} args.id 日志id\n  * @param {string} args.appId 应用id\n  * @param {integer} args.passwordType 0 = 导出密码，1 = 锁定密码\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "日志id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "integer", "name": "args.passwordType", "description": "0 = 导出密码，1 = 锁定密码"}]}, {"name": "addWorkflow", "description": "创建工作流CSM", "comments": "*\n  * 创建工作流CSM\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}]}, {"name": "getEntityShare", "description": "获取应用实体分享信息", "comments": "*\n  * 获取应用实体分享信息\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 分享来源id （页面id，图标id等）\n  * @param {} args.sourceType\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "分享来源id （页面id，图标id等）"}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "editEntityShareStatus", "description": "修改应用实体分享信息", "comments": "*\n  * 修改应用实体分享信息\n  * @param {Object} args 请求参数\n  * @param {string} args.sourceId 分享来源id （页面id，图标id等）\n  * @param {integer} args.sourceType 分享类型  21 =自定义页面，31 = 图表\n  * @param {integer} args.status 状态  0 = 关闭，1 =启用\n  * @param {string} args.password 密码\n  * @param {string} args.validTime 有效时间\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.sourceId", "description": "分享来源id （页面id，图标id等）"}, {"type": "integer", "name": "args.sourceType", "description": "分享类型  21 =自定义页面，31 = 图表"}, {"type": "integer", "name": "args.status", "description": "状态  0 = 关闭，1 =启用"}, {"type": "string", "name": "args.password", "description": "密码"}, {"type": "string", "name": "args.validTime", "description": "有效时间"}]}, {"name": "getEntityShareById", "description": "获取分享基础信息", "comments": "*\n  * 获取分享基础信息\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.id 分享id\n  * @param {string} args.password 密码\n  * @param {string} args.clientId 客户端id\n  * @param {} args.langType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.id", "description": "分享id"}, {"type": "string", "name": "args.password", "description": "密码"}, {"type": "string", "name": "args.clientId", "description": "客户端id"}]}, {"name": "deleteBackupFile", "description": "删除应用备份文件", "comments": "*\n  * 删除应用备份文件\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.appId 应用id\n  * @param {string} args.id 应用备份操作日志Id\n  * @param {string} args.fileName 应用备份的文件名\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.id", "description": "应用备份操作日志Id"}, {"type": "string", "name": "args.fileName", "description": "应用备份的文件名"}]}, {"name": "pageGetBackupRestoreOperationLog", "description": "分页获取应用备份还原操作日志", "comments": "*\n  * 分页获取应用备份还原操作日志\n  * @param {Object} args 请求参数\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appId 应用Id\n  * @param {boolean} args.isBackup 是否为获取备份文件列表，true表示获取备份文件列表，false表示获取操作日志列表\n  * @param {string} args.accountId 操作人\n  * @param {string} args.startTime 开始时间\n  * @param {string} args.endTime 结束时间\n  * @param {} args.orderType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appId", "description": "应用Id"}, {"type": "boolean", "name": "args.isBackup", "description": "是否为获取备份文件列表，true表示获取备份文件列表，false表示获取操作日志列表"}, {"type": "string", "name": "args.accountId", "description": "操作人"}, {"type": "string", "name": "args.startTime", "description": "开始时间"}, {"type": "string", "name": "args.endTime", "description": "结束时间"}]}, {"name": "getAppSupportInfo", "description": "获取应用数量信息", "comments": "*\n  * 获取应用数量信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId AppId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "AppId"}]}, {"name": "renameBackupFileName", "description": "重命名应用备份文件", "comments": "*\n  * 重命名应用备份文件\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.appId 应用id\n  * @param {string} args.id 应用备份操作日志Id\n  * @param {string} args.fileName 备份新名称\n  * @param {string} args.fileOldName 备份新名称\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.id", "description": "应用备份操作日志Id"}, {"type": "string", "name": "args.fileName", "description": "备份新名称"}, {"type": "string", "name": "args.fileOldName", "description": "备份新名称"}]}, {"name": "getValidBackupFileInfo", "description": "获取有效备份文件信息", "comments": "*\n  * 获取有效备份文件信息\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.appId 应用id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.appId", "description": "应用id"}]}, {"name": "restore", "description": "还原应用", "comments": "*\n  * 还原应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {string} args.appId 应用id\n  * @param {string} args.id 备份还原操作日志id\n  * @param {boolean} args.autoEndMaintain 是否自动结束应用维护状态\n  * @param {boolean} args.backupCurrentVersion 备份当前版本\n  * @param {boolean} args.isRestoreNew 是否还原为新应用\n  * @param {boolean} args.containData 是否还原数据\n  * @param {string} args.fileUrl 文件链接\n  * @param {string} args.fileName 文件名称\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.id", "description": "备份还原操作日志id"}, {"type": "boolean", "name": "args.autoEndMaintain", "description": "是否自动结束应用维护状态"}, {"type": "boolean", "name": "args.backupCurrentVersion", "description": "备份当前版本"}, {"type": "boolean", "name": "args.isRestoreNew", "description": "是否还原为新应用"}, {"type": "boolean", "name": "args.containData", "description": "是否还原数据"}, {"type": "string", "name": "args.fileUrl", "description": "文件链接"}, {"type": "string", "name": "args.fileName", "description": "文件名称"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}]}, {"name": "homeApp", "description": "应用", "data": [{"name": "createApp", "description": "添加应用", "comments": "*\n  * 添加应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {string} args.name 名称\n  * @param {string} args.icon 图标\n  * @param {string} args.iconColor 图标颜色\n  * @param {string} args.navColor 导航颜色\n  * @param {string} args.lightColor 背景色\n  * @param {string} args.groupId 分组id\n  * @param {} args.groupType\n  * @param {string} args.urlTemplate url链接模板\n  * @param {object} args.configuratiuon 链接配置\n  * @param {} args.createType\n  * @param {boolean} args.pcDisplay Pc端显示,\n  * @param {boolean} args.webMobileDisplay web移动端显示\n  * @param {boolean} args.appDisplay app端显示\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.icon", "description": "图标"}, {"type": "string", "name": "args.iconColor", "description": "图标颜色"}, {"type": "string", "name": "args.navColor", "description": "导航颜色"}, {"type": "string", "name": "args.lightColor", "description": "背景色"}, {"type": "string", "name": "args.groupId", "description": "分组id"}, {"type": "string", "name": "args.urlTemplate", "description": "url链接模板"}, {"type": "object", "name": "args.configurat<PERSON><PERSON>", "description": "链接配置"}, {"type": "boolean", "name": "args.pcDisplay", "description": "Pc端显示,"}, {"type": "boolean", "name": "args.webMobileDisplay", "description": "web移动端显示"}, {"type": "boolean", "name": "args.appDisplay", "description": "app端显示"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}, {"name": "deleteApp", "description": "首页删除应用(删除之后进入回收站)", "comments": "*\n  * 首页删除应用(删除之后进入回收站)\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isHomePage 是否首页 true 是 false 否\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isHomePage", "description": "是否首页 true 是 false 否"}]}, {"name": "getAppRecoveryRecordList", "description": "分页获取应用回收站", "comments": "*\n  * 分页获取应用回收站\n  * @param {Object} args 请求参数\n  * @param {integer} args.pageIndex 当前页\n  * @param {integer} args.pageSize 页大小\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.isHomePage 是否为首页\n  * @param {string} args.keyword 关键字搜索\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.pageIndex", "description": "当前页"}, {"type": "integer", "name": "args.pageSize", "description": "页大小"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.isHomePage", "description": "是否为首页"}, {"type": "string", "name": "args.keyword", "description": "关键字搜索"}]}, {"name": "appRecycleBinDelete", "description": "首页应用回收站彻底删除", "comments": "*\n  * 首页应用回收站彻底删除\n  * @param {Object} args 请求参数\n  * @param {string} args.id 记录id\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.isHomePage 是否首页 true 是 false 否\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "记录id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.isHomePage", "description": "是否首页 true 是 false 否"}]}, {"name": "restoreApp", "description": "恢复应用", "comments": "*\n  * 恢复应用\n  * @param {Object} args 请求参数\n  * @param {string} args.id 记录id\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.isHomePage 是否是首页恢复\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "记录id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.isHomePage", "description": "是否是首页恢复"}]}, {"name": "editAppTimeZones", "description": "编辑应用时区", "comments": "*\n  * 编辑应用时区\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {integer} args.timeZone 1 = 跟随设备，其他参考个人设置，一样的code\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "integer", "name": "args.timeZone", "description": "1 = 跟随设备，其他参考个人设置，一样的code"}]}, {"name": "markApp", "description": "标星应用或应用项", "comments": "*\n  * 标星应用或应用项\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.itemId 应用项id\n  * @param {integer} args.type 0 = 应用，1 = 自定义页面，2 = 工作表\n  * @param {boolean} args.isMark 是否标星（true or false）\n  * @param {string} args.projectId 网络id(可空为个人应用)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.itemId", "description": "应用项id"}, {"type": "integer", "name": "args.type", "description": "0 = 应用，1 = 自定义页面，2 = 工作表"}, {"type": "boolean", "name": "args.isMark", "description": "是否标星（true or false）"}, {"type": "string", "name": "args.projectId", "description": "网络id(可空为个人应用)"}]}, {"name": "editAppInfo", "description": "编辑应用", "comments": "*\n  * 编辑应用\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 网络id\n  * @param {string} args.name 名称\n  * @param {string} args.description 描述\n  * @param {string} args.icon 图标\n  * @param {string} args.iconColor 图标颜色\n  * @param {integer} args.appNaviStyle 移动端:0 = 列表 ，1= 九宫格，2= 导航\n  * @param {integer} args.pcNavistyle PC端:0-经典 1-左侧列表 2-卡片模式，3 = 树形\n  * @param {boolean} args.viewHideNavi 查看影藏导航项\n  * @param {string} args.navColor 导航栏颜色\n  * @param {string} args.lightColor 淡色色值\n  * @param {integer} args.gridDisplayMode 宫格显示模式\n  * @param {integer} args.appNaviDisplayType 移动端导航列表显示类型\n  * @param {string} args.urlTemplate 外部链接url\n  * @param {object} args.configuration 链接配置\n  * @param {boolean} args.pcDisplay Pc端显示,\n  * @param {boolean} args.webMobileDisplay web移动端显示\n  * @param {boolean} args.appDisplay app端显示\n  * @param {integer} args.selectAppItmeType 记住上次使用（2 = 是，1 = 老配置，始终第一个）\n  * @param {integer} args.pcNaviDisplayType 导航分组展开样式（10.2去掉了）\n  * @param {string} args.displayIcon 显示图标,目前只有三级（000，111，，0=不勾选，1=勾选）\n  * @param {integer} args.expandType 展开方式  0 = 默认，1 = 手风琴\n  * @param {boolean} args.hideFirstSection 隐藏首个分组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.description", "description": "描述"}, {"type": "string", "name": "args.icon", "description": "图标"}, {"type": "string", "name": "args.iconColor", "description": "图标颜色"}, {"type": "integer", "name": "args.appNaviStyle", "description": "移动端:0 = 列表 ，1= 九宫格，2= 导航"}, {"type": "integer", "name": "args.pcNavistyle", "description": "PC端:0-经典 1-左侧列表 2-卡片模式，3 = 树形"}, {"type": "boolean", "name": "args.viewHideNavi", "description": "查看影藏导航项"}, {"type": "string", "name": "args.navColor", "description": "导航栏颜色"}, {"type": "string", "name": "args.lightColor", "description": "淡色色值"}, {"type": "integer", "name": "args.gridDisplayMode", "description": "宫格显示模式"}, {"type": "integer", "name": "args.appNaviDisplayType", "description": "移动端导航列表显示类型"}, {"type": "string", "name": "args.urlTemplate", "description": "外部链接url"}, {"type": "object", "name": "args.configuration", "description": "链接配置"}, {"type": "boolean", "name": "args.pcDisplay", "description": "Pc端显示,"}, {"type": "boolean", "name": "args.webMobileDisplay", "description": "web移动端显示"}, {"type": "boolean", "name": "args.appDisplay", "description": "app端显示"}, {"type": "integer", "name": "args.selectAppItmeType", "description": "记住上次使用（2 = 是，1 = 老配置，始终第一个）"}, {"type": "integer", "name": "args.pcNaviDisplayType", "description": "导航分组展开样式（10.2去掉了）"}, {"type": "string", "name": "args.displayIcon", "description": "显示图标,目前只有三级（000，111，，0=不勾选，1=勾选）"}, {"type": "integer", "name": "args.expandType", "description": "展开方式  0 = 默认，1 = 手风琴"}, {"type": "boolean", "name": "args.hideFirstSection", "description": "隐藏首个分组"}]}, {"name": "updateAppSort", "description": "更新首页应用排序", "comments": "*\n  * 更新首页应用排序\n  * @param {Object} args 请求参数\n  * @param {integer} args.sortType 排序类型 1= 全部组织星标应用排序，2 = 网络，3= 个人，4= 外部协作，5= 过期网络，6 = 首页应用分组下应用排序，7 = 当前组织星标应用排序， 8 = 我拥有的应用排序\n  * @param {array} args.appIds 应用id\n  * @param {string} args.projectId 网络id\n  * @param {string} args.groupId 首页分组id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "integer", "name": "args.sortType", "description": "排序类型 1= 全部组织星标应用排序，2 = 网络，3= 个人，4= 外部协作，5= 过期网络，6 = 首页应用分组下应用排序，7 = 当前组织星标应用排序， 8 = 我拥有的应用排序"}, {"type": "array", "name": "args.appIds", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "string", "name": "args.groupId", "description": "首页分组id"}]}, {"name": "copyApp", "description": "复制应用", "comments": "*\n  * 复制应用\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appName 新的应用名称\n  * @param {string} args.groupId 分组id\n  * @param {} args.groupType\n  * @param {string} args.dbInstanceId 数据库实例id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appName", "description": "新的应用名称"}, {"type": "string", "name": "args.groupId", "description": "分组id"}, {"type": "string", "name": "args.dbInstanceId", "description": "数据库实例id"}]}, {"name": "publishSettings", "description": "应用发布设置", "comments": "*\n  * 应用发布设置\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.pcDisplay Pc端显示,\n  * @param {boolean} args.webMobileDisplay web移动端显示\n  * @param {boolean} args.appDisplay app端显示\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.pcDisplay", "description": "Pc端显示,"}, {"type": "boolean", "name": "args.webMobileDisplay", "description": "web移动端显示"}, {"type": "boolean", "name": "args.appDisplay", "description": "app端显示"}]}, {"name": "editWhiteList", "description": "编辑开放接口的白名单", "comments": "*\n  * 编辑开放接口的白名单\n  * @param {Object} args 请求参数\n  * @param {array} args.whiteIps 白名单\n  * @param {string} args.appId 应用id\n  * @param {string} args.projectId 组织id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.whiteIps", "description": "白名单"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.projectId", "description": "组织id"}]}, {"name": "editFix", "description": "更新维护状态", "comments": "*\n  * 更新维护状态\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.projectId\n  * @param {boolean} args.fixed 维护中标识 true,false\n  * @param {string} args.fixRemark 维护通知\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "boolean", "name": "args.fixed", "description": "维护中标识 true,false"}, {"type": "string", "name": "args.fixRemark", "description": "维护通知"}]}, {"name": "edit<PERSON><PERSON><PERSON><PERSON>", "description": "编辑sso登录应用首页地址", "comments": "*\n  * 编辑sso登录应用首页地址\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {string} args.ssoAddress\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.ssoAddress", "description": ""}]}, {"name": "getAllHomeApp", "description": "获取首页所有应用信息", "comments": "*\n  * 获取首页所有应用信息\n  * @param {Object} args 请求参数\n  * @param {boolean} args.containsLinks\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "boolean", "name": "args.containsLinks", "description": ""}]}, {"name": "getWorksheetsByAppId", "description": "获取应用下所有工作表信息", "comments": "*\n  * 获取应用下所有工作表信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {} args.type\n  * @param {boolean} args.getAlias 是否获取工作表别名(默认不获取)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.get<PERSON><PERSON>s", "description": "是否获取工作表别名(默认不获取)"}]}, {"name": "getAttachementImages", "description": "获取附件图片列表", "comments": "*\n  * 获取附件图片列表\n  * @param {Object} args 请求参数\n  * @param {string} args.workSheetId 工作表id\n  * @param {string} args.viewId 视图id\n  * @param {string} args.attachementControlId 控件id\n  * @param {integer} args.imageLimitCount 图片上限数量\n  * @param {integer} args.displayMode 展示方式（默认值为0） 0-all 1-每条记录第一张\n  * @param {array} args.filedIds 工作表字段控件id数组\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.workSheetId", "description": "工作表id"}, {"type": "string", "name": "args.viewId", "description": "视图id"}, {"type": "string", "name": "args.attachementControlId", "description": "控件id"}, {"type": "integer", "name": "args.imageLimitCount", "description": "图片上限数量"}, {"type": "integer", "name": "args.displayMode", "description": "展示方式（默认值为0） 0-all 1-每条记录第一张"}, {"type": "array", "name": "args.filedIds", "description": "工作表字段控件id数组"}]}, {"name": "getPageInfo", "description": "进入应用刷新页面，前端路由匹配接口", "comments": "*\n  * 进入应用刷新页面，前端路由匹配接口\n  * @param {Object} args 请求参数\n  * @param {string} args.id\n  * @param {string} args.sectionId 分组id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": ""}, {"type": "string", "name": "args.sectionId", "description": "分组id"}]}, {"name": "getAppItemDetail", "description": "批量获取应用项信息", "comments": "*\n  * 批量获取应用项信息\n  * @param {Object} args 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": []}, {"name": "getApp", "description": "获取应用详情（包含分组信息，请求参数可选）", "comments": "*\n  * 获取应用详情（包含分组信息，请求参数可选）\n  * @param {Object} args 请求参数\n  * @param {string} args.ticket 验证码返票据\n  * @param {string} args.randStr 票据随机字符串\n  * @param {} args.captchaType\n  * @param {string} args.clientId 客户端标识\n记录输入密码之后，页面刷新不用重复输入密码操作\n滑动过期\n  * @param {string} args.appId 应用id\n  * @param {boolean} args.getSection 是否获取分组信息\n  * @param {boolean} args.getManager 是否获取管理员列表信息\n  * @param {boolean} args.getProject 获取组织信息\n  * @param {boolean} args.getLang 是否获取应用语种信息\n  * @param {boolean} args.isMobile 是否是移动端\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.ticket", "description": "验证码返票据"}, {"type": "string", "name": "args.randStr", "description": "票据随机字符串"}, {"type": "string", "name": "args.clientId", "description": "客户端标识"}, {"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.getSection", "description": "是否获取分组信息"}, {"type": "boolean", "name": "args.getManager", "description": "是否获取管理员列表信息"}, {"type": "boolean", "name": "args.getProject", "description": "获取组织信息"}, {"type": "boolean", "name": "args.getLang", "description": "是否获取应用语种信息"}, {"type": "boolean", "name": "args.isMobile", "description": "是否是移动端"}]}, {"name": "checkApp", "description": "验证应用有效性", "comments": "*\n  * 验证应用有效性\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}]}, {"name": "getAppFirstInfo", "description": "获取应用下分组和第一个工作表信息", "comments": "*\n  * 获取应用下分组和第一个工作表信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId SectionId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "SectionId"}]}, {"name": "getAppSimpleInfo", "description": "获取简单应用id及分组id", "comments": "*\n  * 获取简单应用id及分组id\n  * @param {Object} args 请求参数\n  * @param {string} args.workSheetId 工作表id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.workSheetId", "description": "工作表id"}]}, {"name": "getAppSectionDetail", "description": "根据应用分组id获取详情", "comments": "*\n  * 根据应用分组id获取详情\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 分组id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "分组id"}]}, {"name": "addAppSection", "description": "添加应用分组", "comments": "*\n  * 添加应用分组\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.name 分组名称\n  * @param {string} args.icon 分组图标\n  * @param {string} args.iconColor 分组图标颜色\n  * @param {string} args.sourceAppSectionId 来源应用分组id（在此后添加应用分组）\n  * @param {string} args.parentId 父级分组id（除了创建一级分组外不需要传，其他都需要传）\n  * @param {string} args.rootId 根分组id（除了创建一级分组外不需要传,其他都需要传,参数值为一级分组的id）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.name", "description": "分组名称"}, {"type": "string", "name": "args.icon", "description": "分组图标"}, {"type": "string", "name": "args.iconColor", "description": "分组图标颜色"}, {"type": "string", "name": "args.sourceAppSectionId", "description": "来源应用分组id（在此后添加应用分组）"}, {"type": "string", "name": "args.parentId", "description": "父级分组id（除了创建一级分组外不需要传，其他都需要传）"}, {"type": "string", "name": "args.rootId", "description": "根分组id（除了创建一级分组外不需要传,其他都需要传,参数值为一级分组的id）"}]}, {"name": "updateAppSectionName", "description": "修改应用分组名称", "comments": "*\n  * 修改应用分组名称\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.name 名称\n  * @param {string} args.appSectionId 分组id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.name", "description": "名称"}, {"type": "string", "name": "args.appSectionId", "description": "分组id"}]}, {"name": "updateAppSection", "description": "修改分组基础信息信息", "comments": "*\n  * 修改分组基础信息信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 分组id\n  * @param {string} args.appSectionName 分组名称\n  * @param {string} args.icon 图标\n  * @param {string} args.iconColor 图标颜色\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "分组id"}, {"type": "string", "name": "args.appSectionName", "description": "分组名称"}, {"type": "string", "name": "args.icon", "description": "图标"}, {"type": "string", "name": "args.iconColor", "description": "图标颜色"}]}, {"name": "deleteAppSection", "description": "删除应用分组（并移动该项下工作表到其他应用分组）", "comments": "*\n  * 删除应用分组（并移动该项下工作表到其他应用分组）\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 删除应用分组Id\n  * @param {string} args.sourceAppSectionId 目标应用分组id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "删除应用分组Id"}, {"type": "string", "name": "args.sourceAppSectionId", "description": "目标应用分组id"}]}, {"name": "updateAppSectionSort", "description": "更新应用分组排序信息", "comments": "*\n  * 更新应用分组排序信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {array} args.appSectionIds 删除应用分组Id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "array", "name": "args.appSectionIds", "description": "删除应用分组Id"}]}, {"name": "updateSectionChildSort", "description": "更新应用分组下工作表排序信息", "comments": "*\n  * 更新应用分组下工作表排序信息\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.appSectionId 分组id\n  * @param {array} args.workSheetIds 排序后的完整ids\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.appSectionId", "description": "分组id"}, {"type": "array", "name": "args.workSheetIds", "description": "排序后的完整ids"}]}, {"name": "setWorksheetStatus", "description": "设置应用项显示隐藏", "comments": "*\n  * 设置应用项显示隐藏\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {string} args.worksheetId 工作表id\n  * @param {integer} args.status 状态(1= 显示，2 = 全隐藏，3 = PC隐藏，4 = 移动端隐藏)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "string", "name": "args.worksheetId", "description": "工作表id"}, {"type": "integer", "name": "args.status", "description": "状态(1= 显示，2 = 全隐藏，3 = PC隐藏，4 = 移动端隐藏)"}]}, {"name": "getApiInfo", "description": "获取应用open api文档", "comments": "*\n  * 获取应用open api文档\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {boolean} args.notOnSettingPage 不是在 配置页面（ 当为 ture 时，代表是在 前台/非管理 页面，此时 需要验证 角色负责人）\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "boolean", "name": "args.notOnSettingPage", "description": "不是在 配置页面（ 当为 ture 时，代表是在 前台/非管理 页面，此时 需要验证 角色负责人）"}]}, {"name": "getMyApp", "description": "获取我的应用", "comments": "*\n  * 获取我的应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.containsLinks 是否包含外部链接\n  * @param {boolean} args.getMarkApp 是否获取标记 (默认获取，10.1新版本后可以不用获取)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含外部链接"}, {"type": "boolean", "name": "args.getMarkApp", "description": "是否获取标记 (默认获取，10.1新版本后可以不用获取)"}]}, {"name": "getGroup", "description": "获取首页分组详情", "comments": "*\n  * 获取首页分组详情\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {boolean} args.containsLinks 是否包含外部链接\n  * @param {boolean} args.getMarkApp 是否获取标记 (默认获取，10.1新版本后可以不用获取)\n  * @param {string} args.id 首页分组id\n  * @param {} args.groupType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.containsLinks", "description": "是否包含外部链接"}, {"type": "boolean", "name": "args.getMarkApp", "description": "是否获取标记 (默认获取，10.1新版本后可以不用获取)"}, {"type": "string", "name": "args.id", "description": "首页分组id"}]}, {"name": "addToGroup", "description": "添加应用到分组下", "comments": "*\n  * 添加应用到分组下\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {array} args.personalGroups 个人分组ids\n  * @param {array} args.projectGroups 网络分组ids\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "array", "name": "args.personalGroups", "description": "个人分组ids"}, {"type": "array", "name": "args.projectGroups", "description": "网络分组ids"}]}, {"name": "removeToGroup", "description": "应用从分组下移除", "comments": "*\n  * 应用从分组下移除\n  * @param {Object} args 请求参数\n  * @param {string} args.appId 应用id\n  * @param {array} args.personalGroups 个人分组ids\n  * @param {array} args.projectGroups 网络分组ids\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": "应用id"}, {"type": "array", "name": "args.personalGroups", "description": "个人分组ids"}, {"type": "array", "name": "args.projectGroups", "description": "网络分组ids"}]}, {"name": "markedGroup", "description": "标星分组", "comments": "*\n  * 标星分组\n  * @param {Object} args 请求参数\n  * @param {string} args.id 分组id\n  * @param {} args.groupType\n  * @param {string} args.projectId\n  * @param {boolean} args.isMarked\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "分组id"}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "boolean", "name": "args.isMarked", "description": ""}]}, {"name": "addGroup", "description": "新增首页分组", "comments": "*\n  * 新增首页分组\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {string} args.name\n  * @param {string} args.icon\n  * @param {} args.groupType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "string", "name": "args.icon", "description": ""}]}, {"name": "editGroup", "description": "编辑分组信息", "comments": "*\n  * 编辑分组信息\n  * @param {Object} args 请求参数\n  * @param {string} args.id 分组id\n  * @param {} args.groupType\n  * @param {string} args.projectId\n  * @param {string} args.name\n  * @param {string} args.icon\n  * @param {} args.displayType\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "分组id"}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.name", "description": ""}, {"type": "string", "name": "args.icon", "description": ""}]}, {"name": "deleteGroup", "description": "删除分组", "comments": "*\n  * 删除分组\n  * @param {Object} args 请求参数\n  * @param {string} args.id 分组id\n  * @param {} args.groupType\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.id", "description": "分组id"}, {"type": "string", "name": "args.projectId", "description": ""}]}, {"name": "editGroupSort", "description": "分组排序", "comments": "*\n  * 分组排序\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {array} args.ids 分组ids ，排好序传过来\n  * @param {integer} args.sortType 排序类型 1= 星标，2 = 网络，3= 个人，\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}, {"type": "array", "name": "args.ids", "description": "分组ids ，排好序传过来"}, {"type": "integer", "name": "args.sortType", "description": "排序类型 1= 星标，2 = 网络，3= 个人，"}]}, {"name": "editHomeSetting", "description": "修改首页自定义显示设置", "comments": "*\n  * 修改首页自定义显示设置\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {} args.displayType\n  * @param {} args.markedAppDisplay\n  * @param {} args.todoDisplay\n  * @param {boolean} args.exDisplay 是否显示外部应用\n  * @param {boolean} args.displayCommonApp 是否显示常用应用\n  * @param {boolean} args.isAllAndProject 是否开启全部和组织分组\n  * @param {boolean} args.displayMark 是否显示星标应用\n  * @param {boolean} args.rowCollect 记录收藏\n  * @param {boolean} args.displayApp 工作台左侧菜单是否显示app\n  * @param {boolean} args.displayChart 图表收藏开关\n  * @param {array} args.sortItems 排序\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "boolean", "name": "args.exDisplay", "description": "是否显示外部应用"}, {"type": "boolean", "name": "args.displayCommonApp", "description": "是否显示常用应用"}, {"type": "boolean", "name": "args.isAllAndProject", "description": "是否开启全部和组织分组"}, {"type": "boolean", "name": "args.displayMark", "description": "是否显示星标应用"}, {"type": "boolean", "name": "args.rowCollect", "description": "记录收藏"}, {"type": "boolean", "name": "args.displayApp", "description": "工作台左侧菜单是否显示app"}, {"type": "boolean", "name": "args.display<PERSON>hart", "description": "图表收藏开关"}, {"type": "array", "name": "args.sortItems", "description": "排序"}]}, {"name": "mark<PERSON><PERSON>", "description": "批量标记应用和应用项目", "comments": "*\n  * 批量标记应用和应用项目\n  * @param {Object} args 请求参数\n  * @param {array} args.items 标记的应用和应用项\n  * @param {string} args.projectId 组织id\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.items", "description": "标记的应用和应用项"}, {"type": "string", "name": "args.projectId", "description": "组织id"}]}, {"name": "editPlatformSetting", "description": "编辑平台设置", "comments": "*\n  * 编辑平台设置\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {array} args.bulletinBoards 宣传栏\n  * @param {string} args.color 颜色\n  * @param {string} args.slogan 标语\n  * @param {string} args.logo 组织logo\n  * @param {boolean} args.logoSwitch logo开关\n  * @param {boolean} args.boardSwitch 宣传栏目开关\n  * @param {integer} args.logoHeight logo高度\n  * @param {object} args.advancedSetting\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "array", "name": "args.bulletinBoards", "description": "宣传栏"}, {"type": "string", "name": "args.color", "description": "颜色"}, {"type": "string", "name": "args.slogan", "description": "标语"}, {"type": "string", "name": "args.logo", "description": "组织logo"}, {"type": "boolean", "name": "args.logoSwitch", "description": "logo开关"}, {"type": "boolean", "name": "args.boardSwitch", "description": "宣传栏目开关"}, {"type": "integer", "name": "args.logoHeight", "description": "logo高度"}, {"type": "object", "name": "args.advancedSetting", "description": ""}]}, {"name": "myPlatform", "description": "工作台", "comments": "*\n  * 工作台\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.noCache 不走缓存\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.noCache", "description": "不走缓存"}]}, {"name": "getAppIdsAndItemIdsTest", "description": "获取工作台ids（测试用）", "comments": "*\n  * 获取工作台ids（测试用）\n  * @param {Object} args 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": []}, {"name": "myPlatformLang", "description": "工作台多语言", "comments": "*\n  * 工作台多语言\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.noCache 不走缓存\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.noCache", "description": "不走缓存"}]}, {"name": "getAppItems", "description": "获取应用下应用项", "comments": "*\n  * 获取应用下应用项\n  * @param {Object} args 请求参数\n  * @param {string} args.appId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.appId", "description": ""}]}, {"name": "getHomePlatformSetting", "description": "获取平台设置", "comments": "*\n  * 获取平台设置\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.noCache 不走缓存\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.noCache", "description": "不走缓存"}]}, {"name": "getOwnedApp", "description": "我拥有的应用", "comments": "*\n  * 我拥有的应用\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 组织id\n  * @param {boolean} args.noCache 不走缓存\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "组织id"}, {"type": "boolean", "name": "args.noCache", "description": "不走缓存"}]}, {"name": "getMyDbInstances", "description": "获取可用的专属数据库列表", "comments": "*\n  * 获取可用的专属数据库列表\n注意 受限于版本 应用/组织管理员\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": ""}]}]}, {"name": "actionLog", "description": "操作日志", "data": [{"name": "getActionLogs", "description": "获取登录日志列表", "comments": "*\n  * 获取登录日志列表\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {integer} args.pageIndex 当前页码\n  * @param {integer} args.pageSize 页面尺寸\n  * @param {string} args.startDateTime 开始时间\n  * @param {string} args.endDateTime 结束时间\n  * @param {} args.logType\n  * @param {} args.accountResult\n  * @param {array} args.accountIds 用户ID\n  * @param {array} args.columnNames 列名称\n  * @param {string} args.fileName 导出文件名\n  * @param {boolean} args.confirmExport 是否确认导出(超量的情况下传)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页码"}, {"type": "integer", "name": "args.pageSize", "description": "页面尺寸"}, {"type": "string", "name": "args.startDateTime", "description": "开始时间"}, {"type": "string", "name": "args.endDateTime", "description": "结束时间"}, {"type": "array", "name": "args.accountIds", "description": "用户ID"}, {"type": "array", "name": "args.columnNames", "description": "列名称"}, {"type": "string", "name": "args.fileName", "description": "导出文件名"}, {"type": "boolean", "name": "args.confirmExport", "description": "是否确认导出(超量的情况下传)"}]}, {"name": "getOrgLogs", "description": "获取组织管理日志列表", "comments": "*\n  * 获取组织管理日志列表\n  * @param {Object} args 请求参数\n  * @param {string} args.projectId 网络id\n  * @param {integer} args.pageIndex 当前页码\n  * @param {integer} args.pageSize 页面尺寸\n  * @param {string} args.startDateTime 开始时间\n  * @param {string} args.endDateTime 结束时间\n  * @param {} args.operateTargetType\n  * @param {} args.operateType\n  * @param {array} args.accountIds 用户ID\n  * @param {string} args.fileName 文件名\n  * @param {array} args.columnNames 列名称\n  * @param {boolean} args.confirmExport 是否确认导出(超量的情况下传)\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.projectId", "description": "网络id"}, {"type": "integer", "name": "args.pageIndex", "description": "当前页码"}, {"type": "integer", "name": "args.pageSize", "description": "页面尺寸"}, {"type": "string", "name": "args.startDateTime", "description": "开始时间"}, {"type": "string", "name": "args.endDateTime", "description": "结束时间"}, {"type": "array", "name": "args.accountIds", "description": "用户ID"}, {"type": "string", "name": "args.fileName", "description": "文件名"}, {"type": "array", "name": "args.columnNames", "description": "列名称"}, {"type": "boolean", "name": "args.confirmExport", "description": "是否确认导出(超量的情况下传)"}]}, {"name": "getAccountDevices", "description": "获取登录设备列表", "comments": "*\n  * 获取登录设备列表\n  * @param {Object} args 请求参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": []}, {"name": "addLog", "description": "添加行为日志", "comments": "*\n  * 添加行为日志\n  * @param {Object} args 请求参数\n  * @param {} args.type\n  * @param {string} args.entityId 实体id(根据访问类型不同， 传不同模块id)\n浏览应用，entityId =应用id，浏览自定义页面，entityId = 页面id。其他的浏览行为 =worksheetId\n  * @param {} args.params\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "string", "name": "args.entityId", "description": "实体id(根据访问类型不同， 传不同模块id)"}]}]}, {"name": "instance", "description": "工作流-流程实例", "data": [{"name": "count", "description": "获取待处理列表总数", "comments": "*\n   * 获取待处理列表总数\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}]}, {"name": "forward", "description": "审批-转审", "comments": "*\n   * 审批-转审\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "getArchivedList", "description": "获取归档服务地址", "comments": "*\n   * 获取归档服务地址\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}]}, {"name": "getHistoryDetail", "description": "获取历史详情", "comments": "*\n   * 获取历史详情\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {String} [args.instanceId] *流程实例ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "String", "name": "args.instanceId", "description": "*流程实例ID"}]}, {"name": "getHistoryList", "description": "获取历史运行列表", "comments": "*\n   * 获取历史运行列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.archivedId] archivedId\n   * @param {Date} [args.endDate] 结束时间\n   * @param {String} [args.instanceId] 主instanceId(根据主历史查子流程历史使用)\n   * @param {int} [args.pageIndex] 页数\n   * @param {int} [args.pageSize] 每页数量\n   * @param {String} [args.processId] *流程ID\n   * @param {Date} [args.startDate] 开始时间\n   * @param {int} [args.status] 状态\n   * @param {String} [args.title] 名称\n   * @param {String} [args.workId] 主workId(根据主历史查子流程历史使用)\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.archivedId", "description": "archivedId"}, {"type": "Date", "name": "args.endDate", "description": "结束时间"}, {"type": "String", "name": "args.instanceId", "description": "主instanceId(根据主历史查子流程历史使用)"}, {"type": "int", "name": "args.pageIndex", "description": "页数"}, {"type": "int", "name": "args.pageSize", "description": "每页数量"}, {"type": "String", "name": "args.processId", "description": "*流程ID"}, {"type": "Date", "name": "args.startDate", "description": "开始时间"}, {"type": "int", "name": "args.status", "description": "状态"}, {"type": "String", "name": "args.title", "description": "名称"}, {"type": "String", "name": "args.workId", "description": "主workId(根据主历史查子流程历史使用)"}]}, {"name": "getInstance", "description": "获取实例基本信息", "comments": "*\n   * 获取实例基本信息\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {String} [args.instanceId] *流程实例ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "String", "name": "args.instanceId", "description": "*流程实例ID"}]}, {"name": "operation", "description": "对应各种操作", "comments": "*\n   * 对应各种操作\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {各种操作类型} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),operationType:操作类型 3撤回 4通过申请 5拒绝申请 6转审 7加签 9提交 10转交 16添加审批人 18催办(integer),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "各种操作类型", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),operationType:操作类型 3撤回 4通过申请 5拒绝申请 6转审 7加签 9提交 10转交 16添加审批人 18催办(integer),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "overrule", "description": "审批-否决", "comments": "*\n   * 审批-否决\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "pass", "description": "审批-通过", "comments": "*\n   * 审批-通过\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "restart", "description": "重新发起", "comments": "*\n   * 重新发起\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "revoke", "description": "撤回", "comments": "*\n   * 撤回\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "signTask", "description": "审批-加签", "comments": "*\n   * 审批-加签\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "submit", "description": "填写动作-提交", "comments": "*\n   * 填写动作-提交\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "taskRevoke", "description": "审批人撤回", "comments": "*\n   * 审批人撤回\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}, {"name": "transfer", "description": "填写动作-填写转给其他人", "comments": "*\n   * 填写动作-填写转给其他人\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {审批动作} {backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}*requestWork\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "审批动作", "name": "requestWork", "description": "{backNodeId:退回节点ID(string),before:加签前后(boolean),data:编辑的控件数据 web端使用(ref),files:附件(string),formData:编辑的控件数据 明道移动端端使用(string),forwardAccountId:转审账号(string),id:id(string),logId:行记录日志id(string),opinion:意见(object),signature:签名(ref),workId:workId(string),}"}]}]}, {"name": "instanceVersion", "description": "工作流-流程实例版本", "data": [{"name": "get2", "description": "获取流程实例流转详情", "comments": "*\n   * 获取流程实例流转详情\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.id] *流程实例id\n   * @param {string} [args.workId] *工作Id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.id", "description": "*流程实例id"}, {"type": "string", "name": "args.workId", "description": "*工作Id"}]}, {"name": "getTodoCount2", "description": "获取未完成数量", "comments": "*\n   * 获取未完成数量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.apkId] 应用id\n   * @param {string} [args.archivedId] 归档服务地址\n   * @param {boolean} [args.complete] 是否是已完成\n   * @param {string} [args.createAccountId] 发起人id\n   * @param {string} [args.endDate] 结束时间 yyyy-MM-dd\n   * @param {string} [args.keyword] null\n   * @param {integer} [args.operationType] 操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType\n   * @param {integer} [args.pageIndex] null\n   * @param {integer} [args.pageSize] null\n   * @param {string} [args.processId] 流程id\n   * @param {string} [args.startAppId] 触发器实体id\n   * @param {string} [args.startDate] 开始时间 yyyy-MM-dd\n   * @param {string} [args.startSourceId] 触发器数据源id\n   * @param {integer} [args.status] 状态  1运行中，2完成，3否决，4 终止 失败\n   * @param {integer} [args.type] 0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.apkId", "description": "应用id"}, {"type": "string", "name": "args.archivedId", "description": "归档服务地址"}, {"type": "boolean", "name": "args.complete", "description": "是否是已完成"}, {"type": "string", "name": "args.createAccountId", "description": "发起人id"}, {"type": "string", "name": "args.endDate", "description": "结束时间 yyyy-MM-dd"}, {"type": "string", "name": "args.keyword", "description": "null"}, {"type": "integer", "name": "args.operationType", "description": "操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType"}, {"type": "integer", "name": "args.pageIndex", "description": "null"}, {"type": "integer", "name": "args.pageSize", "description": "null"}, {"type": "string", "name": "args.processId", "description": "流程id"}, {"type": "string", "name": "args.startAppId", "description": "触发器实体id"}, {"type": "string", "name": "args.startDate", "description": "开始时间 yyyy-MM-dd"}, {"type": "string", "name": "args.startSourceId", "description": "触发器数据源id"}, {"type": "integer", "name": "args.status", "description": "状态  1运行中，2完成，3否决，4 终止 失败"}, {"type": "integer", "name": "args.type", "description": "0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看"}]}, {"name": "getTodoList2", "description": "根据表id行id获取审批流程执行列表", "comments": "*\n   * 根据表id行id获取审批流程执行列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestTodo} {apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestTodo", "name": "request", "description": "{apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}"}]}, {"name": "batch", "description": "批量操作", "comments": "*\n   * 批量操作\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestBatch} {apkId:应用id(string),archivedId:归档服务地址(string),batchOperationType:批量操作类型 可操作动作 3撤回 4通过 5拒绝 6转审 7加签 9提交 10转交 12打印(integer),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),id:单个实例id(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),selects:批量选择(array),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),workId:单个运行id(string),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestBatch", "name": "request", "description": "{apkId:应用id(string),archivedId:归档服务地址(string),batchOperationType:批量操作类型 可操作动作 3撤回 4通过 5拒绝 6转审 7加签 9提交 10转交 12打印(integer),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),id:单个实例id(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),selects:批量选择(array),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),workId:单个运行id(string),}"}]}, {"name": "endInstance", "description": "中止执行", "comments": "*\n   * 中止执行\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.instanceId] *instanceId\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.instanceId", "description": "*instanceId"}]}, {"name": "endInstanceList", "description": "中止执行批量", "comments": "*\n   * 中止执行批量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestStartProcess} {appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestStartProcess", "name": "request", "description": "{appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}"}]}, {"name": "get", "description": "获取流程实例详情", "comments": "*\n   * 获取流程实例详情\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.id] *流程实例id\n   * @param {string} [args.workId] *工作Id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.id", "description": "*流程实例id"}, {"type": "string", "name": "args.workId", "description": "*工作Id"}]}, {"name": "getTodoCount", "description": "获取待处理数量", "comments": "*\n   * 获取待处理数量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.archivedId] archivedId\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.archivedId", "description": "archivedId"}]}, {"name": "getTodoList", "description": "获取待处理列表", "comments": "*\n   * 获取待处理列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestTodo} {apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestTodo", "name": "request", "description": "{apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}"}]}, {"name": "getTodoListFilter", "description": "待处理筛选器", "comments": "*\n   * 待处理筛选器\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestTodo} {apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestTodo", "name": "request", "description": "{apkId:应用id(string),archivedId:归档服务地址(string),complete:是否是已完成(boolean),createAccountId:发起人id(string),endDate:结束时间 yyyy-MM-dd(string),keyword:null(string),operationType:操作类型 默认0 1填写/通过 2加签 3委托 4否决 5取消（非会签用）WorkItemOperationType(integer),pageIndex:null(integer),pageSize:null(integer),processId:流程id(string),startAppId:触发器实体id(string),startDate:开始时间 yyyy-MM-dd(string),startSourceId:触发器数据源id(string),status:状态  1运行中，2完成，3否决，4 终止 失败(integer),type:0:我发起的 -1待处理 包含(3:待填写 4:待审批) 5:待查看(integer),}"}]}, {"name": "getWorkItem", "description": "获取流程实例对应实体", "comments": "*\n   * 获取流程实例对应实体\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.id] *流程实例id\n   * @param {string} [args.workId] *工作Id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.id", "description": "*流程实例id"}, {"type": "string", "name": "args.workId", "description": "*工作Id"}]}, {"name": "resetInstance", "description": "执行历史重试", "comments": "*\n   * 执行历史重试\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.instanceId] *instanceId\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.instanceId", "description": "*instanceId"}]}, {"name": "resetInstanceList", "description": "执行历史重试批量", "comments": "*\n   * 执行历史重试批量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestStartProcess} {appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestStartProcess", "name": "request", "description": "{appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}"}]}]}, {"name": "process", "description": "工作流-流程", "data": [{"name": "addProcess", "description": "创建流程", "comments": "*\n   * 创建流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {增加流程} {companyId:公司ID(string),explain:说明(string),iconColor:图标颜色(string),iconName:图标名称(string),name:流程名称(string),relationId:关联关系(string),relationType:关联的类型(integer),startEventAppType:发起节点app类型：1：从工作表触发 5:循环触发 6:按日期表触发(integer),}*addProcess\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "增加流程", "name": "addProcess", "description": "{companyId:公司ID(string),explain:说明(string),iconColor:图标颜色(string),iconName:图标名称(string),name:流程名称(string),relationId:关联关系(string),relationType:关联的类型(integer),startEventAppType:发起节点app类型：1：从工作表触发 5:循环触发 6:按日期表触发(integer),}"}]}, {"name": "closeStorePush", "description": "关闭流程触发历史推送", "comments": "*\n   * 关闭流程触发历史推送\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.storeId] 推送接收到的id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.storeId", "description": "推送接收到的id"}]}, {"name": "copyProcess", "description": "复制工作流", "comments": "*\n   * 复制工作流\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {CopyProcessRequest} {name:流程名称增加的部分(string),processId:流程ID(string),subProcess:转为子流程(boolean),}*copyProcessRequest\n   * @param {string} [args.name] *复制出来的流程名称后缀\n   * @param {string} [args.processId] *流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "CopyProcessRequest", "name": "copyProcessRequest", "description": "{name:流程名称增加的部分(string),processId:流程ID(string),subProcess:转为子流程(boolean),}"}, {"type": "string", "name": "args.name", "description": "*复制出来的流程名称后缀"}, {"type": "string", "name": "args.processId", "description": "*流程ID"}]}, {"name": "deleteProcess", "description": "删除流程", "comments": "*\n   * 删除流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] *流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "*流程ID"}]}, {"name": "getHistory", "description": "工作流历史版本", "comments": "*\n   * 工作流历史版本\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.pageIndex] 页码\n   * @param {string} [args.pageSize] 条数\n   * @param {string} [args.processId] 流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.pageIndex", "description": "页码"}, {"type": "string", "name": "args.pageSize", "description": "条数"}, {"type": "string", "name": "args.processId", "description": "流程ID"}]}, {"name": "getProcessApiInfo", "description": "PBC流程api", "comments": "*\n   * PBC流程api\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] 发布版流程ID\n   * @param {string} [args.relationId] relationId\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "发布版流程ID"}, {"type": "string", "name": "args.relationId", "description": "relationId"}]}, {"name": "getProcessByControlId", "description": "根据工作表控件获取流程", "comments": "*\n   * 根据工作表控件获取流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.appId] 工作表id\n   * @param {string} [args.companyId] 网络id\n   * @param {string} [args.controlId] 控件id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.appId", "description": "工作表id"}, {"type": "string", "name": "args.companyId", "description": "网络id"}, {"type": "string", "name": "args.controlId", "description": "控件id"}]}, {"name": "getProcessById", "description": "根据流程id查询流程", "comments": "*\n   * 根据流程id查询流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.id] *流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.id", "description": "*流程id"}]}, {"name": "getProcessByTriggerId", "description": "根据按钮获取流程", "comments": "*\n   * 根据按钮获取流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.appId] 表id\n   * @param {string} [args.triggerId] 按钮id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.appId", "description": "表id"}, {"type": "string", "name": "args.triggerId", "description": "按钮id"}]}, {"name": "getProcessConfig", "description": "流程全局配置", "comments": "*\n   * 流程全局配置\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] 流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "流程ID"}]}, {"name": "getProcessListApi", "description": "发布版开启过api的PBC流程列表", "comments": "*\n   * 发布版开启过api的PBC流程列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.relationId] 应用id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.relationId", "description": "应用id"}]}, {"name": "getProcessPublish", "description": "获取版本发布的信息", "comments": "*\n   * 获取版本发布的信息\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.instanceId] 流程实例id\n   * @param {string} [args.processId] 流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.instanceId", "description": "流程实例id"}, {"type": "string", "name": "args.processId", "description": "流程id"}]}, {"name": "getStore", "description": "流程触发历史", "comments": "*\n   * 流程触发历史\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.storeId] 推送接收到的id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.storeId", "description": "推送接收到的id"}]}, {"name": "getTriggerProcessList", "description": "工作流配置 选择部分触发工作流的列表", "comments": "*\n   * 工作流配置 选择部分触发工作流的列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] 流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "流程ID"}]}, {"name": "goBack", "description": "返回上一个版本", "comments": "*\n   * 返回上一个版本\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] *流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "*流程id"}]}, {"name": "move", "description": "流程移到到其他应用下", "comments": "*\n   * 流程移到到其他应用下\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {MoveProcessRequest} {processId:流程id(string),relationId:移动到的应用id(string),}*moveProcessRequest\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "MoveProcessRequest", "name": "moveProcessRequest", "description": "{processId:流程id(string),relationId:移动到的应用id(string),}"}]}, {"name": "publish", "description": "发布工作流", "comments": "*\n   * 发布工作流\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {boolean} [args.isPublish] isPublish\n   * @param {string} [args.processId] *流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "boolean", "name": "args.isPublish", "description": "isPublish"}, {"type": "string", "name": "args.processId", "description": "*流程id"}]}, {"name": "saveProcessConfig", "description": "保存流程全局配置", "comments": "*\n   * 保存流程全局配置\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {保存流程配置} {agents:代理人(array),allowRevoke:允许触发者撤回(boolean),allowTaskRevoke:允许审批人撤回(boolean),allowUrge:允许触发者催办(boolean),callBackType:允许触发者撤回后重新发起 -1: 无配置 0:重新执行  1:直接返回审批节点(integer),dateShowType:日期数据格式1:yyyy-MM-dd HH:mm 6：yyyy-MM-dd HH:mm:ss(integer),debugEvents:调试事件 0开启调试(array),defaultAgent:null(string),defaultCandidateUser:candidateUser获取为空时的默认处理(boolean),defaultErrorCandidateUsers:null(string),disabledPrint:是否关闭系统打印(boolean),endContentType:异常结束返回的contentType(integer),endValue:异常结束返回的配置(string),errorInterval:错误通知间隔时间(integer),errorNotifiers:错误消息通知人(array),executeType:运行方式: 1 并行，2：顺序，3：串行(integer),initiatorMaps:审批人为空处理(object),isSaveVariables:是否只保存流程参数(boolean),pbcConfig:PBC高级设置(ref),permissionLevel:操作时验证用户权限级别 默认 0不需要验证 1查看权限(integer),printIds:打印模版id列表(array),processId:流程ID(string),processIds:编辑版的流程id(array),processVariables:流程参数(array),recordTitle:待办标题(string),required:验证必填字段(boolean),requiredIds:必须审批的节点(array),responseContentType:返回的contentType(integer),revokeNodeIds:通过指定的节点不允许撤回(array),sendTaskPass:触发者不发送通知(boolean),startEventPass:工作流触发者自动通过(boolean),triggerType:触发其他工作流 0 ：允许触发，1：只能触发指定工作流 2：不允许触发(integer),triggerView:触发者查看(boolean),userTaskNullMaps:审批人为空处理(object),userTaskNullPass:审批人为空自动通过(boolean),userTaskPass:审批人自动通过(boolean),value:返回的配置(string),viewNodeIds:可查看意见节点(array),}*saveProcessConfigRequest\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "保存流程配置", "name": "saveProcessConfigRequest", "description": "{agents:代理人(array),allowRevoke:允许触发者撤回(boolean),allowTaskRevoke:允许审批人撤回(boolean),allowUrge:允许触发者催办(boolean),callBackType:允许触发者撤回后重新发起 -1: 无配置 0:重新执行  1:直接返回审批节点(integer),dateShowType:日期数据格式1:yyyy-MM-dd HH:mm 6：yyyy-MM-dd HH:mm:ss(integer),debugEvents:调试事件 0开启调试(array),defaultAgent:null(string),defaultCandidateUser:candidateUser获取为空时的默认处理(boolean),defaultErrorCandidateUsers:null(string),disabledPrint:是否关闭系统打印(boolean),endContentType:异常结束返回的contentType(integer),endValue:异常结束返回的配置(string),errorInterval:错误通知间隔时间(integer),errorNotifiers:错误消息通知人(array),executeType:运行方式: 1 并行，2：顺序，3：串行(integer),initiatorMaps:审批人为空处理(object),isSaveVariables:是否只保存流程参数(boolean),pbcConfig:PBC高级设置(ref),permissionLevel:操作时验证用户权限级别 默认 0不需要验证 1查看权限(integer),printIds:打印模版id列表(array),processId:流程ID(string),processIds:编辑版的流程id(array),processVariables:流程参数(array),recordTitle:待办标题(string),required:验证必填字段(boolean),requiredIds:必须审批的节点(array),responseContentType:返回的contentType(integer),revokeNodeIds:通过指定的节点不允许撤回(array),sendTaskPass:触发者不发送通知(boolean),startEventPass:工作流触发者自动通过(boolean),triggerType:触发其他工作流 0 ：允许触发，1：只能触发指定工作流 2：不允许触发(integer),triggerView:触发者查看(boolean),userTaskNullMaps:审批人为空处理(object),userTaskNullPass:审批人为空自动通过(boolean),userTaskPass:审批人自动通过(boolean),value:返回的配置(string),viewNodeIds:可查看意见节点(array),}"}]}, {"name": "startProcess", "description": "工作表按钮触发流程", "comments": "*\n   * 工作表按钮触发流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestStartProcess} {appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}*startProcess\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestStartProcess", "name": "startProcess", "description": "{appId:表id(string),dataLog:扩展触发值(string),fastFilters:快速筛选条件(array),filterControls:筛选条件(array),isAll:是否全选(boolean),keyWords:搜索框(string),navGroupFilters:分组筛选(array),pushUniqueId:push唯一id 客户端使用(string),sources:行ids(array),triggerId:按钮id(string),viewId:视图id(string),}"}]}, {"name": "startProcessById", "description": "根据流程id手动触发流程", "comments": "*\n   * 根据流程id手动触发流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestStartProcessByProcessId} {dataLog:扩展触发值(string),debugEvents:调试事件(动态人员赋值测试人) 1审批 2短信 3邮件(array),processId:流程id(string),sourceId:行记录id(string),}*startProcess\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestStartProcessByProcessId", "name": "startProcess", "description": "{dataLog:扩展触发值(string),debugEvents:调试事件(动态人员赋值测试人) 1审批 2短信 3邮件(array),processId:流程id(string),sourceId:行记录id(string),}"}]}, {"name": "startProcessByPBC", "description": "根据流程id手动触发PBC流程", "comments": "*\n   * 根据流程id手动触发PBC流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestStartProcessByPBC} {appId:绑定的页面id(string),controls:PBC参数(array),processId:pbc流程id(string),pushUniqueId:push唯一id 客户端使用(string),title:页面按钮名称(string),triggerId:页面按钮id(string),}*startProcess\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestStartProcessByPBC", "name": "startProcess", "description": "{appId:绑定的页面id(string),controls:PBC参数(array),processId:pbc流程id(string),pushUniqueId:push唯一id 客户端使用(string),title:页面按钮名称(string),triggerId:页面按钮id(string),}"}]}, {"name": "updateProcess", "description": "修改流程基本信息", "comments": "*\n   * 修改流程基本信息\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {更新流程信息} {companyId:公司ID(string),explain:说明(string),iconColor:图标颜色(string),iconName:图标名称(string),name:流程名称(string),processId:流程id(string),versionName:版本名称(string),}*updateProcess\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "更新流程信息", "name": "updateProcess", "description": "{companyId:公司ID(string),explain:说明(string),iconColor:图标颜色(string),iconName:图标名称(string),name:流程名称(string),processId:流程id(string),versionName:版本名称(string),}"}]}, {"name": "updateOwner", "description": "转交流程", "comments": "*\n   *  转交流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.companyId] *公司ID\n   * @param {string} [args.id] *流程id\n   * @param {string} [args.owner] *转交人ID\n   * @param {更新拥有者信息} {companyId:公司ID(string),owner:被转交人id(string),processId:流程id(string),}*updateOwner\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.companyId", "description": "*公司ID"}, {"type": "string", "name": "args.id", "description": "*流程id"}, {"type": "string", "name": "args.owner", "description": "*转交人ID"}, {"type": "更新拥有者信息", "name": "updateOwner", "description": "{companyId:公司ID(string),owner:被转交人id(string),processId:流程id(string),}"}]}, {"name": "updateUseStatus", "description": "启用流程或禁用流程", "comments": "*\n   * 启用流程或禁用流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {更新流程状态} {companyId:公司ID(string),enabled:是否启用,是：true,禁用：false(boolean),processId:流程id(string),}*updateUseStatus\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "更新流程状态", "name": "updateUseStatus", "description": "{companyId:公司ID(string),enabled:是否启用,是：true,禁用：false(boolean),processId:流程id(string),}"}]}]}, {"name": "processVersion", "description": "工作流-流程版本", "data": [{"name": "batch", "description": "批量设置(暂停 恢复)流程", "comments": "*\n   * 批量设置(暂停 恢复)流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {流程管理后台批量操作} {hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "流程管理后台批量操作", "name": "request", "description": "{hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}"}]}, {"name": "getDifferenceByCompanyId", "description": "按网络获取流程堆积量", "comments": "*\n   * 按网络获取流程堆积量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.companyId] 网络id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.companyId", "description": "网络id"}]}, {"name": "getDifferenceByProcessId", "description": "获取流程堆积量", "comments": "*\n   * 获取流程堆积量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] 编辑版流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "编辑版流程id"}]}, {"name": "getDifferenceProcessCount", "description": "按网络获取堆积流程总数", "comments": "*\n   * 按网络获取堆积流程总数\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestProcessDifference} {companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}*difference\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestProcessDifference", "name": "difference", "description": "{companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}"}]}, {"name": "getDifferenceProcessList", "description": "按网络获取堆积流程列表", "comments": "*\n   * 按网络获取堆积流程列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestProcessDifference} {companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}*difference\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestProcessDifference", "name": "difference", "description": "{companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}"}]}, {"name": "getDifferenceProcessListByIds", "description": "按历史id获取堆积流程列表", "comments": "*\n   * 按历史id获取堆积流程列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestProcessDifference} {companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}*difference\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestProcessDifference", "name": "difference", "description": "{companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}"}]}, {"name": "getHistoryDifferenceByCompanyId", "description": "按网络获取流程堆积量历史", "comments": "*\n   * 按网络获取流程堆积量历史\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestInstanceIncrementManage} {companyId:网络id(string),endDate:结束时间 yyyy-MM-dd HH:mm:ss(string),startDate:开始时间 yyyy-MM-dd HH:mm:ss(string),}*manage\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestInstanceIncrementManage", "name": "manage", "description": "{companyId:网络id(string),endDate:结束时间 yyyy-MM-dd HH:mm:ss(string),startDate:开始时间 yyyy-MM-dd HH:mm:ss(string),}"}]}, {"name": "getHistoryDifferenceByProcessId", "description": "按流程id获取有堆积的历史", "comments": "*\n   * 按流程id获取有堆积的历史\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] 编辑版流程id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "编辑版流程id"}]}, {"name": "getRouterList", "description": "获取已有通道", "comments": "*\n   * 获取已有通道\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {String} [args.companyId] *网络id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "String", "name": "args.companyId", "description": "*网络id"}]}, {"name": "getWarning", "description": "获取预警配置", "comments": "*\n   * 获取预警配置\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {String} [args.companyId] *网络id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "String", "name": "args.companyId", "description": "*网络id"}]}, {"name": "init", "description": "同步所有应用 所有执行数", "comments": "*\n   * 同步所有应用 所有执行数\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestProcessDifference} {companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestProcessDifference", "name": "request", "description": "{companyId:网络id(string),ids:多个历史id(array),keyword:null(string),pageIndex:null(integer),pageSize:null(integer),sorter:排序 正序{'difference':'ascend'} 倒序{'difference':'descend'}(object),}"}]}, {"name": "remove", "description": "丢弃排队", "comments": "*\n   * 丢弃排队\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {流程管理后台批量操作} {hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "流程管理后台批量操作", "name": "request", "description": "{hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}"}]}, {"name": "reset", "description": "重置排队计数", "comments": "*\n   * 重置排队计数\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {流程管理后台批量操作} {hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "流程管理后台批量操作", "name": "request", "description": "{hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}"}]}, {"name": "updateRouterIndex", "description": "修改选择的通道", "comments": "*\n   * 修改选择的通道\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {流程管理后台批量操作} {hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "流程管理后台批量操作", "name": "request", "description": "{hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}"}]}, {"name": "updateWaiting", "description": "设置暂停流程", "comments": "*\n   * 设置暂停流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {流程管理后台批量操作} {hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "流程管理后台批量操作", "name": "request", "description": "{hours:暂停多少小时(integer),processId:流程id(string),processIds:批量操作 流程ids(array),routerIndex:选择的通道序号(integer),waiting:开启还是关闭 默认true开启暂停(boolean),}"}]}, {"name": "updateWarning", "description": "修改预警配置", "comments": "*\n   * 修改预警配置\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {RequestInstanceWarning} {accountIds:通知人(array),companyId:网络id(string),value:预警值(integer),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "RequestInstanceWarning", "name": "request", "description": "{accountIds:通知人(array),companyId:网络id(string),value:预警值(integer),}"}]}, {"name": "count", "description": "流程列表数量", "comments": "*\n   * 流程列表数量\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.relationId] 应用ID 或者 网络ID\n   * @param {string} [args.relationType] 类型 0 网络，2应用\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.relationId", "description": "应用ID 或者 网络ID"}, {"type": "string", "name": "args.relationType", "description": "类型 0 网络，2应用"}]}, {"name": "getProcessByCompanyId", "description": "网络流程列表", "comments": "*\n   * 网络流程列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.apkId] 应用ID\n   * @param {string} [args.companyId] 网络id\n   * @param {string} [args.enabled] 开启状态 0 全部，1：开启，2：关闭\n   * @param {string} [args.isAsc] 是否升序\n   * @param {string} [args.keyWords] 搜索框\n   * @param {string} [args.pageIndex] 页数\n   * @param {string} [args.pageSize] 条数\n   * @param {string} [args.processListType] 列表类型\n   * @param {string} [args.sortId] 排序字段id\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.apkId", "description": "应用ID"}, {"type": "string", "name": "args.companyId", "description": "网络id"}, {"type": "string", "name": "args.enabled", "description": "开启状态 0 全部，1：开启，2：关闭"}, {"type": "string", "name": "args.isAsc", "description": "是否升序"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "搜索框"}, {"type": "string", "name": "args.pageIndex", "description": "页数"}, {"type": "string", "name": "args.pageSize", "description": "条数"}, {"type": "string", "name": "args.processListType", "description": "列表类型"}, {"type": "string", "name": "args.sortId", "description": "排序字段id"}]}, {"name": "getProcessRole", "description": "流程操作权限", "comments": "*\n   * 流程操作权限\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.relationId] 应用ID 或者 流程ID\n   * @param {string} [args.relationType] 类型 0 网络，2应用\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.relationId", "description": "应用ID 或者 流程ID"}, {"type": "string", "name": "args.relationType", "description": "类型 0 网络，2应用"}]}, {"name": "getProcessUseCount", "description": "获取流程使用数量和执行次数", "comments": "*\n   * 获取流程使用数量和执行次数\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.companyId] 公司ID ,个人传空\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.companyId", "description": "公司ID ,个人传空"}]}, {"name": "list", "description": "流程列表接口", "comments": "*\n   * 流程列表接口\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.keyWords] keyWords\n   * @param {integer} [args.pageIndex] pageIndex\n   * @param {integer} [args.pageSize] pageSize\n   * @param {string} [args.processListType] *流程列表类型：1:工作表触发，2:时间触发，3:其他应用修改本应用，4:应用流程，5:网络流程,100:回收站\n   * @param {string} [args.relationId] 应用ID 或者 网络ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.key<PERSON><PERSON>s", "description": "key<PERSON>ords"}, {"type": "integer", "name": "args.pageIndex", "description": "pageIndex"}, {"type": "integer", "name": "args.pageSize", "description": "pageSize"}, {"type": "string", "name": "args.processListType", "description": "*流程列表类型：1:工作表触发，2:时间触发，3:其他应用修改本应用，4:应用流程，5:网络流程,100:回收站"}, {"type": "string", "name": "args.relationId", "description": "应用ID 或者 网络ID"}]}, {"name": "removeProcess", "description": "切底删除流程", "comments": "*\n   * 切底删除流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] *流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "*流程ID"}]}, {"name": "restoreProcess", "description": "恢复流程", "comments": "*\n   * 恢复流程\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {string} [args.processId] *流程ID\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "string", "name": "args.processId", "description": "*流程ID"}]}]}, {"name": "delegation", "description": "工作流-委托", "data": [{"name": "add", "description": "添加委托", "comments": "*\n   * 添加委托\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {添加委托} {companyId:公司ID(string),endDate:结束时间 yyyy-MM-dd HH:mm(string),startDate:开始时间 yyyy-MM-dd HH:mm(string),trustee:受委托人(string),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "添加委托", "name": "request", "description": "{companyId:公司ID(string),endDate:结束时间 yyyy-MM-dd HH:mm(string),startDate:开始时间 yyyy-MM-dd HH:mm(string),trustee:受委托人(string),}"}]}, {"name": "getList", "description": "获取委托列表", "comments": "*\n   * 获取委托列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}]}, {"name": "getListByPrincipals", "description": "根据委托人获取委托列表", "comments": "*\n   * 根据委托人获取委托列表\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {获取委托列表} {companyId:公司ID(string),principals:多个委托人(array),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "获取委托列表", "name": "request", "description": "{companyId:公司ID(string),principals:多个委托人(array),}"}]}, {"name": "update", "description": "编辑委托", "comments": "*\n   * 编辑委托\n   * @param {Object} args 请求参数\n   * @param {string} [args.access_token] 令牌\n   * @param {编辑委托} {companyId:公司ID(string),endDate:结束时间 yyyy-MM-dd HH:mm(string),id:委托ID(string),startDate:开始时间 yyyy-MM-dd HH:mm(string),status:状态 1正常，0结束(integer),trustee:受委托人(string),}*request\n   * @param {Object} options 配置参数\n   * @param {Boolean} options.silent 是否禁止错误弹层\n   ", "args": [{"type": "string", "name": "args.access_token", "description": "令牌"}, {"type": "编辑委托", "name": "request", "description": "{companyId:公司ID(string),endDate:结束时间 yyyy-MM-dd HH:mm(string),id:委托ID(string),startDate:开始时间 yyyy-MM-dd HH:mm(string),status:状态 1正常，0结束(integer),trustee:受委托人(string),}"}]}]}, {"name": "qiniu", "description": "七牛", "data": [{"name": "getUploadToken", "description": "获取七牛上传 token", "comments": "*\n  * 获取七牛上传 token\n  * @param {Object} args 请求参数\n  * @param {array} args.files 批量上传文件token 请求对象\n  * @param {} args.type\n  * @param {string} args.worksheetId 默认为工作表ID，注：插件使用此ID\n  * @param {string} args.appId\n  * @param {string} args.projectId\n  * @param {string} args.extend 扩展参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.files", "description": "批量上传文件token 请求对象"}, {"type": "string", "name": "args.worksheetId", "description": "默认为工作表ID，注：插件使用此ID"}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.extend", "description": "扩展参数"}]}, {"name": "getFileUploadToken", "description": "获取七牛上传 token", "comments": "*\n  * 获取七牛上传 token\n  * @param {Object} args 请求参数\n  * @param {array} args.files 批量上传文件token 请求对象\n  * @param {} args.type\n  * @param {string} args.worksheetId 默认为工作表ID，注：插件使用此ID\n  * @param {string} args.appId\n  * @param {string} args.projectId\n  * @param {string} args.extend 扩展参数\n  * @param {Object} options 配置参数\n  * @param {Boolean} options.silent 是否禁止错误弹层\n  * @returns {Promise<Boolean, ErrorModel>}\n  *", "args": [{"type": "array", "name": "args.files", "description": "批量上传文件token 请求对象"}, {"type": "string", "name": "args.worksheetId", "description": "默认为工作表ID，注：插件使用此ID"}, {"type": "string", "name": "args.appId", "description": ""}, {"type": "string", "name": "args.projectId", "description": ""}, {"type": "string", "name": "args.extend", "description": "扩展参数"}]}]}]