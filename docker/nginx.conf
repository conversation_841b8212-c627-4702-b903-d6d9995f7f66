server {
    listen 80 default_server;
    root /usr/share/nginx/html/;
    error_page 403 /403.html;
    error_page 404 /404.html;

    location / {
        set $langtag $cookie_i18n_langtag/;
        if ( $cookie_i18n_langtag = 'zh-<PERSON>' ) {
            set $langtag '';
        }

        set $mobile 0;
        if ($http_user_agent ~* "(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|miniprogram|feishu ttwebview|mingdao application") {
            set $mobile 1;
        }
        if ($http_user_agent ~* "^(1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-)") {
            set $mobile 1;
        }
        if ($arg_pc_slide = 'true') {
            set $mobile 1;
        }
        if ($request_uri ~ "portaluser") {
           set $mobile 0;
        }
        # H5 302 跳转
        if ($mobile = 1) {
            rewrite (?i)^/dashboard /mobile/dashboard redirect;
            rewrite (?i)^/myapps /mobile/dashboard redirect;
            rewrite (?i)^/app/lib /mobile/appBox redirect;
            rewrite (?i)^/app/(.+)/(.+)/(.+)/row/(.+) /mobile/record/$1/$2/$3/$4 redirect;
            rewrite (?i)^/app/(.+)/(.+)/row/(.+) /mobile/record/$1/$2/null/$3 redirect;
            rewrite (?i)^/app/(.+)/newrecord/(.+)/(.+) /mobile/addRecord/$1/$2/$3 redirect;
            rewrite (?i)^/app/(.+)/workflowdetail/record/(.+)/(.+) /mobile/processRecord/$2/$3 redirect;
            rewrite (?i)^/app/(.+) /mobile/recordList/$1 redirect;
            rewrite (?i)^/app$ /mobile/dashboard redirect;
            rewrite (?i)^/myprocess/2 /mobile/processInform redirect;
            rewrite (?i)^/myprocess /mobile/processMatters redirect;
        }

        # PC 302 跳转
        if ($mobile = 0) {
            rewrite (?i)^/mobile/dashboard /dashboard redirect;
            rewrite (?i)^/mobile/appBox /app/lib redirect;
            rewrite (?i)^/mobile/app/(.+) /app/$1 redirect;
            rewrite (?i)^/mobile/record/(.+)/(.+)/(.+)/(.+) /app/$1/$2/$3/row/$4 redirect;
            rewrite (?i)^/mobile/addRecord/(.+)/(.+)/(.+) /app/$1/newrecord/$2/$3 redirect;
            rewrite (?i)^/mobile/processRecord/(.+)/(.+) /app/id/workflowdetail/record/$1/$2 redirect;
            rewrite (?i)^/mobile/recordList/(.+) /app/$1 redirect;
            rewrite (?i)^/mobile/customPage/(.+) /app/$1 redirect;
            rewrite (?i)^/mobile/discuss/(.+)/(.+)/(.+)/(.+) /app/$1/$2/$3/row/$4 redirect;
            rewrite (?i)^/mobile/processInform /myprocess/2 redirect;
            rewrite (?i)^/mobile/processMatters /myprocess redirect;
        }

        rewrite (?i)^/recordshare/(.+) /public/workflow/$1 redirect;
        rewrite "(?i)^/form/(\w{32})" /public/form/$1 redirect;
        rewrite (?i)^/printshare/(.+)&&(.+)&&(.+) /public/print/$1&&$2&&$3 redirect;
        rewrite (?i)^/analytics/(.+)/(.+) /app/$2/analytics/$1 redirect;
        rewrite (?i)^/worksheetshare/(.+) /public/record/$1 redirect;

        include rewrite.setting;
    }

    location ^~ /portal {
        set $mobile 0;
        if ($http_user_agent ~* "(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|miniprogram|feishu ttwebview|mingdao application") {
            set $mobile 1;
        }
        if ($http_user_agent ~* "^(1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-)") {
            set $mobile 1;
        }
        if ($arg_pc_slide = 'true') {
            set $mobile 1;
        }
        # H5 302 跳转
        if ($mobile = 1) {
            rewrite (?i)^/portal/app/(.+)/(.+)/(.+)/row/(.+) /portal/mobile/record/$1/$2/$3/$4 redirect;
            rewrite (?i)^/portal/app/(.+)/(.+)/row/(.+) /portal/mobile/record/$1/$2/null/$3 redirect;
            rewrite (?i)^/portal/app/(.+)/newrecord/(.+)/(.+) /portal/mobile/addRecord/$1/$2/$3 redirect;
            rewrite (?i)^/portal/app/(.+)/(.+)/(.+) /portal/mobile/recordList/$1/$2/$3 redirect;
            rewrite (?i)^/portal/app/([0-9a-z-]+)/?$ /portal/mobile/app/$1 redirect;
        }

        # PC 302 跳转
        if ($mobile = 0) {
            rewrite (?i)^/portal/mobile/app/(.+) /portal/app/$1 redirect;
            rewrite (?i)^/portal/mobile/record/(.+)/(.+)/(.+)/(.+) /portal/app/$1/$2/$3/row/$4 redirect;
            rewrite (?i)^/portal/mobile/addRecord/(.+)/(.+)/(.+) /portal/app/$1/newrecord/$2/$3 redirect;
        }
        include portal.rewrite.setting;
    }
}
