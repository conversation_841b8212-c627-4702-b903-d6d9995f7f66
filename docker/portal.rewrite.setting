# 静态文件
rewrite ^/favicon.png /staticfiles/favicon.png break;
rewrite ^/(favicon.*) /staticfiles/$1 break;
rewrite ^/robots.txt /staticfiles/robots.txt break;
rewrite ^/staticfiles/(.*) /staticfiles/$1 break;
rewrite ^/src/common/mdcss/(.*) /staticfiles/mdcss/$1 break;
rewrite ^/src/common/mdjs/(.*) /staticfiles/mdjs/$1 break;
rewrite ^/districtDataConfigFile/(.*) /staticfiles/districtDataConfigFile/$1 break;

# 纯静态页面
rewrite (?i)^/browserupgrade /staticfiles/html/browserupgrade.html break;
rewrite (?i)^/filedownloaddenied /staticfiles/html/downloadDenied.html break;

# 主站 独立入口
rewrite (?i)^(/|/portal/)mobileChart /mobileChart.html break;
rewrite (?i)^(/|/portal/)mobileGunter /mobileGunter.html break;
rewrite (?i)^(/|/portal/)mobileFlowChart /mobileFlowChart.html break;
rewrite (?i)^(/|/portal/)mobile /mobile.html break;
rewrite (?i)^(/|/portal/)public/view /viewshare.html break;
rewrite (?i)^(/|/portal/)public/record /recordshare.html break;
rewrite (?i)^(/|/portal/)public/query /publicQuery.html break;
rewrite (?i)^(/|/portal/)public/print /recordshare.html break;
rewrite (?i)^(/|/portal/)public/form/ /publicworksheet.html break;
rewrite (?i)^(/|/portal/)recordfile /kcshare.html break;
rewrite (?i)^(/|/portal/)rowfile /kcshare.html break;
rewrite (?i)^(/|/portal/)public/workflow /workflowrecordshare.html break;
rewrite (?i)^(/|/portal/)excelfile /excelfile.html break;
rewrite (?i)^(/|/portal/)privacy /privacyOrAgreen.html break;
rewrite (?i)^(/|/portal/)agreen /privacyOrAgreen.html break;

# 登录注册相关
rewrite (?i)^(/|/portal/)login /portalLogin.html break;
rewrite (?i)^(/|/portal/)network /portalLogin.html break;
rewrite (?i)^(/|/portal/)findPwd /portalFindPassWord.html break;
rewrite (?i)^(/|/portal/)logout /logout.html break;

# auth
rewrite (?i)^(/|/portal/)tpauth /portalLogin.html break;
rewrite (?i)^(/|/portal/)wxscanauth /portalLogin.html break;
rewrite (?i)^(/|/portal/)portalTpauth /portalLogin.html break;
rewrite (?i)^(/|/portal/)wxauth(.*) /portalLogin.html break;

# 主站
rewrite (?i)^(/|/portal/)app /index.html break;
rewrite (?i)^(/|/portal/)worksheet /index.html break;
rewrite (?i)^(/|/portal/)ExcelErrorPage /index.html break;
rewrite (?i)^(/|/portal/)print /index.html break;
rewrite (?i)^(/|/portal/)workflow /index.html break;
rewrite (?i)^(/|/portal/)gunterExport /index.html break;
rewrite (?i)^(/|/portal/)printForm /index.html break;
rewrite (?i)^(/|/portal/) /index.html break;
