# 静态文件
rewrite ^/favicon.png /staticfiles/favicon.png break;
rewrite ^/(favicon.*) /staticfiles/$1 break;
rewrite ^/robots.txt /staticfiles/robots.txt break;
rewrite ^/staticfiles/(.*) /staticfiles/$1 break;
rewrite ^/src/common/mdcss/(.*) /staticfiles/mdcss/$1 break;
rewrite ^/src/common/mdjs/(.*) /staticfiles/mdjs/$1 break;
rewrite ^/districtDataConfigFile/(.*) /staticfiles/districtDataConfigFile/$1 break;

# 纯静态页面
rewrite (?i)^/qrcode /staticfiles/html/account-login-qrCode.html break;
rewrite (?i)^/browserupgrade /staticfiles/html/browserupgrade.html break;
rewrite (?i)^/appLibrary /staticfiles/html/appLibrary.html break;
rewrite (?i)^/appShortCut /staticfiles/html/appShortCut.html break;
rewrite (?i)^/filedownloaddenied /staticfiles/html/downloadDenied.html break;
rewrite (?i)^/404 /staticfiles/html/404.html break;

# 主站 独立入口
rewrite (?i)^/apps/kcshare/ /kcshare.html break;
rewrite (?i)^/apps/kcsharelocal /kcshare.html break;
rewrite (?i)^/apps/kcsharefolder/ /kcsharefolder.html break;
rewrite (?i)^/recordfile /kcshare.html break;
rewrite (?i)^/rowfile /kcshare.html break;
rewrite (?i)^/mobileChart /mobileChart.html break;
rewrite (?i)^/mobileLog /mobileLog.html break;
rewrite (?i)^/mobileGunter /mobileGunter.html break;
rewrite (?i)^/nativeEmbedCustomWidget /nativeEmbedCustomWidget.html break;
rewrite (?i)^/mobileFlowChart /mobileFlowChart.html break;
rewrite (?i)^/mobile /mobile.html break;
rewrite (?i)^/public/view /viewshare.html break;
rewrite (?i)^/public/record /recordshare.html break;
rewrite (?i)^/public/query /publicQuery.html break;
rewrite (?i)^/public/chart /statisticschartshare.html break;
rewrite (?i)^/public/page /custompageshare.html break;
rewrite (?i)^/embed/page /custompageembed.html break;
rewrite (?i)^/embed/chart /chartembed.html break;
rewrite (?i)^/public/print /recordshare.html break;
rewrite (?i)^/public/form/ /publicworksheet.html break;
rewrite (?i)^/weixinAuth /weixinAuth.html break;
rewrite (?i)^/worksheetapi/ /worksheetapi.html break;
rewrite (?i)^/public/apidoc /worksheetapi.html break;
rewrite (?i)^/public/workflow /workflowrecordshare.html break;
rewrite (?i)^/m/detail/calendar /calendar-share.html break;
rewrite (?i)^/excelfile /excelfile.html break;

# 企业微信落地
rewrite (?i)^/workweixin /workWeixin.html break;

# 登录注册相关
rewrite ^/$ /login.html break;
rewrite (?i)^/login /login.html break;
rewrite (?i)^/network /login.html break;
rewrite (?i)^/register /login.html break;
rewrite (?i)^/linkInvite /login.html break;
rewrite (?i)^/enterpriseregister /login.html break;
rewrite (?i)^/join /login.html break;
rewrite (?i)^/findPassword /login.html break;
rewrite (?i)^/twofactor /login.html break;
rewrite (?i)^/logout /logout.html break;
rewrite (?i)^/resetPassword /login.html break;

# 注销账户
rewrite (?i)^/cancellation /cancellation.html break;

# 个人账户邮箱验证
rewrite (?i)^/emailvalidate /emailvalidate.html break;

# 重置密码和install安装
rewrite (?i)^/resetPassword /resetPassword.html break;
rewrite (?i)^/privateImageInstall /privateImageInstall.html break;

# auth
rewrite (?i)^/tpAuthPortal /account-login-tpAuthPortal.html break;
rewrite (?i)^/tpAuthApp /account-login-tpAuthApp.html break;
rewrite (?i)^/tpauth /account-login-tpauth.html break;
rewrite (?i)^/auth/workwx /auth-workwx.html break;
rewrite (?i)^/auth/chatTools /auth-chat-tools.html break;
rewrite (?i)^/auth/welink /auth-welink.html break;
rewrite (?i)^/auth/feishu /auth-feishu.html break;
rewrite (?i)^/auth/dingding /auth-dingding.html break;
rewrite (?i)^/sso/dingding  /sso-dingding.html break;
rewrite (?i)^/sso/sso  /sso-sso.html break;
rewrite (?i)^/sso/workweixin  /sso-workweixin.html break;
rewrite (?i)^/legalportal /legalportal.html break;

# 主站
rewrite (?i)^/custom /index.html break;
rewrite (?i)^/app /index.html break;
rewrite (?i)^/feed /index.html break;
rewrite (?i)^/personal /index.html break;
rewrite (?i)^/privateDeployment /index.html break;
rewrite (?i)^/admin /index.html break;
rewrite (?i)^/user_ /index.html break;
rewrite (?i)^/group/groupValidate /index.html break;
rewrite (?i)^/worksheet/ /index.html break;
rewrite (?i)^/print /index.html break;
rewrite (?i)^/printPivotTable /index.html break;
rewrite (?i)^/workflowedit /index.html break;
rewrite (?i)^/dingAppCourse /index.html break;
rewrite (?i)^/weixinAppCourse /index.html break;
rewrite (?i)^/dingSyncCourse /index.html break;
rewrite (?i)^/wxappSyncCourse /index.html break;
rewrite (?i)^/welinkSyncCourse /index.html break;
rewrite (?i)^/feishuSyncCourse /index.html break;
rewrite (?i)^/search /index.html break;
rewrite (?i)^/chat /index.html break;
rewrite (?i)^/chat_window /index.html break;
rewrite (?i)^/workflow /index.html break;
rewrite (?i)^/myprocess /index.html break;
rewrite (?i)^/gunterExport /index.html break;
rewrite (?i)^/embed/view /index.html break;
rewrite (?i)^/integration /index.html break;
rewrite (?i)^/plugin /index.html break;
rewrite (?i)^/integrationConnect /index.html break;
rewrite (?i)^/integrationApi /index.html break;
rewrite (?i)^/favorite /index.html break;
rewrite (?i)^/dashboard /index.html break;
rewrite (?i)^/aggregation /index.html break;
rewrite (?i)^/dataMirrorPreview /index.html break;
rewrite (?i)^/certification /index.html break;
rewrite (?i)^/certificationDetail /index.html break;

# 购买入口
rewrite (?i)^/orderpay /orderPay.html break;

# 商家小票
rewrite (?i)^/receipt /receipt.html break;

# 命令行授权
rewrite (?i)^/cliauth/success /cli-auth-success.html break;
rewrite (?i)^/cliauth /cli-auth-confirm.html break;

# 插件运行环境 iFrame
rewrite (?i)^/widgetview /widget-container.html break;
rewrite (?i)^/freefield /free-field-sandbox.html break;

#HAPAI问答窗口
rewrite (?i)^/hapai /hapAI.html break;
