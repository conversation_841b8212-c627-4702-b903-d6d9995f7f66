import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取账户一览信息
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getAccountListInfo: function (params = {}, options = {}) {
    const payload = `@query:MlcUopUser__findFirst/mobilePhone, email, useDays,
                            userPersonal{ id:sid, fullname, avatar, gender,
                              birthday, companyName, profession, weixin,
                              linkedin, sina },
                            userResumes{ id:sid, type, name, title,
                              description, startDate, endDate }?filter_userId=@biz:userId`;
    return fetchGql(payload, params, options);
   },
   /**
  * 获取当前账户隐私的联系方式
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getMyContactInfo: function (args, options = {}) {

    return mdyAPI('Account', 'GetMyContactInfo', args, options);
  },
  /**
  * 获取个人账户的工作/教育履历
  * @param {Object} params 请求参数
  * @param {Integer} params.type
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getAccountResume: function (params, options = {}) {
    const payload = `@query:MlcUopUserResume__findList/id:sid, type, name, title, description, startDate, endDate?
                            filter_type=${params.type}&orderField=createdAt&orderDir=asc`;

    return fetchGql(payload, params, options);
  },
  /**
  * 删除履历
  * @param {Object} params 请求参数
  * @param {string} params.sid 履历id
  * @param {integer} params.detailType 用户资历类型(1:教育、2:工作)
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  delAccountResume: function (params = {}, options = {}) {
    const payload = '@mutation:MlcUopUserResume__delete';
    return fetchGql(payload, params, options);
  },
  /**
  * 保存个人账户的工作/教育履历
  * @param {Object} params 请求参数
  * @param {string} params.name 雇主/学校名称,null时不修改
  * @param {string} params.title 职位/学位&amp;学历,null时不修改
  * @param {string} params.description 工作内容/核心课程,null时不修改
  * @param {string} params.startDate 开始年月份,null时不修改
  * @param {string} params.endDate 结束年月份,null时不修改
  * @param {} params.type LOV：1:教育；2:工作,null时不修改
  * @param {String} params.autoId 修改的Id
  * @param {string} params.isStill 是否至今仍在 0：是的  1：不是
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editAccountResume: function (params, options = {}) {
    const payload = '@mutation:MlcUopUserResume__save_update/sid';
    return fetchGql(payload, params, options);
  },
  /**
  * 保存个人账户基本信息
  * @param {Object} params 请求参数
  * @param {string} params.fullname 真实姓名
  * @param {string} params.birthdate 出身日期
  * @param {Integer} params.gender 性别
  * @param {string} params.companyName 单位名称
  * @param {string} params.profession 职业
  * @param {string} params.address 地址
  * @param {string} params.linkedin Linkedin账号,null时不修改
  * @param {string} params.sina 新浪微博账号,null时不修改
  * @param {string} params.weixin 微信号,null时不修改
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editAccountBasicInfo: function (params, options = {}) {
    const payload = '@mutation:MlcUopUserPersonal__update/sid';

    return fetchGql(payload, params, options);
  },
  /**
  * 修改头像
  * @param {Object} args 请求参数
  * @param {string} args.fileName 头像文件名
如: https://pic.mingdao.com/UserAvatar/9e4554bb-4fb4-4ef7-abb8-d79fcdbc3f7d.jpg
只需要传: 9e4554bb-4fb4-4ef7-abb8-d79fcdbc3f7d.jpg
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editAccountAvatar: function (args, options = {}) {

     return mdyAPI('Account', 'EditAccountAvatar', args, options);
   },
  /**
  * 获取账户信息（手机/邮箱）
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getAccountInfo: function (params = {}, options = {}) {
    const payload = '@query:MlcUopUser__findFirst/mobilePhone, email, emailVerified';
    return fetchGql(payload, params, options);
  },
  /**
    * 获取扩展登录绑定列表
    * @param {Object} params 请求参数
    * @param {Object} options 配置参数
    * @param {Boolean} options.needError 是否禁止错误弹层
    * @returns {Promise<Boolean, ErrorModel>}
    **/
  getAccountExtBind: function (params = {}, options = {}) {
    const payload = '@query:MlcUopUserExtBind__findList/bindType,nickName,verified';
    return fetchGql(payload, params, options);
  },
  /**
    * 获取扩展登录绑定是否验证
    * @param {Object} params 请求参数
    * @param {Object} options 配置参数
    * @param {Boolean} options.needError 是否禁止错误弹层
    * @returns {Promise<Boolean, ErrorModel>}
    **/
  getExtBindType: function (params = {}, options = {}) {
    const payload = '@query:MlcUopUserExtBind__isBinded';
    return fetchGql(payload, params, options);
  },
  /**
  * 发送网络邮箱绑定的验证邮件
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType 验证码类型（默认腾讯云）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   sendProjectBindEmail: function (args, options = {}) {

     return mdyAPI('Account', 'SendProjectBindEmail', args, options);
   },
  /**
  * 邮箱验证
  * @param {Object} args 请求参数
  * @param {string} args.token token
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   emailValidate: function (args, options = {}) {

     return mdyAPI('Account', 'EmailValidate', args, options);
   },
  /**
  * 修改账户密码
  * @param {Object} params 请求参数
  * @param {string} params.oldPwd 老密码
  * @param {string} params.newPwd 新密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editPwd: function (params, options = {}) {
    const payload = '@mutation:MlcUopUser__editPwd';
    return fetchGql(payload, params, options);
  },
  /**
  * 修改集成账号信息
  * @param {Object} args 请求参数
  * @param {string} args.account 账号
  * @param {string} args.verifyCode 验证码
  * @param {string} args.newPwd 新密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editIntergrationAccount: function (args, options = {}) {

     return mdyAPI('Account', 'EditIntergrationAccount', args, options);
   },
   /**
  * 发送修改帐号验证码
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.account 账号
  * @param {boolean} args.needCheckCode 是否需要验证密码输入
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  sendChangeAccountVerifyCode: function (args, options = {}) {
    return mdyAPI('Account', 'SendChangeAccountVerifyCode', args, options);
  },
   /**
  * 发送验证码
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.account 账号
  * @param {boolean} args.needCheckCode 是否需要验证密码输入
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  sendVerifyCode: function (params, options = {}) {
    const payload = '@mutation:MlcUopUser__sendVerifyCode';
    return fetchGql(payload, params, options);
  },
  /**
  * 验证登录密码
  * @param {Object} params 请求参数
  * @param {string} params.password 密码
  * @param {string} params.ticket 验证码返票据
  * @param {string} params.randStr 票据随机字符串
  * @param {} params.captchaType 验证码类型（本地验证码）
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkAccount: function (params, options = {}) {
    const payload = '@query:Captcha__checkCaptcha';
    return fetchGql(payload, params, options);
  },
  /**
  * 验证登录密码,根据设备（勾选之后1小时内免验证）
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType 验证码类型（默认腾讯云）
  * @param {string} args.password 密码
  * @param {string} args.projectId 组织ID
  * @param {boolean} args.isNoneVerification 是否1小时内该设备免验证
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkAccountIdentity: function (args, options = {}) {

    return mdyAPI('Account', 'CheckAccountIdentity', args, options);
  },
  /**
  * 修改账号
  * @param {Object} params 请求参数
  * @param {string} params.account 账号
  * @param {string} params.verifyCode 验证码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editAccount: function (params, options = {}) {
    const payload = '@mutation:MlcUopUser__editAccount';
    return fetchGql(payload, params, options);
  },
  /**
  * 取消绑定明道云账号
  * @param {Object} args 请求参数
  * @param {string} args.state 状态码
  * @param {} args.tpType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   unBindAccount: function (args, options = {}) {

     return mdyAPI('Account', 'UnBindAccount', args, options);
   },
  /**
  * 退出指定设备已经登录的账号
  * @param {Object} params 请求参数
  * @param {string} params.sessionId 会话ID
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  exitAccount: function (params, options = {}) {
    const payload = '@mutation:LoginApi__killLogin';
    return fetchGql(payload, params, options);
  },
  /**
  * 获取我加入的网络
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectList: function (params = {}, options = {}) {
    const payload = `@query:MlcUopUserInfoNoTenant__findList/sid,
                            userJoinTenant{projectId:tenantId, projectCode:tenantCode, companyName},
                            cardUserRole?filter_userId=@biz:userId`;
    return fetchGql(payload, params, options);
  },
  /**
  * 按token的方式加入网络
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.token Token
  * @param {string} args.companyName 公司名称
  * @param {string} args.jobId 职位id
  * @param {string} args.jobNumber 工号
  * @param {string} args.workSiteId 工作地id
  * @param {string} args.departmentId 部门id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   joinProjectByToken: function (args, options = {}) {

     return mdyAPI('Account', 'JoinProjectByToken', args, options);
   },
  /**
  * 按企业号加入网络
  * @param {Object} params 请求参数
  * @param {string} params.projectCode 企业号
  * @param {string} params.companyName 公司名称
  * @param {string} params.jobId 职位id
  * @param {string} params.jobNumber 工号
  * @param {string} params.workSiteId 工作地id
  * @param {string} params.departmentId 部门id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  joinProjectByCode: function (params, options = {}) {
    const payload = '@mutation:InviteApi__joinProjectByCode';
    return fetchGql(payload, params, options);
  },
  /**
  * 取消加入网络
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   revokedJoinProject: function (args, options = {}) {

     return mdyAPI('Account', 'RevokedJoinProject', args, options);
   },
  /**
  * 同意邀请
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.token 邀请码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   checkJoinProjectByTokenWithCard: function (args, options = {}) {

     return mdyAPI('Account', 'CheckJoinProjectByTokenWithCard', args, options);
   },
  /**
  * 拒绝加入邀请
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.token token
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   refuseJoin: function (args, options = {}) {

     return mdyAPI('Account', 'RefuseJoin', args, options);
   },
  /**
  * 获取网络名片
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getUserCard: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfoNoTenant__get/projectId, contactPhone, jobNumber, workSite{workSiteName},
                            user{fullname, departmentNames, jobNames}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 提醒管理员审核
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {} args.msgType
  * @param {boolean} args.sendIntergrationMsg 是否同步发送到集成消息
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   sendSystemMessageToAdmin: function (args, options = {}) {

     return mdyAPI('Account', 'SendSystemMessageToAdmin', args, options);
   },
  /**
  * 退出组织（密码验证）
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.projectId 网络id
  * @param {string} args.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   validateExitProject: function (args, options = {}) {

     return mdyAPI('Account', 'ValidateExitProject', args, options);
   },
  /**
  * 退出网络（全部交接给小秘书）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.newAdminAccountId 新管理员账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   exitProject: function (args, options = {}) {

     return mdyAPI('Account', 'ExitProject', args, options);
   },
  /**
  * 获取我的邀请信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getMyAuthList: function (args, options = {}) {

     return mdyAPI('Account', 'GetMyAuthList', args, options);
   },
  /**
  * 获取我的未处理的邀请信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getUntreatAuthList: function (args, options = {}) {

     return mdyAPI('Account', 'GetUntreatAuthList', args, options);
   },
  /**
  * 解绑邮件
  * @param {Object} params 请求参数
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  unbindEmail: function (params = {}, options = {}) {
    const payload = '@mutation:MlcUopUser__updateByQuery?filter_userId=@biz:userId';
    params = { email: null };
    return fetchGql(payload, params, options);
  },
  /**
  * 解绑手机
  * @param {Object} params 请求参数
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   unbindMobile: function (params, options = {}) {
    const payload = '@mutation:MlcUopUser__updateByQuery?filter_userId=@biz:userId';
    params = { mobilePhone: null };
    return fetchGql(payload, params, options);
   },
  /**
  * 验证是否 可以注销账户
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   validateLogoffAccount: function (args, options = {}) {
     options.ajaxOptions = Object.assign({}, options.ajaxOptions, { type: 'GET' });
     return mdyAPI('Account', 'ValidateLogoffAccount', args, options);
   },
  /**
  * 申请注销账户
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   applyLogOffAccount: function (args, options = {}) {

     return mdyAPI('Account', 'ApplyLogOffAccount', args, options);
   },
  /**
  * 查询注销状态
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getApplyLogOffAccount: function (args, options = {}) {
     options.ajaxOptions = Object.assign({}, options.ajaxOptions, { type: 'GET' });
     return mdyAPI('Account', 'GetApplyLogOffAccount', args, options);
   },
  /**
  * 取消申请注销
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   cancelLogOffAccount: function (args, options = {}) {

     return mdyAPI('Account', 'CancelLogOffAccount', args, options);
   },
   /**
    * 查询多个用户的信息
    */
  getUserInfoList: function (params, options = {}) {
    const payload = `@query:MlcUopUser__findList/accountId:userId,fullname, avatar
                        ?filter_id__in=${params.ids}`;
    return fetchGql(payload, params, options);
  }
};
