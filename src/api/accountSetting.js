import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取系统设置
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getAccountSettings: function (params, options = {}) {
    const payload = `@query:MlcUopUserSetting__getFieldValue/${params.fieldKeys}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 更新个人系统设置
  * @param {Object} params 请求参数
  * @param {string} params.fieldKey 设置项
  * @param {Object} params.fieldValue
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editAccountSetting: function (params, options = {}) {
    const payload = '@mutation:MlcUopUserSetting__updateFieldValue';
    return fetchGql(payload, params, options);
  },
  /**
  * 自动修改账号语言设置
  * @param {Object} args 请求参数
  * @param {} args.langType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  autoEditAccountLangSetting: function (args, options = {}) {

    return mdyAPI('AccountSetting', 'AutoEditAccountLangSetting', args, options);
  },
  /**
  * 获取用户上次签名
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getSign: function (args, options = {}) {

     return mdyAPI('AccountSetting', 'GetSign', args, options);
   },
  /**
  * 修改用户当前签名
  * @param {Object} args 请求参数
  * @param {string} args.url 签名url
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editSign: function (args, options = {}) {

     return mdyAPI('AccountSetting', 'EditSign', args, options);
   },
};
