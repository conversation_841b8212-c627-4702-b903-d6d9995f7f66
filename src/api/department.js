import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 创建部门（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.departmentName 部门名称
  * @param {string} params.parentId 父部门Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addDepartment: function (params, options = {}) {
    const payload = '@mutation:MlcUopDepartment__save/departmentId,departmentName';
    return fetchGql(payload, params, options);
  },
  /**
  * 编辑部门（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.departmentId 部门Id
  * @param {string} params.departmentName 部门名称
  * @param {string} params.parentId 父部门Id
  * @param {string} params.chargeAccountId 部门负责人
  * @param {array} params.chargeAccountIds 部门负责人列表
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editDepartment: function (params, options = {}) {
    const payload = '@mutation:MlcUopDepartment__update/departmentId';
    return fetchGql(payload, params, options);
   },
  /**
  * 新增或移除单个部门负责人（Admin）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门Id
  * @param {string} args.chargeAccountId 部门负责人
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editDepartmentSingleChargeUser: function (args, options = {}) {

     return mdyAPI('Department', 'EditDepartmentSingleChargeUser', args, options);
   },
  /**
  * 删除部门（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {string} params.departmentId 部门id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteDepartments: function (params, options = {}) {
    const payload = '@mutation:MlcUopDepartment__delete';
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 根据部门父Id获取子部门,departmentId为null表示父部门是网络（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.parentId 上级部门Id（可空，空则 为 顶级部门）
  * @param {integer} params.pageIndex 页码（默认第一页：1）
  * @param {integer} params.pageSize 页大小（默认50）
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedSubDepartments: function (params, options = {}) {
    const payload = `@query:MlcUopDepartment__findPage/items{departmentId, departmentName, haveSubDepartment}
                        ?filter_parentId=${params.parentId ? params.parentId : '__null'}`;
    params.orderBy = [{name: 'createdAt', asc: true}, {name: 'orderNum', asc: true}];
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 搜索(人和部门)（Admin）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {string} args.keywords 查找关键词
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   searchDeptAndUsers: function (args, options = {}) {

     return mdyAPI('Department', 'SearchDeptAndUsers', args, options);
   },
  /**
  * （Admin）指定departmentId，查询整个树状结构（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.departmentId 部门id
  * @param {boolean} params.isGetAll 是否获取所有
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectDepartmentFullTreeByDepartmentId: function (args, options = {}) {
    const payload = '@query:MlcUopDepartment__getDeptFullTreeByDeptId';
    return fetchGql(payload, args, options);
  },
  /**
  * （Admin）获取 指定（单个）部门 至顶而下的树状结构（含该部门所有上级 和 所有下级）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getOneDepartmentFullTree: function (args, options = {}) {

     return mdyAPI('Department', 'GetOneDepartmentFullTree', args, options);
   },
  /**
  * 网络管理 - 按关键词搜索部门（仅搜部门），通用邀请层使用（Admin）
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {string} params.keywords 关键词
  * @param {array} params.filterAccountIds 过滤哪些账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectContactDepartments: function (params, options = {}) {
    const payload = `@query:MlcUopDepartment__findList/departmentId, departmentName, haveSubDepartment,
                           users:relatedUserList{accountId: userId, fullname}
                           ?filter_departmentName__like=%${params.keywords}%`;
    return fetchGql(payload, params, options);
  },
  /**
  * （Admin） 获取部门详细信息
  * @param {Object} params 请求参数
  * @param {string} params.departmentId 部门id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getDepartmentInfo: function (params, options = {}) {
    const payload = `/r/MlcUopDepartment__get?@selection=departmentId,departmentName,parentDepartment:parent{departmentId,departmentName},chargeUsers:userMappingsConnection{items{accountId:userId,user{fullname}}}&_subArgs.chargeUsers.filter_isPrincipal=1`;
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 查询部门
  * @param {Object} params 请求参数
  * @param {string} params.departmentId 部门id
  * @param {string} params.keywords 关键词
  * @param {boolean} params.returnCount 是否返回用户数量
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 每页条数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  searchProjectDepartment2: function (params, options = {}) {
    const payload = `@query:MlcUopDepartment__findPage/items{departmentId, departmentName, haveSubDepartment}
                                                      ?filter_departmentName=${params.keywords}&filter_parentId=__null`;
    return fetchGql(payload, params, options);
  },
  /**
  * 导入部门
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.projectId 网络id
  * @param {string} args.fileName 文件名
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   importDepartmentList: function (args, options = {}) {

     return mdyAPI('Department', 'ImportDepartmentList', args, options);
   },
  /**
  * 网络管理 - 分页获取部门成员简要信息
  * @param {Object} args 请求参数
  * @param {string} args.departmentId 部门id
  * @param {string} args.keywords 关键词
  * @param {array} args.filterAccountIds 过滤哪些账号id
  * @param {integer} args.pageIndex 页码（默认第一页：1）
  * @param {integer} args.pageSize 页大小（默认50）
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedDeptAccountShrotInfos: function (params, options = {}) {
    const payload = `@query:MlcUopUserDepartment__findPage/pageIndex:page, list:items{accountId:userId,
                            user{accountId:userId, fullname}}?filter_departmentId=${params.departmentId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 获取部门的总人数，以及成员详情
  * @param {Object} params 请求参数
  * @param {string} params.departmentId 部门id
  * @param {string} params.keywords 关键词
  * @param {array} params.filterAccountIds 过滤哪些账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectDepartmentUsers: function (params, options = {}) {
    const payload = `@query:MlcUopUserDepartment__findPage/pageIndex:page, allCount:total,list:items{
                            accountId:userId, isPrincipal,
                            base:user{
                              userInfo{
                                jobNumber, createdAt,
                                workSite{workSiteName}
                              },
                              mobilePhone, email, fullname,
                              departmentInfos:relatedDepartmentList{departmentId, departmentName},
                              jobInfos:relatedJobList{jobId, jobName}
                            }}?filter_departmentId=${params.departmentId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 获取网络的总人数以及未加入任何部门成员详情
  * @param {Object} params 请求参数
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getNoDepartmentUsers: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__findPage/allCount:total,
                            list:items{
                              accountId:userId, jobNumber, createdAt,
                              workSite{workSiteName},
                              base:user{
                                mobilePhone, email, fullname,
                                departmentInfos:relatedDepartmentList{departmentId, departmentName},
                                jobInfos:relatedJobList{jobId, jobName}
                              }
                            }?filter_onlyLookNonDepartment=__null`;
    return fetchGql(payload, params, options);
   },
  /**
  * 网络管理 - 部门拖拽
  * @param {Object} params 请求参数
  * @param {string} params.moveToParentId 拖入的 上级部门Id
  * @param {string} params.movingDepartmentId 被拖拽的 部门Id
  * @param {array} params.sortedDepartmentIds 排好序的 部门Ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  moveDepartment: function (params, options = {}) {
    const payload = '@mutation:MlcUopDepartment__moveDepartment';
    return fetchGql(payload, params, options);
  },
  /**
  * 根据部门 Ids 获取完整的部门路径
  * @param {Object} params 请求参数
  * @param {array} params.ids 部门 id 集合
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getDepartmentFullNameByIds: function (params, options = {}) {
    delete params.projectId;
    const payload = '@query:MlcUopDepartment__getDepartmentFullNameByIds/id:departmentId, name:departmentFullName';
    return fetchGql(payload, params, options);
  },
  /**
  * 根据部门父Id获取子部门,departmentId为null表示父部门是网络
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门id
  * @param {boolean} args.returnCount 是否返回用户数量
  * @param {integer} args.pageIndex
  * @param {integer} args.pageSize
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectSubDepartmentByDepartmentId: function (params, options = {}) {
    const payload = `@query:MlcUopDepartment__findPage/items{departmentId, departmentName, haveSubDepartment}
                        ?filter_parentId=${params.departmentId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 获取 成员及下级部门
  * @param {Object} args 请求参数
  * @param {string} args.projectId
  * @param {integer} args.pageIndex
  * @param {integer} args.pageSize
  * @param {boolean} args.onlyMyJoin
  * @param {array} args.filterAccountIds
  * @param {string} args.departmentId 可空（空时 仅返回 用户可见的  最顶级部门）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getMembersAndSubs: function (args, options = {}) {

     return mdyAPI('Department', 'GetMembersAndSubs', args, options);
   },
  /**
  * 分页获取部门列表
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {} args.sortField
  * @param {} args.sortType
  * @param {string} args.keywords 关键词
  * @param {boolean} args.onlyMyJoin 是否仅看自己的部门
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectDepartmentByPage: function (args, options = {}) {

     return mdyAPI('Department', 'GetProjectDepartmentByPage', args, options);
   },
  /**
  * 获取 部门所有下级（树结构，可取全网络）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.parentId
  * @param {string} args.keyword 关键字搜索
  * @param {boolean} args.onlyMyJoin 仅看我加入的部门
  * @param {integer} args.pageIndex
  * @param {integer} args.pageSize
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   pagedDepartmentTrees: function (args, options = {}) {

     return mdyAPI('Department', 'PagedDepartmentTrees', args, options);
   },
  /**
  * 获取 部门所有下级（树结构，可取全网络）
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {string} params.parentId
  * @param {string} params.keyword 关键字搜索
  * @param {boolean} params.onlyMyJoin 仅看我加入的部门
  * @param {integer} params.pageIndex
  * @param {integer} params.pageSize
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedProjectDepartmentTrees: function (params, options = {}) {

    let payload;
    if(params.onlyMyJoin){
      payload = `@query:MlcUopUserDepartment__findPage/total, items{department{departmentId, departmentName, haveSubDepartment}}`;
      options.headers = { ...options.headers, onlyMyJoin: true };
    }else{
      payload = `@query:MlcUopDepartment__findPage/items{departmentId, departmentName, haveSubDepartment}
                        ?filter_parentId=${params.parentId ? params.parentId : '__null'}`;
      params.orderBy = [{name: 'createdAt', asc: true}, {name: 'orderNum', asc: true}];
    }

    return fetchGql(payload, params, options);
  },
  /**
  * 按关键词搜索部门（仅搜部门），通用邀请层使用
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.keywords 关键词
  * @param {array} args.filterAccountIds 过滤哪些账号id
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小（默认200）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getContactProjectDepartments: function (args, options = {}) {

     return mdyAPI('Department', 'GetContactProjectDepartments', args, options);
   },
  /**
  * 获取部门的总人数以及成员详情,受通讯录规则限制
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {string} params.departmentId 部门id
  * @param {string} params.keywords 关键词
  * @param {array} params.filterAccountIds 过滤哪些账号id
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getDepartmentUsers: function (params, options = {}) {
    const payload = `@query:MlcUopUserDepartment__findPage/total, items{accountId:userId, user{accountId:userId, fullname}}
                            ?filter_departmentId=${params.departmentId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 网络管理 - 用户下的部门ids
  * @param {Object} args 请求参数
  * @param {string} args.projectId
  * @param {boolean} args.includePath
  * @param {array} args.accountIds
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getDepartmentsByAccountId: function (args, options = {}) {

     return mdyAPI('Department', 'GetDepartmentsByAccountId', args, options);
   },
  /**
  * 网络管理 - 获取网络的 未加入任何部门成员 详情
（?? 暂不能确定，该方法是否只在 网络管理中使用！！）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.keywords 关键词
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getNotInDepartmentUsers: function (args, options = {}) {

     return mdyAPI('Department', 'GetNotInDepartmentUsers', args, options);
   },
  /**
  * 查询部门
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门id
  * @param {string} args.keywords 关键词
  * @param {boolean} args.returnCount 是否返回用户数量
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   searchDepartment: function (args, options = {}) {

     return mdyAPI('Department', 'SearchDepartment', args, options);
   },
  /**
  * 指定 部门查询
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {integer} args.rangeTypeId 指定查询的 取值范围（仅指定部门==10，指定部门和所有下级部门==20，仅 指定部门的 所有下级部门=30）
  * @param {array} args.appointedDepartmentIds 指定 部门ids（支持 当前用户的部门：user-departments）
  * @param {array} args.appointedUserIds 指定 用户Ids（支持 当前用户：user-self）
  * @param {string} args.keywords 关键词
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 每页条数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   appointedDepartment: function (args, options = {}) {

     return mdyAPI('Department', 'AppointedDepartment', args, options);
   },
  /**
  * 通过组织code加入组织验证Token 获取部门架构
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门id
  * @param {string} args.keywords 关键词
  * @param {boolean} args.returnCount 是否返回用户数量
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 每页条数
  * @param {string} args.token 接口返回的校验Token(必填)
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getDepartmentByJoinProject: function (args, options = {}) {

     return mdyAPI('Department', 'GetDepartmentByJoinProject', args, options);
   },
  /**
  * 查询部门（分页）
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.departmentId 部门id
  * @param {string} args.keywords 关键词
  * @param {boolean} args.returnCount 是否返回用户数量
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 每页条数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  searchDepartment2: function (params, options = {}) {
    const payload = `@query:MlcUopDepartment__findPage/items{departmentId, departmentName, haveSubDepartment}
                            ?filter_departmentName=${params.keywords}&filter_parentId=__null`;
    return fetchGql(payload, params, options);
  },
   /**
  * 复制到当前部门
  * @param {Object} params 请求参数
  * @param {string} params.id 用户 ID
  * @param {string} params.departmentId 部门 ID
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   copyWithDepartment: function (params, options = {}) {
    const payload = '@mutation:MlcUopUserDepartment__batchModify';
    return fetchGql(payload, params, options);
  },
};
