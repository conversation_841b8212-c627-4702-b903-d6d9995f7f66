import { P } from "@antv/g2plot";

export default {
  /**
  * 添加讨论
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 源ID
  * @param {} args.sourceType
  * @param {string} args.message 消息
  * @param {string} args.replyId 被回复的讨论id
  * @param {string} args.attachments 本地附件
  * @param {string} args.knowledgeAtts 知识附件
  * @param {string} args.appId 基础模块或第三方应用的 appId
  * @param {} args.location
  * @param {string} args.extendsId 扩展ID(工作表:appId|viewId)
  * @param {} args.entityType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   addDiscussion: function (args, options = {}) {

     return mdyAPI('Discussion', 'AddDiscussion', args, options);
   },
  /**
  * 获取指定源讨论
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 讨论来源ID
  * @param {} args.sourceType
  * @param {boolean} args.isFocus 是否只返回与当前用户相关的讨论
  * @param {boolean} args.containAttachment 是否只返回包含附件的讨论
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {} args.entityType
  * @param {integer} args.focusType 与我相关类型is_focus传true 0:默认老逻辑全部 1:我发布的 2:我回复别人 3:别人回复我
  * @param {string} args.keywords 关键字
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getDiscussions: function (args, options = {}) {
    return new Promise((resolve, reject) => {
      resolve({ "data": [
        {
            "sourceType": 8,
            "discussionId": "5bb786eb-13fc-4c59-b696-6f8055469c2f",
            "sourceId": "658ea76e13664bfc4099011c|dd21791c-b5d7-42a8-a3d6-1e52af6d0371",
            "message": "应该可以通过！",
            "createTime": "2024-10-02 07:56:25",
            "createAccount": {
                "accountId": "c42de15d-aa34-4922-a280-6bd1f5d9a16a",
                "fullname": "刘先生",
                "avatar": "https://p1.mingdaoyun.cn/UserAvatar/e5382c4d-6e04-4a67-be97-b78d0909aaf2.jpg?imageView2/1/w/48/h/48/q/90",
                "isPortal": false,
                "status": 1
            },
            "replyId": "",
            "projectId": "855f6f44-293a-4b3c-a4a1-e2e18ae32910",
            "appName": "网页端",
            "accountsInMessage": [],
            "newAccounts": [],
            "location": {
                "name": "",
                "address": "",
                "longitude": 0.0,
                "latitude": 0.0
            },
            "attachments": [],
            "isDeleted": false,
            "extendsId": "48fcdf30-d439-44b9-af09-d317a0a755f8|658ea76e13664bfc4099015c"
        }
      ] });
    });
  },
  /**
  * 获取讨论数量
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 讨论来源ID
  * @param {} args.sourceType
  * @param {boolean} args.isFocus 是否只返回与当前用户相关的讨论
  * @param {boolean} args.containAttachment 是否只返回包含附件的讨论
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {} args.entityType
  * @param {integer} args.focusType 与我相关类型is_focus传true 0:默认老逻辑全部 1:我发布的 2:我回复别人 3:别人回复我
  * @param {string} args.keywords 关键字
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getDiscussionsCount: function (args, options = {}) {
    return new Promise((resolve, reject) => {
      resolve({ data: 199 });
    });
   },
  /**
  * 删除讨论
  * @param {Object} args 请求参数
  * @param {string} args.discussionId 讨论id
  * @param {} args.sourceType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   removeDiscussion: function (args, options = {}) {

     return mdyAPI('Discussion', 'RemoveDiscussion', args, options);
   },
  /**
  * 获取单条讨论的msg
  * @param {Object} args 请求参数
  * @param {string} args.discussionId 讨论id
  * @param {} args.sourceType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getDiscussionMsg: function (args, options = {}) {

     return mdyAPI('Discussion', 'GetDiscussionMsg', args, options);
   },
  /**
  * 获取源附件（不分页）
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 讨论id
  * @param {} args.sourceType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getSourceAtts: function (args, options = {}) {

     return mdyAPI('Discussion', 'GetSourceAtts', args, options);
   },
};
