import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取全局依赖的数据
  * @param {Object} params 请求参数
  * @param {string} params.token API token
  * @param {string} params.projectId 组织id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getGlobalMeta: function (params, options = {}) {
     const payload = '/r/Global__GetGlobalMeta';
     return fetchGql(payload, params, options);
   },
  /**
  * 获取系统额外配置信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getSystemConfiguration: function (args, options = {}) {

    return mdyAPI('Global', 'GetSystemConfiguration', args, options);
  },
};
