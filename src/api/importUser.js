import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 导入用户
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.projectId 网络id
  * @param {string} args.fileName 文件名
  * @param {string} args.originalFileName 原始文件名
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   importUserList: function (args, options = {}) {

     return mdyAPI('ImportUser', 'ImportUserList', args, options);
   },
  /**
  * 导入编辑用户
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.projectId 网络id
  * @param {string} args.fileName 文件名
  * @param {string} args.originalFileName 原始文件名
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   importEditUserList: function (args, options = {}) {

     return mdyAPI('ImportUser', 'ImportEditUserList', args, options);
   },
  /**
  * 邀请单个用户
  * @param {Object} params 请求参数
  * @param {string} params.account 账号
  * @param {string} params.accountId 账号id
  * @param {string} params.fullName 姓名
  * @param {string} params.jobNumber 工号
  * @param {string} params.workSiteId 工作地点id
  * @param {string} params.contactPhone 工作电话
  * @param {string} params.departmentIds 用分号分隔，所选部门Id集（第一个为主部门）
  * @param {string} params.jobIds 用分号分隔，所选职位Id集
  * @param {} params.verifyType
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  inviteUser: function (params, options = {}) {
    const payload = '@mutation:InviteApi__inviteUser';
    return fetchGql(payload, params, options);
  },
  /**
  * 重新邀请导入未响应的用户
  * @param {Object} params 请求参数
  * @param {array} params.accounts 账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  reInviteImportUser: function (params, options = {}) {
    const payload = '@mutation:InviteApi__reInviteUser';
    return fetchGql(payload, params, options);
  },
  /**
  * 取消邀请导入的用户
  * @param {Object} params 请求参数
  * @param {array} params.accounts 账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  cancelImportUser: function (params, options = {}) {
    const payload = '@mutation:MlcUopUserJoinRecord__batchModify';
    return fetchGql(payload, params, options);
  },
  /**
  * 查找整个网络的导入用户，未被使用的列表
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getImportUserDetails: function (params, options = {}) {
    const payload = `@query:MlcUopUserJoinRecord__findPage/allCount:total,
                            list:items{
                              accountId: sid,fullName, jobNumber, email, mobilePhone,
                              departmentInfos{departmentId,departmentName},
                              jobInfos{jobId, jobName}, contactPhone, workSite{workSiteName}
                            }
                            ?filter_status=3`;
    return fetchGql(payload, params, options);
  },
  /**
  * 整个网络的导入用户，未激活的总数
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getUnusedInfosByProjectIdCount: function (params, options = {}) {
    const payload = '@query:MlcUopUserJoinRecord__findCount?filter_status=3';
    return fetchGql(payload, params, options);
  },
};
