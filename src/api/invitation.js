import { fetchGql } from 'src/common/axios/index.js';
import moment from 'moment';

export default {
  /**
  * 根据邀请账号生成账户信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getInviteAccountInfo: function (args, options = {}) {

     return mdyAPI('Invitation', 'GetInviteAccountInfo', args, options);
   },
  /**
  * 邀请用户加入某个模块
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 账号id，群组id，任务id，文件夹id，网络id，日程id，项目id
  * @param {} args.fromType
  * @param {array} args.accountIds 数组：[accountId1,accountId2]
  * @param {object} args.accounts 字典:{账号1:姓名1,账号2:姓名2}
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   inviteUser: function (args, options = {}) {

     return mdyAPI('Invitation', 'InviteUser', args, options);
   },
  /**
  * 获取通用邀请链接
  * @param {Object} params 请求参数
  * @param {string} params.sourceId 账号id，群组id，任务id，文件夹id，网络id，日程id，项目id
  * @param {integer} params.inviteFromType 0:好友，1:群组，2:任务，3:文件夹，4:网络，5:日程，6:项目
  * @param {integer} params.linkFromType 1:微信，2:QQ，3:链接，4:二维码，5:钉钉
  * @param {integer} params.hours 多少小时后失效
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getInviteLink: function (params, options = {}) {
    const payload = "@mutation:MlcUopInvitationLink__getInviteLink/linkUrl,sid";
    return fetchGql(payload, params, options);
  },
  /**
  * 二维码链接
  * @param {Object} args 请求参数
  * @param {string} args.sourceId 账号id，群组id，任务id，文件夹id，网络id，日程id，项目id
  * @param {integer} args.fromType 0:好友，1:群组，2:任务，3:文件夹，4:网络，5:日程，6:项目
  * @param {integer} args.linkFromType 1:微信，2:QQ，3:链接，4:二维码，5:钉钉
  * @param {integer} args.size 二维码型号
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getQRCodeInviteLink: function (args, options = {}) {

     return mdyAPI('Invitation', 'GetQRCodeInviteLink', args, options);
   },
  /**
  * 获取当前用户所有有效的链接
  * @param {Object} params 请求参数
  * @param {string} params.sourceId 账号id，群组id，任务id，文件夹id，网络id，日程id，项目id
  * @param {boolean} params.isAll false:是否是我创建的，true:所有
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getAllValidTokenByAccountId: function (params = {}, options = {}) {
    const currDate = moment().format('YYYY-MM-DD HH:mm:ss');
    const payload = `@query:MlcUopInvitationLink__findList/sid, linkUrl, expireTime, userPersonal{fullName}
                              ?filter_expireTime__ge=${currDate}&orderField=createdAt&orderDir=asc`;
    return fetchGql(payload, params, options);
  },
  /**
  * 设置链接失效
  * @param {Object} params 请求参数
  * @param {string} params.id 链接id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateAuthToExpire: function (params, options = {}) {
    const payload = "@mutation:MlcUopInvitationLink__delete";
    return fetchGql(payload, params, options);
   },
  /**
  * 更新链接失效时间
  * @param {Object} params 请求参数
  * @param {array} params.tokens 链接token
  * @param {integer} params.hours 多少小时后失效
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  updateAuthExpireTime: function (params, options = {}) {
    const payload = "@mutation:MlcUopInvitationLink__update/sid";
    return fetchGql(payload, params, options);
  },
};
