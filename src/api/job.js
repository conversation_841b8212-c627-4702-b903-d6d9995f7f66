import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取职位列表
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {string} params.keywords 关键词
  * @param {} params.sortField
  * @param {} params.sortType
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getJobs: function (params, options = {}) {
    const payload = `@query:MlcUopJob__findPage/total, page, list:items{jobId, jobName}
                            ?filter_jobName__like=${params.keywords ? `%${params.keywords}%`: ''}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 添加职位
  * @param {Object} params 请求参数
  * @param {string} params.jobName 职位名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addJob: function (params, options = {}) {
    const payload = '@mutation:MlcUopJob__save/id';
    return fetchGql(payload, params, options);
  },
  /**
  * 修改职位名称
  * @param {Object} params 请求参数
  * @param {string} params.id 职位id
  * @param {string} params.jobName 职位名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editJobName: function (params, options = {}) {
    const payload = '@mutation:MlcUopJob__update/id';
    return fetchGql(payload, params, options);
   },
  /**
  * 删除职位
  * @param {Object} params 请求参数
  * @param {array} params.id 职位Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteJobs: function (params, options = {}) {
    const payload = '@mutation:MlcUopJob__delete';
    return fetchGql(payload, params, options);
  },
  /**
  * 导入职位
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.projectId 网络id
  * @param {string} args.fileName 文件名
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   importJobList: function (args, options = {}) {

     return $.api('Job', 'ImportJobList', args, options);
   },
  /**
  * 获取 职位成员列表
  * @param {Object} params 请求参数
  * @param {string} params.jobId 职位id
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedJobAccounts: function (params, options = {}) {
    const payload = `query MlcUopJob__get($id:String, $offset: Int, $limit: Int){
      job:MlcUopJob__get(id: $id){
        users:userMappingsConnection(offset: $offset, limit: $limit) {
          total, items{accountId: userId, user{fullname, avatar, departments:relatedDepartmentList{departmentId, name:departmentName}}}
        }
      }
    }`;
    return fetchGql(payload, params, options);
  },
  /**
  * 添加用户
  * @param {Object} params 请求参数
  * @param {string} params.jobId 职位id
  * @param {array} params.accountIds 账号Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addJobUser: function (params, options = {}) {
    const payload = '@mutation:MlcUopJob__addManyToManyRelations';
    return fetchGql(payload, params, options);
  },
  /**
  * 删除用户
  * @param {Object} params 请求参数
  * @param {array} params.accountIds 账号Id
  * @param {string} params.jobId
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteJobUsers: function (params, options = {}) {
    const payload = '@mutation:MlcUopJob__removeManyToManyRelations';
    return fetchGql(payload, params, options);
  },
};
