import { fetchGql } from "src/common/axios"

export default {
  /**
  * 获取角色分组列表
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getOrgRoleGroupsByProjectId: function (params, options = {}) {
    const payload = `@query:MlcUopRoleGroup__findList/orgRoleGroupId:roleGroupId,orgRoleGroupName:roleGroupName,sortIndex:orderNum`;
    return fetchGql(payload, params, options);
  },
  /**
  * 添加角色分组
  * @param {Object} params 请求参数
  * @param {string} params.orgRoleGroupId 角色分组Id
  * @param {string} params.orgRoleGroupName 角色名称
  * @param {integer} params.sortIndex 排序
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  upsertOrgRoleGroup: function (params, options = {}) {
    const payload = '@mutation:MlcUopRoleGroup__save_update/roleGroupId';
    return fetchGql(payload, params, options);
  },
  /**
  * 设置组织角色分组排序
  * @param {Object} params 请求参数
  * @param {string} params.orgRoleGroupId 角色分组Id
  * @param {string} params.previousOrgRoleGroupId 上一个排序id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  setSortOrgRoleGroup: function (params, options = {}) {
    const payload = '@mutation:MlcUopRoleGroup__setSortRoleGroup';
    return fetchGql(payload, params, options);
  },
  /**
  * 删除角色分组
  * @param {Object} args 请求参数
  * @param {string} args.orgRoleGroupId 角色分组Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  removeOrgRoleGroup: function (params, options = {}) {
    const payload = '@mutation:MlcUopRoleGroup__delete';
    return fetchGql(payload, params, options);
  },
  /**
  * 获取角色列表
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {string} params.keywords 关键词
  * @param {string} params.orgRoleGroupId 角色分组ID
  * @param {array} params.appointedOrganizeIds 指定的组织角色列表
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getOrganizes: function (params = {}, options = {}) {
    const payload = `@query:MlcUopRole__findPage/list:items{organizeId:roleId, organizeName:roleName, orgRoleGroupId:roleGroupId, roleIdentity, remark}
                                                ?filter_roleGroupId=${params.orgRoleGroupId || '__null'}&filter_roleName=${params.keywords ? `%${params.keywords}%`: ''}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 添加角色
  * @param {Object} params 请求参数
  * @param {string} params.organizeName 角色名称
  * @param {string} params.remark 备注
  * @param {string} params.orgRoleGroupId 角色分组id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addOrganize: function (params, options = {}) {
    const payload = `@mutation:MlcUopRole__save/roleId`;
    return fetchGql(payload, params, options);
  },
  /**
  * 修改角色名称
  * @param {Object} args 请求参数
  * @param {string} args.organizeId 角色id
  * @param {string} args.organizeName 角色名称
  * @param {string} args.remark 角色名称
  * @param {string} args.orgRoleGroupId 角色分组id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  editOrganizeName: function (args, options = {}) {
    const payload = '@mutation:MlcUopRole__update/roleId';
    return fetchGql(payload, args, options);
  },
  /**
  * 设置组织角色排序
  * @param {Object} params 请求参数
  * @param {string} params.organizeId 角色Id
  * @param {string} params.previousOrgRoleId 上一个排序id
  * @param {string} params.moveOrgRoleGroupId 移动的角色Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  setSortOrgRole: function (args, options = {}) {
    const payload = '@mutation:MlcUopRole__setSortRole';
    return fetchGql(payload, args, options);
  },
  /**
  * 删除角色
  * @param {Object} params 请求参数
  * @param {array} params.organizeIds 角色Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteOrganizes: function (params, options = {}) {
    const payload = '@mutation:MlcUopRole__delete';
    return fetchGql(payload, params, options);
  },
  /**
  * 根据账号id获取角色列表
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 用户ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getOrganizesByAccountId: function (args, options = {}) {

     return mdyAPI('Organize', 'GetOrganizesByAccountId', args, options);
   },
  /**
  * 获取 角色成员列表
  * @param {Object} params 请求参数
  * @param {string} params.organizeId 职位id
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedOrganizeAccounts: function (params, options = {}) {
    const payload = `@query:MlcUopUserRole__findPage/allCount:total, list:items{
                            orgRoleChargeDepartments:chargeDepartments{departmentId, departmentName}, accountId:userId,
                            user{fullname, userInfo{jobNumber}, departmentNames, jobNames}
                          }?filter_roleId=${params.organizeId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 组织角色用户设置分管部门
  * @param {Object} params 请求参数
  * @param {string} params.orgRoleId 角色Id
  * @param {string} params.accountId 用户id
  * @param {array} params.departmentIds 部门集合
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  setOrgRoleChargeDepartment: function (params, options = {}) {
    const payload = '@mutation:MlcUopRoleChargeDepartment__setRoleChargeDepartment';
    return fetchGql(payload, params, options);
  },
  /**
  * 添加用户
  * @param {Object} args 请求参数
  * @param {string} args.organizeId 职位id
  * @param {array} args.accountIds 账号Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   addOrganizeUsers: function (args, options = {}) {
    const payload = '@mutation:MlcUopRole__addManyToManyRelations';
    return fetchGql(payload, args, options);
   },
  /**
  * 删除用户
  * @param {Object} params 请求参数
  * @param {string} args.organizeId
  * @param {array} params.accountIds 账号Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteOrganizeUsers: function (args, options = {}) {
    const payload = '@mutation:MlcUopRole__removeManyToManyRelations';
    return fetchGql(payload, args, options);
  },
};
