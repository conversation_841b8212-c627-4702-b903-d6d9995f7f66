export default {

  /**
  * 获取当前服务器秘钥申请列表（废弃）
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getServerLicenseList: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'GetServerLicenseList', args, options);
   },
  /**
  * 获取当前服务器信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getServerInfo: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'GetServerInfo', args, options);
   },
  /**
  * 获取组织列表
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjects: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'GetProjects', args, options);
   },
  /**
  * 绑定组织
  * @param {Object} args 请求参数
  * @param {array} args.projectIds 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   bindProject: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'BindProject', args, options);
   },
  /**
  * 添加授权（安装流程绑定密钥）
  * @param {Object} args 请求参数
  * @param {string} args.licenseCode 授权码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   bindLicenseCode: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'BindLicenseCode', args, options);
   },
  /**
  * 启用授权（废弃）
  * @param {Object} args 请求参数
  * @param {string} args.licenseCode 授权码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   enableLicenseCode: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'EnableLicenseCode', args, options);
   },
  /**
  * 创建管理员账号（安装流程）
  * @param {Object} args 请求参数
  * @param {string} args.name 姓名
  * @param {string} args.email 邮箱
  * @param {string} args.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   addAdmin: function (args, options = {}) {
     
     return mdyAPI('PrivateGuide', 'AddAdmin', args, options);
   },
  /**
  * 创建组织（安装流程）
  * @param {Object} args 请求参数
  * @param {string} args.name 网络名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   addProject: function (args, options = {}) {

     return mdyAPI('PrivateGuide', 'AddProject', args, options);
   },
};
