export default {
  /**
  * 系统配置
  * @param {Object} args 请求参数
  * @param {object} args.settings 配置项
                    OnlyAdminCreateApp:bool，是否只有管理员可以创建应用
                    HideTemplateLibrary:bool，是否隐藏模板库
                    TemplateLibraryTypes:string，开启的模板库类型，1：明道云应用库 2：自建运用库，多个使用英文逗号分隔
                    TemplateLibraryAuditProjectId，string 模板库应用上架允许审核的组织Id
                    ForbidSuites:string，隐藏的协作套件 【1:动态 2:任务 3:日程 4:文件，多个用|分隔】，人事模块强制不显示
                    HideDownloadApp:bool，是否隐藏 APP 下载入口
                    DownloadAppRedirectUrl:string，APP 下载落地页（APP 定制开发的客户需要）
                    HideHelpTip:bool，是否隐藏帮助提示
                    HideRegister:bool，是否隐藏注册入口
                    WorkflowBatchGetDataLimitCount:int，工作流批量数据获取限制条数
                    WorkflowSubProcessDataLimitCount:int，工作流子流程数据源限制条数
                    WorktableBatchOperateDataLimitCount:int，工作表批量数据操作限制条数
                    WorksheetExcelImportDataLimitCount:int，工作表 Excel 数据导入限制条数
                    FileUploadLimitSize:int，文件上传限制大小（单位：M）
                    InstallCaptainUrl:string，安装管理器访问地址
                    ServiceStatusWebhookUrl:string，服务状态通知地址
                    RefreshReportInterval:int，刷新报表时间间隔（单位：秒），默认 300s
                    AllowBindAccountNoVerify:bool，是否允许绑定账号不验证
                    PasswordRegex:string，密码正则表达式
                    PasswordRegexTip:string，密码正则表达式说明文字
                    EnableTwoFactorAuthentication:bool，是否启用双因素认证
                    TwoFactorAuthenticationPriorityType:int，双因素认证优先的账号类型（1：手机号，2：邮箱）
                    FirstLoginResetPassword:bool，首次登录需要重置密码
                    PasswordOverdueDays:int，密码过期时间，0代表不过期
                    EnableDeclareConfirm:bool，是否开启申明确认
                    BrandName:string，品牌名
                    BrandLogo:string，品牌Logo
                    BrandHomeImage:string，品牌背景
                    BrandFavicon:string，品牌 Favicon
                    EnableCreateProject:bool，非平台管理员是否可以创建组织
                    ExportAppWorksheetLimitCount:int，导出的应用工作表总数上限
                    EnableMultipleDevicesUse:bool，是否允许多个设备同步登录
                    WorkWxSelfBuildNoticUrl:string，企业微信申请集成通知地址
                    HideBrandName:bool，是否隐藏品牌名（登录页面）
                    HideBrandLogo:bool，是否隐藏品牌Logo（登录页面）
                    BrandLogoHeight:int，品牌Logo显示高度
                    HideIntegration:bool，是否隐藏集成中心入口
                    HideIntegrationLibrary:bool，是否隐藏集成中心预置API库
                    AppRecycleDays:int，应用回收站保留天数
                    AppItemRecycleDays:int，应用项回收站保留天数
                    WorksheetRowRecycleDays:int，工作表行记录回收站保留天数
                    AppBackupRecycleDays:int，应用备份文件回收站保留天数
                    HideWorkWeixin:bool，隐藏企业微信
                    HideDingding:bool，隐藏钉钉
                    HideFeishu:bool，隐藏飞书
                    HideWelink:bool，隐藏Welink
                    HideWeixin:bool，隐藏微信公众号
                    HideWorksheetControl:string，隐藏的工作表控件（多个使用|分割）
                    EnableMobilePhoneRegister:bool，是否允许手机号注册
                    EnableEmailRegister:bool，是否允许邮箱注册
                    EnableEditAccountInfo:bool，是否允许修改个人账号信息
                    EnableSmsCustomContent:bool，是否支持自定义短信内容的功能
                    &lt;br&gt;EnableBackupWorksheetData:bool，是否支持备份工作表数据
                    &lt;br&gt;EnableFooterInfo:bool，是否显示页脚信息栏
                    &lt;br&gt;FooterThemeColor:int，页脚信息主题色 1:浅色,2:深色
                    &lt;br&gt;CsmWebhookUrl:string，运营事件通知接收地址
                    &lt;br&gt;EnableRequiredStrictVerification:bool，是否开启接口严格鉴权
                    br&gt;EnableSso:bool，是否显示 SSO 登录&lt;
                    br&gt;SsoName:string，SSO 按钮名称&lt;
                    br&gt;SsoWebUrl:string，SSO Web Url&lt;
                    br&gt;SsoAppUrl:string，SSO App Url&lt;
                    br&gt;LoginGotoAppId:string，登录后直接进入的应用Id&lt;
                    br&gt;EnablePromptNewVersion:bool，是否开启新版本提醒&lt;
                    br&gt;HidePlugin:bool，是否隐藏插件入口
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   editSysSettings: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'EditSysSettings', args, options);
   },
  /**
  * 获取组织列表
  * @param {Object} args 请求参数
  * @param {string} args.keywords 关键词
  * @param {integer} args.pageSize 数量
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjects: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'GetProjects', args, options);
   },
   /**
  * 配置 SSO 信息
  * @param {Object} args 请求参数
  * @param {string} args.clientId 客户端Id
  * @param {string} args.clientSecret 客户端密钥
  * @param {string} args.redirectUri 回调地址（废弃，改成服务端固定地址，前端只作为显示）
  * @param {} args.tpType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   setSso: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'SetSso', args, options);
  },
  /**
   * 配置 SSO 启用状态
   * @param {Object} args 请求参数
   * @param {} args.tpType
   * @param {} args.status
   * @param {Object} options 配置参数
   * @param {Boolean} options.silent 是否禁止错误弹层
   * @returns {Promise<Boolean, ErrorModel>}
   **/
  setSsoStatus: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'SetSsoStatus', args, options);
  },
  /**
   * 获取 SSO 配置信息
   * @param {Object} args 请求参数
   * @param {Object} options 配置参数
   * @param {Boolean} options.silent 是否禁止错误弹层
   * @returns {Promise<Boolean, ErrorModel>}
   **/
  getSsoSettings: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'GetSsoSettings', args, options);
  },
  /**
  * 登录页获取 SSO 配置信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getSsonSettingsFroLogin: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'GetSsonSettingsFroLogin', args, options);
  },
  /**
  * 获取组织
  * @param {Object} args 请求参数
  * @param {string} args.projectId 组织id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProject: function (args, options = {}) {

    return mdyAPI('PrivateSysSetting', 'GetProject', args, options);
   },
    /**
  * 获取最新版本信息
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
    getNewVersionInfo: function (args, options = {}) {

      return mdyAPI('PrivateSysSetting', 'GetNewVersionInfo', args, options);
    },
};
