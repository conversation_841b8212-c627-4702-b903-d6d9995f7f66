import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取网络基本信息
  * @param {Object} params 请求参数
  * @param {Object} params 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectInfo: function (params = {}, options = {}) {
    const payload = '@query:MlcUopTenant__findFirst/projectCode: tenantCode, companyName, geographyId, industryId';
    return fetchGql(payload, params, options);
  },
  /**
  * 查询外部项目，获取组织与授权相关信息
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getExternalProjectInfo: function (params = {}, options = {}) {
    const payload = `@query:MlcUopTenant__get/companyName?id=${params.projectId}`;
    return fetchGql(payload, params, options);
},
  /**
  * 保存公司信息
  * @param {Object} params 请求参数
  * @param {string} params.companyName 公司名称
  * @param {integer} args.geographyId 组织所属区域
  * @param {integer} args.industryId 组织所属行业
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  setProjectInfo: function (params, options = {}) {
    const payload = '@mutation:MlcUopTenant__updateByQuery?filter_id=@biz:tenantId';
    return fetchGql(payload, params, options);
  },
  /**
  * 二级域名有效性验证
  * @param {Object} args 请求参数
  * @param {string} args.host host地址
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   checkSubDomain: function (args, options = {}) {

     return mdyAPI('Project', 'CheckSubDomain', args, options);
   },
  /**
  * 获取二级域名页面数据
  * @param {Object} args 请求参数
  * @param {string} args.host Host 地址
  * @param {string} args.projectId 组织Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectSubDomainInfo: function (args, options = {}) {

     return mdyAPI('Project', 'GetProjectSubDomainInfo', args, options);
   },
  /**
  * 获取网络授权辅助信息
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {boolean} args.onlyNormal 是否只需要基本信息
  * @param {boolean} args.onlyUsage 是否只需要用量信息
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectLicenseSupportInfo: function (args, options = {}) {

     return mdyAPI('Project', 'GetProjectLicenseSupportInfo', args, options);
   },
  /**
  * 获取网络有效成员数量
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getEffectiveUsersCount: function (args, options = {}) {

     return mdyAPI('Project', 'GetEffectiveUsersCount', args, options);
   },
  /**
  * 获取网络注销记录
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectLogOff: function (args, options = {}) {

     return mdyAPI('Project', 'GetProjectLogOff', args, options);
   },
  /**
  * 申请注销
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {string} args.reason 原因
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   applyCancelProject: function (args, options = {}) {

     return mdyAPI('Project', 'ApplyCancelProject', args, options);
   },
  /**
  * 取消申请注销
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   removeApplyCancelProject: function (args, options = {}) {

     return mdyAPI('Project', 'RemoveApplyCancelProject', args, options);
   },
  /**
  * 取消高级模式试用授权
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   removeProjectTrialLicense: function (args, options = {}) {

     return mdyAPI('Project', 'RemoveProjectTrialLicense', args, options);
   },
  /**
  * 获取我邀请加入网络成员的历史记录
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 第几页
  * @param {integer} params.pageSize 每页显示记录数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getInvitedUsersJoinProjectLog: function (params, options = {}) {
    const payload = '@query:MlcUopUserJoinRecord__findPage/total,items{fullName, status}';
    return fetchGql(payload, params, options)
  },
  /**
  * 获取网络集成类型
0 代表尚未集成，1代表钉钉自建应用集成，2代表企业微信（第三方），3代表企业微信自建应用，4代表Welink自建应用集成
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectSource: function (args, options = {}) {

     return mdyAPI('Project', 'GetProjectSource', args, options);
   },
  /**
  * 获取网络内待审批用户数量
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectUnauditedUserCount: function (params = {}, options = {}) {
    const payload = '@query:MlcUopUserJoinRecord__findCount?filter_status=1';
    return fetchGql(payload, params, options);
  },
  /**
  * 给用户发送安装手机App或企业客户端通知
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账号Id 数组：[accountId1,accountId2]
  * @param {} args.clientType 通知类型 0：客户端 1：移动端
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   pushInstallClientMsg: function (args, options = {}) {

     return mdyAPI('Project', 'PushInstallClientMsg', args, options);
   },
  /**
  * 绑定微信公众号
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   bindingWeiXin: function (args, options = {}) {

     return mdyAPI('Project', 'BindingWeiXin', args, options);
   },
  /**
  * 获取绑定的微信公众号信息
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getWeiXinBindingInfo: function (args, options = {}) {

     return mdyAPI('Project', 'GetWeiXinBindingInfo', args, options);
   },
  /**
  * 取消绑定微信公众号
  * @param {Object} args 请求参数
  * @param {string} args.appId
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   cancelBindingWeiXin: function (args, options = {}) {

     return mdyAPI('Project', 'CancelBindingWeiXin', args, options);
   },
  /**
  * 微信公众号绑定回调
  * @param {Object} args 请求参数
  * @param {string} args.state 微信回调参数
  * @param {string} args.authCode 微信回调参数
  * @param {string} args.projectId
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   callBackWeiXinBinding: function (args, options = {}) {

     return mdyAPI('Project', 'CallBackWeiXinBinding', args, options);
   },
  /**
  * 私有部署绑定微信公众号
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.appId
  * @param {string} args.appSecret
  * @param {string} args.name 公众号名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   addTpAuthorizerInfo: function (args, options = {}) {

     return mdyAPI('Project', 'AddTpAuthorizerInfo', args, options);
   },
  /**
  * 获取所有专属算力实例
  * @param {Object} args 请求参数
  * @param {string} args.projectId 组织id
  * @param {array} args.resourceIds 资源id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getComputingInstances: function (args, options = {}) {

     return mdyAPI('Project', 'GetComputingInstances', args, options);
   },
  /**
  * 获取专属算力实例详情
  * @param {Object} args 请求参数
  * @param {string} args.projectId 组织id
  * @param {string} args.id 组织id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getComputingInstanceDetail: function (args, options = {}) {

     return mdyAPI('Project', 'GetComputingInstanceDetail', args, options);
   },
  /**
  * 更新专属算力实例名称,删除专属算力
  * @param {Object} args 请求参数
  * @param {string} args.projectId 组织id
  * @param {string} args.instanceId 专属算力实例id
  * @param {string} args.name 专属算力实例name
  * @param {boolean} args.isDelete 是否删除  true表示删除，默认为false
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateComputingInstance: function (args, options = {}) {

     return mdyAPI('Project', 'UpdateComputingInstance', args, options);
   },
  /**
  * 重试创建专属算力实例
  * @param {Object} args 请求参数
  * @param {string} args.projectId
  * @param {string} args.id id
  * @param {string} args.resourceId 资源id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   retryComputingInstance: function (args, options = {}) {

     return mdyAPI('Project', 'RetryComputingInstance', args, options);
   },
   /**
    * 切换网络
    * @param {Object} params 请求参数
    * @param {string} params.projectId 网络id
    * @param {Object} options 配置参数
    * @param {Boolean} options.needError 是否禁止错误弹层
    * @returns {Promise<Boolean, ErrorModel>}
    * */
    switchProject: function (params, options = {}) {
      const payload = '@mutation:MlcUopTenant__switchProject';
      return fetchGql(payload, params, options);
    }
};
