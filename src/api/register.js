import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 发送注册验证码
  * @param {Object} params 请求参数
  * @param {string} params.account 账号邮箱或手机号
  * @param {} params.verifyCodeType 类型：1.短信 2.语音(暂时没用)
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  sendVerifyCode: function (params, options = {}) {
    const payload = '/r/UserRegisterApi__sendVerificationCode';
    return fetchGql(payload, params, options);
  },
  /**
  * 邀请加入网络进行已有账号登录
  * @param {Object} params 请求参数
  * @param {string} params.ticket 验证码返票据
  * @param {string} params.randStr 票据随机字符串
  * @param {} params.captchaType
  * @param {string} params.account 账号邮箱或手机号
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkExistAccount: function (params, options = {}) {
    const payload = '/r/LoginApi__login?@selection=accessToken';
    return fetchGql(payload, params, options);
  },
  /**
  * 定向账号邀请加入网络进行已有账号登录
  * @param {Object} params 请求参数
  * @param {string} params.ticket 验证码返票据
  * @param {string} params.randStr 票据随机字符串
  * @param {} params.captchaType
  * @param {string} params.confirmation token
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkExistAccountByConfirmation: function (params, options = {}) {
    const payload = "/r/LoginApi__checkExistAccountByConfirmation?@selection=accessToken";
    return fetchGql(payload, params, options)
  },
  /**
  * 验证邀请链接
  * @param {Object} params 请求参数
  * @param {string} params.confirmation 加密链接
  * @param {boolean} params.isLink 是否是链接
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkInviteLink: function (args, options = {}) {
    const payload = "/r/InviteApi__checkInviteLink?@selection=fromType,createUserName,sourceName,sourceId,account,isNormal";
    return fetchGql(payload, args, options)
  },
  /**
  * 申请加入网络，根据projectId返回网络设置信息
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   checkJoinLink: function (args, options = {}) {
     
     return mdyAPI('Register', 'CheckJoinLink', args, options);
   },
  /**
  * 如果已有账号加入某个邀请模块(不含加入公司)
  * @param {Object} args 请求参数
  * @param {string} args.ticket 验证码返票据
  * @param {string} args.randStr 票据随机字符串
  * @param {} args.captchaType
  * @param {string} args.password 密码
  * @param {string} args.confirmation token
  * @param {boolean} args.isLink 是否是链接
  * @param {string} args.account 账号邮箱或手机号
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   joinByExistAccount: function (args, options = {}) {
     
     return mdyAPI('Register', 'JoinByExistAccount', args, options);
   },
  /**
  * 创建账号
  * @param {Object} params 请求参数
  * @param {string} params.password 密码
  * @param {string} params.fullName 用户名
  * @param {boolean} params.setSession 是否直接登录进入
  * @param {string} params.verifyCode 验证码
  * @param {string} params.account 账号邮箱或手机号
  * @param {string} params.confirmation token
  * @param {boolean} params.isLink 是否是链接
  * @param {string} params.unionId 第三方账号
  * @param {string} params.state 第三方账号
  * @param {integer} params.tpType 第三方类型
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  createAccount: function (params, options = {}) {
    const payload = "/r/UserRegisterApi__createAccount";
    return fetchGql(payload, params, options)
  },
  /**
  * 设置 用户名称
  * @param {Object} params 请求参数
  * @param {string} params.fullName 真实姓名
  * @param {string} params.email 邮箱
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  setAccountInfo: function (params, options = {}) {
    const payload = "@mutation:MlcUopUser__updateByQuery?filter_userId=@biz:userId";
    return fetchGql(payload, params, options)
  },
  /**
  * 根据企业号加入网络(检验企业号是否正确，含userCard)
  * @param {Object} params 请求参数
  * @param {string} params.projectCode 企业号
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  checkProjectCode: function (params, options = {}) {
    const payload = `@query:MlcUopTenant__checkProjectCode`;
    return fetchGql(payload, params, options)
  },
  /**
  * 创建企业网络
  * @param {Object} params 请求参数
  * @param {string} params.companyName 企业名称
  * @param {string} params.job 职位
  * @param {integer} params.scaleId 规模Id
  * @param {integer} params.industryId 行业id
  * @param {string} params.departmentType 部门类型
  * @param {string} params.jobType 职级类型
  * @param {string} params.code 授权code方式
  * @param {string} params.unionId 第3方
  * @param {string} params.state 第3方
  * @param {} params.tpType 第3方类型
  * @param {string} args.extraDatas 组织额外注册信息
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  createCompany: function (params, options = {}) {
    const payload = "@mutation:MlcUopTenant__save/tenantId";
    return fetchGql(payload, params, options)
  },
  /**
  * 邀请加入网络
  * @param {Object} params 请求参数
  * @param {string} params.confirmation 加密链接
  * @param {string} params.password 密码
  * @param {string} params.verifyCode 验证码
  * @param {string} params.fullName 用户名
  * @param {array} params.jobIds 职位id
  * @param {boolean} params.isLink 是否是链接
  * @param {string} params.companyName 公司名
  * @param {array} params.departmentIds 部门id
  * @param {string} params.workSiteId 工作地id
  * @param {string} params.jobNumber 工号
  * @param {string} params.contactPhone 座机
  * @param {string} params.email 邮箱
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  inviteJoinCompany: function (params, options = {}) {
    const payload = "/r/UserRegisterApi__inviteJoinCompany";
    return fetchGql(payload, params, options)
  },
  /**
  * 主动申请加入网络
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络id
  * @param {string} args.account 账号邮箱或手机号
  * @param {string} args.password 密码
  * @param {string} args.verifyCode 验证码
  * @param {string} args.fullname 用户名
  * @param {string} args.email 邮箱
  * @param {array} args.jobIds 职位id
  * @param {string} args.companyName 公司名
  * @param {array} args.departmentIds 部门id
  * @param {array} args.orgRoleIds 角色id
  * @param {string} args.workSiteId 工作地id
  * @param {string} args.jobNumber 工号
  * @param {string} args.contactPhone 座机
  * @param {string} args.unionId 第3方
  * @param {string} args.state 第3方
  * @param {integer} args.tpType 第3方类型
  * @param {string} args.jobId 兼容字段（不要传值）
  * @param {string} args.departmentId 兼容字段（不要传值）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   applyJoinCompany: function (args, options = {}) {
     
     return mdyAPI('Register', 'ApplyJoinCompany', args, options);
   },
  /**
  * 忘记密码 - 更新密码
  * @param {Object} params 请求参数
  * @param {string} params.account 账号
  * @param {string} params.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  updatePassword: function (params, options = {}) {
    const payload = "/r/UserRegisterApi__updatePassword";
    return fetchGql(payload, params, options)
  },
  /**
  * 获取重置密码触发信息
  * @param {Object} args 请求参数
  * @param {string} args.state 状态码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getResetPasswordTrigerInfo: function (args, options = {}) {
     
     return mdyAPI('Register', 'GetResetPasswordTrigerInfo', args, options);
   },
  /**
  * 根据状态码重置密码
  * @param {Object} args 请求参数
  * @param {string} args.state 状态码
  * @param {string} args.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   resetPasswordByState: function (args, options = {}) {
     
     return mdyAPI('Register', 'ResetPasswordByState', args, options);
   },
};
