import { fetchGql } from "src/common/axios";
export default {
  /**
  * 获取可选择下属
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号id
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageSize 页大小
  * @param {integer} params.pageIndex 页码
  * @param {boolean} params.isSetParent true 设置上级 false 添加下属
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getAllowChooseUsers: function (params, options = {}) {
    let payload = `@query:MlcUopUserInfo__findPage/total,items{accountId: userId, user{accountId: userId, fullname}}
                            ?filter_managerId=__null${params.keywords && `&filter_user.userPersonal.fullName__like=${'%' + params.keywords + '%'}`}${params.filterCustomize && '&filter_userId__ne=@biz:userId'}`;
    return fetchGql(payload, params, options);
   },
  /**
  * 我的下属
  * @param {Object} params 请求参数
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageSize 页大小
  * @param {integer} params.pageIndex 页码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getSubordinateUsers: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__findPageForTree/items{accountId:userId,user{accountId:userId,fullname}}
                            ?filter_managerId=@biz:userId`;
    return fetchGql(payload, params, options);
  },
  /**
  * 分页获取最顶层员工
  * @param {Object} params 请求参数
  * @param {string} params.parentId 父级id，当为0时获取最顶层员工，为null时表示没有分配上下级关系的员工
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 每页条数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedGetAccountList: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__findPage/total, items{accountId: userId,
                            user{accountId: userId, fullname, departmentNames, jobNames}, hasSub, subTotalCount}
                            ?filter_managerId=${params.parentId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 获取员工上级用户信息
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getTreesByAccountId: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__findList/accountId: userId, user{accountId: userId, fullname}, hasSub, subTotalCount,
                            ?filter_userId=${params.accountId}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 获取员工下属
  * @param {Object} params 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  myStructures: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__findFirst/accountId: userId, subTotalCount,
      parent:superior{accountId: userId, fullName, departmentNames, jobNames},
      mySelf:user{accountId: userId, fullName, departmentNames, jobNames},
      subordinates{accountId: userId, fullName, departmentNames, jobNames},
      ?filter_userId=@biz:userId`;
    return fetchGql(payload, params, options);
  },
  /**
  * 添加下属
  * @param {Object} params 请求参数
  * @param {boolean} params.isTop 是否顶级
  * @param {string} params.parentId 上级ID
  * @param {array} params.accountIds 账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addStructure: function (params, options = {}) {
    const payload = `@mutation:MlcUopUserInfo__addStructure`;
    return fetchGql(payload, params, options);
  },
  /**
  * 替换节点
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号id
  * @param {string} params.replacedAccountId 被替换员工
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  replaceUserStructure: function (params, options = {}) {
    const payload = `@mutation:MlcUopUserInfo__replaceUserStructure`;
    return fetchGql(payload, params, options);
  },
  /**
  * 移除上级
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  removeParentID: function (params, options = {}) {
    // const payload = `@mutation:MlcUopUserInfo__updateByQuery?filter_userId=${params.accountId}`;
    // params = { managerId: ''};
    const payload = `@mutation:MlcUopUserInfo__removeParentID`;
    return fetchGql(payload, params, options);
  },
};
