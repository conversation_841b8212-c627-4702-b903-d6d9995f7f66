import { fetchGql } from 'src/common/axios/index.js';

export default {
  /**
  * 获取联系人在组织中的状态
  * @param {Object} params 请求参数
  * @param {string} params.userContact 用户的联系方式
  * @param {string} params.departmentId 部门Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getUserOrgState: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__getUserOrgStatus/userStatus, accountId, mobilePhone, email, fullname, avatar`;
    return fetchGql(payload, params, options);
  },
  /**
  * 分页 获取一般用户列表
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex pageIndex
  * @param {integer} params.pageSize pageSize
  * @param {string} params.keywords 关键词
  * @param {} params.sortUserType
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  pagedNormalUserList: function (params = {}, options = {}) {
    const payload = `@query:MlcUopUserInfo__findPage/allCount:total,
                      list:items{
                        accountId:userId, jobNumber, createdAt,
                        workSite{workSiteName},
                        base:user{
                          mobilePhone, email, fullname,
                          departmentInfos:relatedDepartmentList{departmentId, departmentName},
                          jobInfos:relatedJobList{jobId, jobName}
                        }
                      }`;
    return fetchGql(payload, params, options);
  },
  /**
  * 获取用户列表
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {integer} args.pageIndex pageIndex
  * @param {integer} args.pageSize pageSize
  * @param {string} args.keywords 关键词
  * @param {integer} args.keywordsType 关键词查询 类型（多字段[默认]=0、名称=1、工号=2）
  * @param {} args.userStatus
  * @param {} args.sortField
  * @param {} args.sortType
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getUserList: function (args, options = {}) {
     
     return mdyAPI('User', 'GetUserList', args, options);
   },
  /**
  * 获取 已删除的用户列表
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {integer} args.pageIndex pageIndex
  * @param {integer} args.pageSize pageSize
  * @param {string} args.keywords 关键词
  * @param {integer} args.keywordsType 关键词查询 类型（多字段[默认]=0、名称=1、工号=2）
  * @param {integer} args.sort 排序字段（默认 0=离职时间 倒序）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   pagedRemovedUsers: function (args, options = {}) {
     
     return mdyAPI('User', 'PagedRemovedUsers', args, options);
   },
  /**
  * 获取 待审批 或 已拒绝 的用户
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {} params.userStatus
  * @param {} params.sortField
  * @param {} params.sortType
  * @param {string} params.keywords 关键司
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getApprovalUser: function (params, options = {}) {
    const payload = `@query:MlcUopUserJoinRecord__findPage/allCount:total,
                            list:items{
                              accountId: sid, fullname, jobNumber, email, contactPhone, mobilePhone, status,
                              departmentInfos{departmentId, departmentName},
                              jobInfos{jobId, jobName},
                              workSite{workSiteName},
                              inviteUser{fullname}
                            }?filter_status=${params.userStatus}`;
    return fetchGql(payload, params, options);
  },
  /**
  * 根据AccountId获取用户
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 用户Id集
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getUserListByAccountId: function (args, options = {}) {
     
     return mdyAPI('User', 'GetUserListByAccountId', args, options);
   },
  /**
  * 获取网络内用户信息
  * @param {Object} params 请求参数
  * @param {string} params.id 用户Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getUserCard: function (params, options = {}) {
    let payload = ""
    if(params.typeCursor === 3){

       payload = `@query:MlcUopUserJoinRecord__get/
                          accountId: sid, fullname, jobNumber, email, mobilePhone, status, contactPhone,
                          departmentInfos{departmentId,departmentName},
                          jobInfos{jobId, jobName},
                          workSite{workSiteName},
                          jobs{jobId, jobName},
                          workSites{workSiteId, workSiteName}`;

    } else {

       payload = `@query:MlcUopUser__get/accountId:userId, mobilePhone, email, fullname,
                        userOrgInfo{userInfoId:sid, jobNumber, contactPhone, workSiteId}
                        departmentInfos:relatedDepartmentList{departmentId, departmentName},
                        orgRoles:relatedUserRoleList{id:roleId, name:roleName},
                        jobInfos:relatedJobList{jobId, jobName}
                        jobs{jobId, jobName},
                        workSites{workSiteId, workSiteName}`;
    }

    return fetchGql(payload, params, options);
  },
  /**
  * 获取用户名片层账号基本信息
  * @param {Object} args 请求参数
  * @param {string} args.onProjectId 页面所在组织Id（可空）
  * @param {string} args.appId 所在应用
如果是外部门户用户，则会读取可见字段配置
  * @param {string} args.accountId 账号Id
  * @param {boolean} args.refresh 是否刷新读取
为true则不走缓存
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getAccountBaseInfo: function (params, options = {}) {
    const payload = `@query:MlcUopUser__get/email, mobilePhone, fullname, companyName, profession, avatar`;
    return fetchGql(payload, {id: params.accountId}, options);
   },
  /**
  * 获取用户详细信息
  * @param {Object} args 请求参数
  * @param {string} args.accountId 用户Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getAccountDetail: function (args, options = {}) {
     
     return mdyAPI('User', 'GetAccountDetail', args, options);
   },
  /**
  * mentionsInput 使用，@出来的用户和群组
用于任何实体 AT 快速搜索
  * @param {Object} args 请求参数
  * @param {integer} args.search 搜索类型
1：为用户搜索；2为群组搜索
  * @param {string} args.keywords 关键词
  * @param {string} args.currentProjectId 当前组织（可不传）
主数据所属组织，比如行记录
  * @param {integer} args.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getUsersByKeywords: function (args, options = {}) {
     
     return mdyAPI('User', 'GetUsersByKeywords', args, options);
   },
  /**
  * 获取当前用户经常协作的用户
用于工作表/任务 等默认最常协作联系人
  * @param {Object} args 请求参数
  * @param {integer} args.count 页大小（经常协作用户的数量，不包含 prefixAccountIds 和未指定/我自己的数量）
  * @param {array} args.filterAccountIds 需要排除的帐号
  * @param {array} args.prefixAccountIds 需要插在前面的帐号，排在未指定和我自己后面
  * @param {boolean} args.includeUndefinedAndMyself 是否在前面插入未指定和我自己
  * @param {boolean} args.includeSystemField 是否包含系统预设账户
比如当前用户、当前用户的下属、未指定、工作流、公开表单、API等
  * @param {string} args.projectId 当前网络id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getOftenMetionedUser: function (args, options = {}) {
     
     return mdyAPI('User', 'GetOftenMetionedUser', args, options);
   },
  /**
  * 获取用户列表（projectId不存在加载好友，projectId存在加载公司通讯录）
      当 dataRange=project时 projectId不能为空
      用于通讯录简化弹层
  * @param {Object} args 请求参数
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {string} params.keywords 关键司
  * @param {string} params.projectId 网络Id
  * @param {} params.dataRange
  * @param {string} params.firstLetter 过滤的首字母
  * @param {array} params.filterAccountIds 过滤的需要排除的帐号
  * @param {array} params.prefixAccountIds 需要插在前面的帐号，排在未指定和我自己后面
  * @param {boolean} params.includeUndefinedAndMyself 是否在前面插入未指定和我自己
  * @param {boolean} params.includeMyself 是否在前面插入我自己
  * @param {boolean} params.includeSystemField 是否包含系统预设账户:比如当前用户、当前用户的下属、未指定、工作流、公开表单、API等
  * @param {array} params.appointedAccountIds 指定的账户列表
  * @param {array} params.appointedDepartmentIds 指定的部门列表
  * @param {array} params.appointedOrganizeIds 指定的组织角色列表
  * @param {boolean} params.takeTotalCount 是否获取 总数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getContactUserList: function (args, options = {}) {

    return mdyAPI('User', 'GetContactUserList', args, options);
  },
  /**
  * 获取网络下已离职的用户信息
  * @param {Object} args 请求参数
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {string} args.keywords 关键司
  * @param {string} args.projectId 网络Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectResignedUserList: function (args, options = {}) {
     
     return mdyAPI('User', 'GetProjectResignedUserList', args, options);
   },
  /**
  * 网络管理 - 获取网络下用户列表（projectId不存在加载好友，projectId存在加载公司通讯录）
      当 dataRange=project时 projectId不能为空
      用于网络管理通讯录简化弹层
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 页码
  * @param {integer} params.pageSize 页大小
  * @param {string} params.keywords 关键司
  * @param {string} params.projectId 网络Id
  * @param {} params.dataRange
  * @param {string} params.firstLetter 过滤的首字母
  * @param {array} params.filterAccountIds 过滤的需要排除的帐号
  * @param {array} params.prefixAccountIds 需要插在前面的帐号，排在未指定和我自己后面
  * @param {boolean} params.includeUndefinedAndMyself 是否在前面插入未指定和我自己
  * @param {boolean} params.includeMyself 是否在前面插入我自己
  * @param {boolean} params.includeSystemField 是否包含系统预设账户,例如:当前用户、当前用户的下属、未指定、工作流、公开表单、API等
  * @param {array} params.appointedAccountIds 指定的账户列表
  * @param {array} params.appointedDepartmentIds 指定的部门列表
  * @param {array} params.appointedOrganizeIds 指定的组织角色列表
  * @param {boolean} params.takeTotalCount 是否获取 总数
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getProjectContactUserList: function (params, options = {}) {
    const payload = `@query:MlcUopUserInfo__getProjectContactUserList/total, page,
                            items{accountId:userId, user{accountId:userId, fullname}}`;
    const attrs = { 'v_contactBean': { ...params }, 'filter_userId__notIn': params.filterCustomize };
    options = {...options, stdAction: 'findList'}
    return fetchGql(payload, attrs, options);
  },
  /**
  * 根据人员筛选条件获取人员列表
包括：指定部门、指定人员、指定组织角色，或者动态范围
  * @param {Object} args 请求参数
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {string} args.keywords 关键司
  * @param {string} args.projectId 网络Id
  * @param {} args.dataRange
  * @param {string} args.firstLetter 过滤的首字母
  * @param {array} args.filterAccountIds 过滤的需要排除的帐号
  * @param {array} args.prefixAccountIds 需要插在前面的帐号，排在未指定和我自己后面
  * @param {boolean} args.includeUndefinedAndMyself 是否在前面插入未指定和我自己
  * @param {boolean} args.includeMyself 是否在前面插入我自己
  * @param {boolean} args.includeSystemField 是否包含系统预设账户
比如当前用户、当前用户的下属、未指定、工作流、公开表单、API等
  * @param {array} args.appointedAccountIds 指定的账户列表
  * @param {array} args.appointedDepartmentIds 指定的部门列表
  * @param {array} args.appointedOrganizeIds 指定的组织角色列表
  * @param {boolean} args.takeTotalCount 是否获取 总数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getProjectContactUserListByApp: function (args, options = {}) {
     
     return mdyAPI('User', 'GetProjectContactUserListByApp', args, options);
   },
  /**
  * 获取已离职的用户
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {integer} args.pageIndex 页码
  * @param {integer} args.pageSize 页大小
  * @param {string} args.keywords 关键词
  * @param {string} args.firstLetter 首字母
  * @param {array} args.filterAccountIds 过滤哪些账号
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   getLeaveUserList: function (args, options = {}) {
     
     return mdyAPI('User', 'GetLeaveUserList', args, options);
   },
  /**
  * 设置用户信息
  * @param {Object} args 请求参数
  * @param {string} args.fullname 姓名
  * @param {string} args.email 邮箱
  * @param {string} args.mobilePhone 手机号
  * @param {string} args.projectId 组织编号
  * @param {string} args.accountId 账号Id
  * @param {string} args.companyName 公司名
  * @param {string} args.jobNumber Job号
  * @param {string} args.contactPhone 联系号码
  * @param {string} args.workSiteId 工作地点
  * @param {array} args.departmentIds 部门Id（第一个为主部门）
  * @param {array} args.jobIds 职位Id
  * @param {array} args.orgRoleIds 组织角色ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateUser: function (args, options = {}) {
     
     return mdyAPI('User', 'UpdateUser', args, options);
   },
  /**
  * 设置用户信息
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号Id
  * @param {string} params.companyName 公司名
  * @param {string} params.jobNumber Job号
  * @param {string} params.contactPhone 联系号码
  * @param {string} params.workSiteId 工作地点
  * @param {array} params.departmentIds 部门（第一个为主部门）
  * @param {array} params.jobIds 职位ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  updateUserCard: function (params, options = {}) {
    const payload = `@mutation:MlcUopUser__update/userId`;
    return fetchGql(payload, params, options);
  },
  updateUserInfo: function (params, options = {}) {
    const payload = `@mutation:MlcUopUser__updateUserCard`;
    return fetchGql(payload, params, options);
  },
  /**
  * 移除用户
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {string} args.accountId 账户Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   removeUser: function (args, options = {}) {
     
     return mdyAPI('User', 'RemoveUser', args, options);
   },
  /**
  * 移除用户
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账户Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   removeUsers: function (args, options = {}) {
     
     return mdyAPI('User', 'RemoveUsers', args, options);
   },
  /**
  * 恢复用户
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {string} args.accountId 账户Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   recoveryUser: function (args, options = {}) {
     
     return mdyAPI('User', 'RecoveryUser', args, options);
   },
  /**
  * 审批同意用户加入公司
  * @param {Object} params 请求参数
  * @param {string} params.inviteId 邀请Id
  * @param {string} params.departmentId 部门Id
  * @param {array} params.departmentIds 新部门Ids（第一个为主部门）
  * @param {string} params.workSiteId 工作地点
  * @param {string} params.jobId 职位
  * @param {array} params.jobIds 职位Ids
  * @param {array} params.orgRoleIds 组织角色Id
  * @param {string} params.jobNumber 工号
  * @param {string} params.contactPhone 联系号码
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  agreeUserJoin: function (params, options = {}) {
    const payload = `@mutation:InviteApi__agreeUserJoin`;
    return fetchGql(payload, params, options);
  },
  /**
  * 审批同意用户加入公司
  * @param {Object} params 请求参数
  * @param {array} params.inviteIds 邀请Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  agreeUsersJoin: function (params, options = {}) {
    const payload = `@mutation:InviteApi__agreeUsersJoin`;
    return fetchGql(payload, params, options);
  },
  /**
  * 批量 审批拒绝用户加入公司
  * @param {Object} params 请求参数
  * @param {string} params.projectId 网络Id
  * @param {array} params.accountIds 账户Id
  * @param {string} params.refuseMessage 拒绝消息
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  refuseUsersJoin: function (params, options = {}) {
    const payload = `@mutation:MlcUopUserJoinRecord__batchModify`;
    return fetchGql(payload, params, options);
  },
  /**
  * 重置员工密码
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {string} args.accountId 账号Id
  * @param {string} args.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   resetPassword: function (args, options = {}) {
     
     return mdyAPI('User', 'ResetPassword', args, options);
   },
  /**
  * 批量重置员工密码
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账号Id
  * @param {string} args.password 密码
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   batchResetPassword: function (args, options = {}) {
     
     return mdyAPI('User', 'BatchResetPassword', args, options);
   },
  /**
  * 批量更新用户所在部门
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账户Ids
  * @param {array} args.departmentIds 部门ids 第一个为主部门
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateDepartmentForUsers: function (args, options = {}) {
     
     return mdyAPI('User', 'UpdateDepartmentForUsers', args, options);
   },
  /**
  * 批量更新用户职位
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账户Ids
  * @param {array} args.jobIds 职位ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateJobForUsers: function (args, options = {}) {
     
     return mdyAPI('User', 'UpdateJobForUsers', args, options);
   },
  /**
  * 批量更新用户工作地点
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {array} args.accountIds 账户Ids
  * @param {string} args.worksiteId 工作地点id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   updateWorkSiteForUsers: function (args, options = {}) {
     
     return mdyAPI('User', 'UpdateWorkSiteForUsers', args, options);
   },
  /**
  * 根据ProjectId检测当前用户是不是网络管理员
  * @param {Object} args 请求参数
  * @param {string} args.projectId 网络Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   validateUserIsProjectAdmin: function (args, options = {}) {
     
     return mdyAPI('User', 'ValidateUserIsProjectAdmin', args, options);
   },
  /**
  * 检查当前用户是否有好友
  * @param {Object} args 请求参数
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
   validateUserHaveFriend: function (args, options = {}) {
     
     return mdyAPI('User', 'ValidateUserHaveFriend', args, options);
   },
};
