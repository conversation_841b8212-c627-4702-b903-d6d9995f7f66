export default {
  /**
  * 添加缓存数据
  * @param {Object} args 请求参数
  * @param {string} args.key 缓存的键
  * @param {string} args.value 缓存的值
  * @param {string} args.expireTime 过期时间，单位为秒（不需要的时候不用传）
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  add: function (args, options = {}) {
    try {
      const { key, value, expireTime = 3600 } = args;
      const cacheData = {
        value: value,
        timestamp: Date.now(),
        expireTime: expireTime ? expireTime * 1000 : null, // 过期时间转化为毫秒
      };
      localStorage.setItem(key, JSON.stringify(cacheData));
      return Promise.resolve(true);
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
  * 清理缓存数据
  * @param {Object} args 请求参数
  * @param {string} args.key
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  clear: function (args, options = {}) {
    try {
      const { key } = args;
      localStorage.removeItem(key);
      return Promise.resolve(true);
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
  * 批量清理缓存数据
  * @param {Object} args 请求参数
  * @param {array} args.keys
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  clears: function (args, options = {}) {
    try {
      const { keys } = args;
      keys.forEach(key => {
        localStorage.removeItem(key);
      });
      return Promise.resolve(true);
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
  * 获取缓存数据
  * @param {Object} args 请求参数
  * @param {string} args.key
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  get: function (args, options = {}) {
    try {
      const { key } = args;
      const cachedData = localStorage.getItem(key);

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        // 检查是否已过期
        if (parsedData.expireTime && Date.now() > parsedData.timestamp + parsedData.expireTime) {
          localStorage.removeItem(key); // 如果过期则删除
          return Promise.resolve(null);
        }
        return Promise.resolve({
          data: parsedData.value,
        });
      }

      return Promise.resolve(null);
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
  * 批量获取缓存数据
  * @param {Object} args 请求参数
  * @param {array} args.keys
  * @param {Object} options 配置参数
  * @param {Boolean} options.silent 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  gets: function (args, options = {}) {
    try {
      const { keys } = args;
      const results = {};

      keys.forEach(key => {
        const cachedData = localStorage.getItem(key);

        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          // 检查是否已过期
          if (parsedData.expireTime && Date.now() > parsedData.timestamp + parsedData.expireTime) {
            localStorage.removeItem(key); // 如果过期则删除
            results[key] = null;
          } else {
            results[key] = parsedData.value;
          }
        } else {
          results[key] = null;
        }
      });

      return Promise.resolve(results);
    } catch (error) {
      return Promise.reject(error);
    }
  },
};
