import { fetchGql } from 'src/common/axios/index.js';
import {convertToOffsetLimit} from 'src/util/paginationConvert.js';

export default {
  /**
  * 获取工作地点列表
  * @param {Object} params 请求参数
  * @param {integer} params.pageIndex 分页码
  * @param {integer} params.pageSize 页大小
  * @param {string} params.keywords 关键词
  * @param {} params.sortField
  * @param {} params.sortType
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getWorkSites: function (params, options = {}) {
    const query = `@query:MlcUopWorkSite__findPage/total, page, items{workSiteName, workSiteId, userCount:workSiteUserInfosConnection{total}}
                          ?filter_workSiteName__like=${params.keywords ? `%${params.keywords}%`: ''}`;
    return fetchGql(query, params, options);

  },
  /**
  * 添加工作地点
  * @param {Object} params 请求参数
  * @param {string} params.workSiteName 工作地点名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addWorkSite: function (params, options = {}) {
    const query = '@mutation:MlcUopWorkSite__save/id';
    return fetchGql(query, params, options);
  },
  /**
  * 修改工作地点名称
  * @param {Object} params 请求参数
  * @param {string} params.workSiteId 工作地点Id
  * @param {string} params.workSiteName 工作地点名称
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  updateWorkSiteName: function (params, options = {}) {
    const query = '@mutation:MlcUopWorkSite__update/id';
    return fetchGql(query, params, options);
  },
  /**
  * 删除工作地点
  * @param {Object} params 请求参数
  * @param {array} params.ids 删除的工作地点Ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteWorkSites: function (params, options = {}) {
    const query = '@mutation:MlcUopWorkSite__batchDelete';
    return fetchGql(query, params, options);
  },
  /**
  * 获取用户列表
  * @param {Object} params 请求参数
  * @param {string} params.workSiteId 工作地点Id
  * @param {string} params.keywords 关键词
  * @param {integer} params.pageIndex 分页码
  * @param {integer} params.pageSize 页大小
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  getWorkSiteUsers: function (params, options = {}) {

    const payload = `query MlcUopWorkSite__get($id:String, $subQuery:Map){
      workSite:MlcUopWorkSite__get(id: $id){
        users:workSiteUserInfosConnection(query: $subQuery) {
          total, items{accountId: userId, user{fullName}}
        }
      }
    }`;

    params = {
      id: params.id,
      subQuery: {
        ...convertToOffsetLimit(params.pageIndex, params.pageSize),
        ...(params.keywords && {
          filter: {
            "$type": "like",
            "name": "user.userPersonal.fullName",
            "value": `%${params.keywords}%`,
          }
        })
      }
    }
    return fetchGql(payload, params, options);
  },
  /**
  * 添加用户
  * @param {Object} params 请求参数
  * @param {string} params.workSiteId 工作地点Id
  * @param {array} params.accountIds 账号Ids
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  addWorkSiteUser: function (params, options = {}) {
    const payload = `@mutation:MlcUopUserInfo__updateByQuery?filter_userId__in=${params.accountIds}`;
    params = {
      workSiteId: params.workSiteId,
    };
    return fetchGql(payload, params, options);
  },
  /**
  * 删除用户
  * @param {Object} params 请求参数
  * @param {string} params.accountId 账号Id
  * @param {Object} options 配置参数
  * @param {Boolean} options.needError 是否禁止错误弹层
  * @returns {Promise<Boolean, ErrorModel>}
  **/
  deleteWorkSiteUser: function (params, options = {}) {
    const payload = `@mutation:MlcUopUserInfo__updateByQuery?filter_userId=${params.accountId}`;
    params = {
      workSiteId: null,
    };
    return fetchGql(payload, params, options);
  },
};
