import { isBoolean, isInteger, isNumber, isArray, isPlainObject, isString } from 'lodash-es';
import { splitPrefixUrl } from './utils';

export function handleGraphQL(config, payload, params) {
  const [type, body] = splitPrefixUrl(payload) || [];
  if (type === 'query' || type === 'mutation' || type === 'subscription') {
    config.url = config.baseUrl + '/graphql';
    handleGraphQLUrl(type, body, config, params);
  } else if (payload.startsWith('/r/')) {
    config.url = config.baseUrl + payload;
    config.data = { ...params };
  } else {
    // 之前的条件为：else if (config.url.endsWith('/graphql') || config.url.indexOf('/graphql?') >= 0)
    config.url = config.baseUrl + '/graphql';
    config.data = { variables: { ...params }, query: payload };
  }
}

function toArray(value, delimiter) {
  if (isString(value)) {
    value = value.split(delimiter || ',');
  }
  return value;
}

function normalizeData(config) {
  const { data, params } = splitData(config.params);
  config.data = { ...filterData(config.data), ...data };
  config.params = params;
}

function filterData(data) {
  if (!data) return {};

  const ret = {};
  for (let k in data) {
    if (k.startsWith('__')) continue;
    ret[k] = data[k];
  }
  return ret;
}

function splitData(data) {
  if (!data) {
    return {};
  }
  const body = {};
  const params = {};
  for (let k in data) {
    if (k.startsWith('__')) continue;
    if (k.charAt(0) === '@' || k.charAt(0) === '_') {
      params[k] = data[k];
    } else {
      body[k] = data[k];
    }
  }
  return {
    data: body,
    params,
  };
}

function guessDefinition(data) {
  let args = [];
  if (data) {
    for (let k in data) {
      if (isSpecialVarName(k)) {
        continue;
      }
      args.push({ name: k, type: guessType(data[k]) });
    }
  }
  return { arguments: args };
}

function guessExtArgDefinitions(data) {
  let args = [];
  if (data) {
    for (let k in data) {
      if (k.startsWith('v_')) {
        args.push({ name: k, type: guessType(data[k]) });
      }
    }
  }
  return args;
}

function isSpecialVarName(name) {
  return name.startsWith('__') || name.startsWith('@') || name.startsWith('v_') || name.startsWith('filter_');
}

function guessType(value) {
  if (isString(value)) {
    return 'String';
  }
  if (isNumber(value)) {
    if (isInteger(value)) {
      return 'Int';
    }
    return 'Float';
  }
  if (isBoolean(value)) {
    return 'Boolean';
  }

  if (isPlainObject(value)) {
    return 'Map';
  }
  if (isArray(value)) {
    return '[String]';
  }
  return 'String';
}

/**
 * 缺省注册了CRUD操作所对应的GraphQL类型
 */
const operationRegistry = {
  get: {
    //  operation: 'query',
    arguments: [
      {
        name: 'id',
        type: 'String',
        builder: argString,
      },
      {
        name: 'ignoreUnknown',
        type: 'Boolean',
        builder: argBoolean,
      },
    ],
  },
  findPage: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },

  findList: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },
  findTreeEntityPage: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },
  findPageForTree: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },
  findFirst: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },
  findCount: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
    ],
  },
  findParameter: {
    //  operation: 'query',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  updateByQuery: {
    //  operation: 'query',
    arguments: [
      {
        name: 'query',
        type: 'QueryBeanInput',
        builder: argQuery,
      },
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  update: {
    //  operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  save: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  saveOrUpdate: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  upsert: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },
  copyForNew: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: 'Map',
        builder: argDataMap,
      },
    ],
  },

  delete: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'id',
        type: 'String',
        builder: argString,
      },
    ],
  },
  batchGet: {
    arguments: [
      {
        name: 'ids',
        type: '[String]',
        builder: argStringList,
      },
    ],
  },
  batchDelete: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'ids',
        type: '[String]',
        builder: argStringList,
      },
    ],
  },
  batchModify: {
    // operation: 'mutation',
    arguments: [
      {
        name: 'data',
        type: '[Map]',
        builder: argMapList,
      },
      {
        name: 'delIds',
        type: '[String]',
        builder: argStringList,
      },
    ],
  },
};

const defaultArgBuilders = {
  String: argString,
  Boolean: argBoolean,
  Int: argInt,
  Float: argFloat,
  Map: argMap,
  '[String]': argStringList,
  '[Map]': argMapList,
  QueryBeanInput: argQuery,
};

function handleGraphQLUrl(opType, body, config, params) {
  let pos = body.indexOf('?');
  let part = body.substring(0, pos > 0 ? pos : undefined);
  if (pos > 0) {
    // 例如：@query:NopAuthUser__findList?filter_userStatus=1&orderField=userName&orderDir=asc
    const splicingStr = body.substring(pos + 1);
    const condition = splicingStr
      ? splicingStr.split('&').reduce((data, item) => {
          const [key, value] = item.split('=');
          data[key] = value;
          return data;
        }, {})
      : {};

    params = { ...params, ...condition };
  }

  let [action, selection] = part.split('/');

  selection = selection ? selection.replaceAll('%20', ' ').replaceAll('%0A', '\n') : params['gql:selection'];

  let stdAction = config.stdAction || action.substring(action.lastIndexOf('_') + 1);

  let def = operationRegistry[stdAction];
  if (!def) {
    def = guessDefinition(params);
  }

  // 对于__findPage等调用，除了预定义的query等参数之外，可以通过v_XXX这种参数名来增加额外的参数
  let args = [...def.arguments, ...guessExtArgDefinitions(params)];

  let query = opType + ' ' + action;
  if (args.length > 0) {
    query += '(';
    query += args.map(arg => '$' + arg.name + ':' + arg.type).join(',');
    query += ')';
  }
  query += '{\n';
  query += action + '(';
  if (args.length > 0) {
    query += args.map(arg => `${arg.name}: $${arg.name}`).join(',');
  }
  query += ')';
  if (selection) {
    query += '{\n';
    query += selection;
    query += '\n}';
  }
  query += '\n}';

  const variables = reorganVar(args, params);

  config.data = {
    query,
    variables,
  };
}

function reorganVar(args, params) {
  const variables = {};
  args.forEach(arg => {
    const builder = arg.builder || defaultArgBuilders[arg.type] || argValue;
    variables[arg.name] = builder(params, arg);
  });
  return variables;
}

export function registerOperation(name, op) {
  operationRegistry[name] = op;
}

function argString(data, arg) {
  let v = data[arg.name];
  if (v == null) {
    return null;
  }
  return String(v);
}

function argBoolean(data, arg) {
  let v = data[arg.name];
  if (v == null) {
    return null;
  }
  if (v === 'false' || v === 'n' || v === '0' || v === 'N') {
    return false;
  }
  return !!v;
}

function argInt(data, arg) {
  let v = data[arg.name];
  if (v == null) {
    return null;
  }
  return parseInt(v, 10);
}

function argFloat(data, arg) {
  let v = data[arg.name];
  if (v == null) {
    return null;
  }
  return parseFloat(v);
}

/**
 * 通过 filter_XX__ge=3来表达 <ge name="XX" value="3" />这种过滤条件
 */
function argQuery(data, arg, options) {
  let query = {};
  query.limit = data.pageSize || data.limit || 0;
  query.offset = data.offset || query.limit * ((data.pageIndex || 0) - 1);
  query.orderBy = toOrderBy(data.orderBy || data.orderField, data.orderDir);
  query.filter = toFilter(data);
  query.cursor = data.cursor;
  query.timeout = data.timeout;

  return query;

  function toOrderBy(v, orderDir) {
    if (v == null) {
      return;
    }
    if (isString(v)) {
      if (v.length === 0) {
        return;
      }
      // 字典表显示时会替换成label字段。例如status在列表上实际显示status_label，排序需要按照status进行
      if (v.endsWith('_label')) {
        v = v.substring(0, v.length - '_label'.length);
      }
      return [{ name: v, desc: orderDir === 'desc' }];
    }
    if (isArray(v)) {
      return v;
    }
    return [v];
  }

  function toFilter(perkData) {
    let filter = {
      $type: 'and',
      $body: [],
    };
    for (let k in perkData) {
      if (k.startsWith('filter_')) {
        let [name, op] = k.substring('filter_'.length).split('__');
        op = op || 'eq';
        // 把换行符替换掉
        let value = typeof perkData[k] === "string" ? perkData[k].replace(/\s+/g, ''): perkData[k];

        // 不提交空的查询条件
        if (value == null || value === '') {
          continue;
        }

        // 如果传入字符串__empty，则实际提交的是空字符串
        if (value === '__empty') {
          value = '';
        } else if (value === '__null') {
          value = null;
        }

        let min;
        let max;

        if (op.startsWith('between') && value != null) {
          let ary = toArray(value);
          min = ary[0];
          max = ary[1];
          value = undefined;
        }
        filter.$body.push({ $type: op, name, value, min, max });
      }
    }

    // if (options.filter) {
    //     if (options.filter.$type === 'and' || options.filter.$type === '_' || options.filter.$type === 'filter') {
    //         filter.$body = filter.$body.concat(options.filter.$body || []);
    //     } else {
    //         filter.$body.push(options.filter);
    //     }
    // }

    if (filter.$body.length === 0) {
      return;
    }
    return filter;
  }
}

function argDataMap(data, arg) {
  if (data == null) {
    return null;
  }
  let ret = {};
  for (let k in data) {
    if (isSpecialVarName(k)) {
      continue;
    }
    ret[k] = data[k];
  }
  return ret;
}

function argMap(data, arg) {
  return data[arg.name];
}

function argStringList(data, arg) {
  let v = data[arg.name];
  if (v == null) {
    return null;
  }
  if (isString(v)) {
    return v.split(',');
  }
  return v;
}

function argMapList(data, arg) {
  return data[arg.name];
}

function argValue(data, arg) {
  return data[arg.name];
}
