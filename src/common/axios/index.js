import axios from 'axios';
import { handleGraphQL } from './graphql';
import { returnDataResponseInterceptors } from './interceptors/returnDataResponse';
import { convertStatusResponseInterceptors } from './interceptors/convertStatusResponse';
import { axiosUseRequestInterceptors } from './interceptors/request';

export const ajax = axios.create({
  timeout: 30000,
});

// 使用方法：https://github.com/kirklin/celeris-web/blob/master/packages/web/request/src/HttpClient.ts
// const axiosCanceler = new AxiosCanceler();
axiosUseRequestInterceptors(ajax);
convertStatusResponseInterceptors(ajax);
returnDataResponseInterceptors(ajax);


export function fetchGql(payload, params = {}, options = {}) {
  console.info('_______fetchGql_______', payload, params, options);

  const controller = options.abortController || new AbortController();

  // const globSetting = useAdapter().useSettings();

  // if (globSetting.apiUrl && options.config.useApiUrl !== false) {
  //   url = `${globSetting.apiUrl}${url}`;
  // }

  function normalizeData(data) {
    if (!data) return data;
    if (data instanceof FormData || data instanceof ArrayBuffer) return data;
    return Object.assign({}, data);
  }
  //**************
  const grapHqlParams = {
    baseUrl: `${window.location.protocol}//${window.location.hostname}:8111`,
    stdAction: options.stdAction, // 是否需要标准的action格式
    // params: query,
  };

  handleGraphQL(grapHqlParams, payload, normalizeData(params));

  // const { useI18n, processRequest, processResponse } = useAdapter();
  const ajaxParams = {
    url: grapHqlParams.url,
    data: grapHqlParams.data,
    options,
    method: options.method || 'post',
    withCredentials: options.withCredentials || true,
  }

  const response = ajax.request(ajaxParams);
  return response;
}
