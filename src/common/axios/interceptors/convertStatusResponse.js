export function convertStatusResponseInterceptors(reference) {
  reference.interceptors.response.use(
    (response) => {
      // 根据URL决定使用哪种转换函数
      const transformFunc = response.config.url.includes('/r/') ?
                              transformsRestResponse : transformsGraphqlResponse;
      response.data = transformFunc(response);
      return response;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
}

function transformsRestResponse(responseData) {
  const { status, msg, code, data } = responseData.data;
  return {
    status: status === -1 ? 0 : 1,
    msg,
    ...(status !== -1 ? { data } : {})
  };
}

function transformsGraphqlResponse(response) {
  const { data: respData, config  } = response;
  const { data, errors, extensions } = respData;

  if (errors && errors.length > 0) {
    return {
      status: extensions['nop-status'] || 0,
      msg: errors[0].message,
      code: extensions['nop-error-code']
    };
  }

  /**
   * 如果 res.data 的值只有一个对象，证明是单个查询，直接返回这个属性的值
   * 否则返回整个对象，让业务方可以选择不同的对象进行处理
   */
  if (data && Object.keys(data).length === 1 && config.options.converSingleObject !== false) {
    return {
      status: 1,
      msg: extensions && extensions['nop-msg'],
      data: data[Object.keys(data)[0]]
    };
  }

  return {
    status: 1,
    msg: extensions && extensions['nop-msg'],
    data
  };
}

