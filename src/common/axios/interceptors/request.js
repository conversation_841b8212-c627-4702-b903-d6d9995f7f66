import { HEADER_TIMESTAMP, HEADER_APP_PATHNAME } from '../consts';
import { useAdapter } from '../useAdapter';

/* 请求拦截器 */
export function axiosUseRequestInterceptors(service) {
  service.interceptors.request.use((config) => {
    const { useAuthToken, useTokenPrefix, useApplicationPath } = useAdapter();

    config.headers = {
      ...config.headers,
      'x-requested-with': 'XMLHttpRequest',
      'Content-Type': 'application/json;charset=UTF-8'
    };

    const token = useAuthToken();
    const options = config.options;

    if (token && (options && options.isToken !== false)) {
      config.headers.Authorization = `${useTokenPrefix()}${token}`;

      // 添加时间戳, 防止缓存
      config.headers[HEADER_TIMESTAMP] = new Date().getTime();

      // 添加应用ID
      const applicationPath = useApplicationPath();
      if (applicationPath) {
        config.headers[HEADER_APP_PATHNAME] = applicationPath;
      }
    }
    return config;
  });
}
