
import { debounce } from 'lodash';
import { getErrorMessage } from '../../global';

export function returnDataResponseInterceptors(reference) {
  reference.interceptors.response.use(
    (response) => {
      const { config, data: resData } = response;
      const { options } = config;
      const { status, msg, data } = resData;

      if (options.responseType === 'blob') {
        return response;
      }

      if (status === 1) {
        return options.rawResponse ? resData : data;
      } else {
        if (options.needError) {
          return resData;
        } else {
          alert(msg, 3);
        }
      }
      return Promise.reject(new Error(msg));
    },
    (error) => {

      let errorMsg = '';
      if (error.code === 'ERR_NETWORK') {
        errorMsg = '网络异常，请检查服务器情况';
      } else {
        // 处理 HTTP 网络错误
        const parError = getErrorMessage(error.response, '') || '发生未知错误';
        errorMsg = parError.errorMessage;
      }
      BombError(errorMsg);
      return Promise.reject(error);
    },
  );
}

// 使用防抖的方式对消息进行拦截
const BombError = debounce((msg) => {
  alert(msg, 3);
}, 500)


// function doLogout(reason) {
  // const { setAuthToken, logout } = useAdapter();
  // setAuthToken(undefined);
  // logout(reason);
// }
