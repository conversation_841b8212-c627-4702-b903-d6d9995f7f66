
import { getAuthId } from 'src/util/authStorage';


const adapter = {

    // 如果存放在localStorage中的数据需要升级，这里的版本号需要增加。
    // 从localStorage中读取缓存数据时会检查版本号，如果版本不一致，会调用configUpgrade函数来升级，缺省会丢弃原有配置
    configUpgrade(configName, version, prevVersion, config) {
        return undefined;
    },

    /**
     * 返回当前的locale
     */
    useLocale() {
        // throw new Error('not-impl');
    },

    useI18n() {
        // throw new Error('not-impl');
    },


    useRouter() {
        // throw new Error('not-impl');
    },

    useSettings() {
        return {
            apiUrl: ''
        };
    },

    /**
     * 返回当前的认证token
     */
    useAuthToken() {
      return getAuthId();
    },

    setAuthToken(token) {

    },
    useApplicationPath() {
      const pathname = location.pathname;
      const match = pathname.match(/\/(app|worksheet)\/([^/]+)/);
      if (match) {
        return pathname+location.search;
      }
      // if (!match) return;
      // try {
      //     // 解析 URL 路径和查询参数
      //     // const urlObj = new URL(location.url, location.origin);
      //     const searchParams = location.search;

      //     // 判断前缀并提取 appid
      //     if (pathname.startsWith("/app/")) {
      //       // 从 "/app/<appid>" 提取
      //       const match = pathname.match(/\/app\/([a-zA-Z0-9]+)/);
      //       console.log('match:::::', match)
      //       return match ? match[1] : null;
      //     }

      //     if (pathname.startsWith("/worksheet/field/edit")) {
      //       // 从 fromURL 参数提取
      //       const fromURL = searchParams.get("fromURL");
      //       if (fromURL) {
      //         const match = fromURL.match(/\/app\/([a-zA-Z0-9]+)/);
      //         return match ? match[1] : null;
      //       }
      //     }

      //     if (pathname.startsWith("/worksheet/formSet/edit")) {
      //       // 从 "/worksheet/formSet/edit/<appid>" 提取
      //       const match = pathname.match(/\/formSet\/edit\/([a-zA-Z0-9]+)/);
      //       return match ? match[1] : null;
      //     }
      //   } catch (error) {
      //     console.error(`提取 appid 失败，url: ${url}`, error);
      //   }
      //   return null;
    },
    /**
     * 返回token的前缀
     */
    useTokenPrefix() {
      return 'mlc_auth_mark ';
    },

    isUserInRole(role) {
        // throw new Error('not-impl');
    },

    useTenantId() {
        // throw new Error('not-impl');
    },

    /**
     * 自动退出时执行的回调
     */
    logout(reason) {
        throw new Error('not-impl');
    },

    processRequest(request) {
        return request;
    },

    processResponse(response) {
        return response;
    },

    // compileFunction(code, page) {
    //     return new Function('page', 'return ' + code).call(null, page);
    // },

    // isCurrentUrl: default_isCurrentUrl,

    alert(msg, title) {
        throw new Error('not-impl');
    },

    confirm(msg, title) {
        throw new Error('not-impl');
    },

    dataMapping(
        to,
        from = {},
        ignoreFunction = false,
        convertKeyToPath,
        ignoreIfNotMatch = false
      ) {
        throw new Error('not-impl');
    },

    fetchDict(dictName, options) {
        throw new Error('not-impl');
    },

    getPage(pageUrl) {
        throw new Error('not-impl');
    }
};

export function registerAdapter(data) {
    Object.assign(adapter, data);
}

export function useAdapter() {
    return adapter;
}

