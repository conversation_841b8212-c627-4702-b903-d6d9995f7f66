export function splitPrefixUrl(url) {
  if (url.startsWith('@')) {
    let pos = url.indexOf(':');
    if (pos < 0) {
      return;
    }
    return [url.substring(1, pos), url.substring(pos + 1).trim()];
  }
  let pos = url.indexOf('://');
  if (pos < 0) { return; }
  return [url.substring(0, pos), url.substring(pos + 3)];
}


export function fetcherOk(data) {
  return {
    status: 200,
    headers: {},
    data: {
      status: 0,
      msg: '',
      data: data,
    },
  };
}

export function responseOk(data) {
  return {
    status: 0,
    msg: '',
    data,
  };
}
