﻿@import url(./themeVariables.less);
@import url(./mixins.less);

a {
    color: @themeLinkColor;
}

.themeLoopClass(1, 10, ThemeColor, color, themeColor);
.themeLoopClass(1, 10, ThemeBGColor, background-color, themeBgColor);
.themeLoopClass(1, 10, ThemeHoverColor, color, themeHoverColor, ":hover");
.themeLoopClass(1, 10, ThemeBorderColor, border-color, themeBorderColor);

.ThemeHoverBGColor2:hover {
  background-color: @themeBgColor2 !important;
}
.ThemeHoverBGColor3:hover {
  background-color: @themeBgColor3 !important;
}
.ThemeHoverBGColor7:not(.ThemeBGColor8):hover {
    background-color: @themeBgColor7 !important;
}
.ThemeHoverBorderColor2:hover {
  border-color: @themeBorderColor2 !important;
}
.ThemeHoverBorderColor3:hover {
  border-color: @themeBorderColor3 !important;
}
.ThemeHoverColor3:hover {
  color: @themeColor3 !important;
}
.ThemeBeforeBGColor6:before {
  background-color: @themeBgColor6 !important;
}

.btnBootstrap-primary {
    background-color: @themeColor3;
    transition: all 0.35s ease-in;
}
.btnBootstrap-primary:hover, .btnBootstrap-primary:focus, .btnBootstrap-primary:active, .btnBootstrap-primary.active, .btnBootstrap-primary.disabled, .btnBootstrap-primary[disabled] {
    background-color: @themeColor2;
}


.MdLoader {
  position: relative;
  margin: 0px auto;
  &:before {
    content: '';
    display: block;
    padding-top: 100%;
  }

  &-circular {
    animation: mdLoaderRotate 2s linear infinite;
    height: 100%;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }

  &-path {
    stroke: @themeColor3;
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    stroke-miterlimit: 10;
    fill: none;
    animation: mdLoaderDash 1.5s ease-in-out infinite;
  }

  &--big {
    width: 44px;

    .MdLoader-path {
      stroke-width: 4px;
      cx: 22px;
      cy: 22px;
      r: 18px;
    }
  }

  &--middle {
    width: 30px;

    .MdLoader-path {
      stroke-width: 3px;
      cx: 15px;
      cy: 15px;
      r: 12px;
    }
  }

  &--small {
    width: 20px;

    .MdLoader-path {
      stroke-width: 2px;
      cx: 10px;
      cy: 10px;
      r: 8px;
    }
  }
}

@keyframes mdLoaderRotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes mdLoaderDash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}
