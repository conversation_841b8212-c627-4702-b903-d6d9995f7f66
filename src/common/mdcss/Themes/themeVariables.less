﻿@shadow2: 0 4px 20px rgba(0, 0, 0, 0.13), 0 2px 6px rgba(0, 0, 0, 0.1);

@grayDarker: #151515;
@grayDark: #757575;
@gray: #9e9e9e;
@grayLight: #bdbdbd; // 主色调
@grayLighter: #e0e0e0;
@grayLightest: #f5f5f5;

@textColor: @grayDarker;
@bgColor: @grayLightest;
@dangerColor: #f44336;
@successColor: #4caf50;


@themeColor1: #0d47a1;
@themeColor2: #1565c0;
@themeColor3: #1e88e5;
@themeColor4: #90caf9;
@themeColor5: #bbdefb;
@themeColor6: #e3f2fd;
@themeColor7: rgba(000,000,000,0.12);
@themeColor8: rgba(000,000,000,0.32);
@themeColor9: rgba(000,000,000,0.54);
@themeColor10: rgba(000,000,000,0.87);

@themeLinkColor: @themeColor3;

@themeBgColor1: @themeColor1;
@themeBgColor2: @themeColor2;
@themeBgColor3: @themeColor3;
@themeBgColor4: @themeColor4;
@themeBgColor5: @themeColor5;
@themeBgColor6: @themeColor6;
@themeBgColor7: rgba(000,000,000,0.04);
@themeBgColor8: #bbdefb;
@themeBgColor9: white;
@themeBgColor10: @themeColor10;

@themeHoverBgColor1: @themeColor1;
@themeHoverBgColor2: @themeColor2;
@themeHoverBgColor3: @themeColor3;
@themeHoverBgColor4: @themeColor4;
@themeHoverBgColor5: @themeColor5;
@themeHoverBgColor6: @themeColor6;
@themeHoverBgColor7: rgba(000,000,000,0.04);
@themeHoverBgColor8: rgba(000,000,000,0.24);
@themeHoverBgColor9: rgba(000,000,000,0.4);
@themeHoverBgColor10: @themeColor10;

@themeHoverColor1: @themeColor1;
@themeHoverColor2: @themeColor2;
@themeHoverColor3: @themeColor3;
@themeHoverColor4: @themeColor4;
@themeHoverColor5: @themeColor5;
@themeHoverColor6: @themeColor6;
@themeHoverColor7: @themeColor7;
@themeHoverColor8: @themeColor8;
@themeHoverColor9: @themeColor9;
@themeHoverColor10: @themeColor10;

@themeBorderColor1: @themeColor1;
@themeBorderColor2: @themeColor2;
@themeBorderColor3: @themeColor3;
@themeBorderColor4: @themeColor4;
@themeBorderColor5: @themeColor5;
@themeBorderColor6: @themeColor6;
@themeBorderColor7: @themeColor7;
@themeBorderColor8: @themeColor8;
@themeBorderColor9: @themeColor9;
@themeBorderColor10: @themeColor10;
