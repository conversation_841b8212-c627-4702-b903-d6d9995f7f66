﻿:root {
  --app-primary-color: #1e88e5;
  --app-primary-hover-color: #1565c0;
}

body,
input,
select,
img,
ol,
ul,
textarea,
button {
  font-size: 13px;
  color: #151515;
  line-height: 1.5;
}

body {
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    'WenQuanYi Micro Hei', sans-serif !important;
  width: 100%;
  height: 100%;
}

body#ja {
  font-family: 'Helvetica Neue', 'Helvetica, Arial', 'Hiragino Kaku Gothic Pro', 'Meiryo, Yu Gothic',
    'MS Gothic, PingFang SC', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif !important;
}

html {
  -webkit-font-smoothing: antialiased;
  width: 100%;
  height: 100%;
}

html body {
  color: #151515;
  font-size: 13px !important;
  margin: 0px;
  padding: 0px;
  background-attachment: fixed;
  -webkit-text-size-adjust: none;
  line-height: 1.5;
  background-color: #f5f5f9;
}

img {
  border: 0px;
  -webkit-user-drag: none;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
}

body ul,
body ol {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

li {
  list-style: none;
}

a {
  text-decoration: none;
  color: #1e88e5;
  cursor: pointer;
}

a:hover {
  text-decoration: underline;
}

a:focus {
  outline: none;
  -moz-outline: none;
}

input,
textarea {
  outline: none;
}

.ThemeBG {
  background: #f5f5f5;
}

.ThemeBGColor9 {
  box-shadow: 0 1px 2px 0px rgba(0, 0, 0, 0.24);
}

#app {
  height: 100%;
}

.placeholder {
  color: #aaa;
}

.overflowHidden {
  overflow: hidden;
  zoom: 1;
}

.overflow_ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.WordBreak {
  word-wrap: break-word;
  word-break: break-word;
}

.breakAll {
  word-wrap: break-word;
  word-break: break-all;
}

.noSelect {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
}

.cursorDefault {
  cursor: default !important;
}

.Hand,
.pointer {
  cursor: pointer;
}

.grab {
  cursor: grab;
}

.cursorText {
  cursor: text;
}

.InlineFlex {
  display: inline-flex !important;
}

.inlineFlex {
  display: inline-flex;
}

.InlineBlock {
  display: inline-block !important;
  display: -moz-inline-stack;
  display: inline-block;
  /* zoom:1; */
  *display: inline;
}

.Block {
  display: block !important;
}

.NoUnderline {
  text-decoration: none !important;
}

.Underline {
  text-decoration: underline !important;
}

.Absolute {
  position: absolute !important;
}

.Relative,
.relative {
  position: relative !important;
}

.Fixed {
  position: fixed !important;
}

.Static {
  position: static !important;
}

.placeholderColor::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #bdbdbd;
}

.placeholderColor::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #bdbdbd;
}

.placeholderColor:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #bdbdbd;
}

/*浮动*/
.Left,
.left {
  float: left !important;
}

.Right,
.right {
  float: right !important;
}

.Clear,
.clear {
  clear: both;
  visibility: hidden;
  border-top: 0px;
  margin-top: -1px !important;
}

.clearfix:after {
  content: '';
  display: table;
  clear: both;
}

.TxtLeft,
.leftAlign {
  text-align: left !important;
}

.TxtCenter,
.center,
.centerAlign {
  text-align: center !important;
}

.TxtRight,
.rightAlign {
  text-align: right !important;
}

.TxtTop,
.TxtTop img {
  vertical-align: top !important;
}

.TxtMiddle {
  vertical-align: middle !important;
}

.TxtBottom {
  vertical-align: bottom !important;
}

.divCenter {
  margin: 0 auto;
}

.Border0 {
  border: 0px !important;
}

.BorderRight0 {
  border-right: 0px !important;
}

.Border {
  border: 1px solid white !important;
}

.BorderTop {
  border-top: 1px solid white !important;
}

.BorderTopGrayC {
  border-top: 1px solid #ccc !important;
}

.BorderRight {
  border-right: 1px solid white !important;
}

.BorderBottom {
  border-bottom: 1px solid white !important;
}

.BorderGrayColor {
  border-color: #ccc !important;
}

.BorderGrayD {
  border: 1px solid #ddd;
}

.borderColor_ef {
  border-color: #efefef !important;
}

.borderColor_c {
  border-color: #ccc !important;
}

.borderColor_d8 {
  border-color: #d8d8d8 !important;
}

.Visibility {
  visibility: hidden;
}

body .Hidden {
  display: none;
}

.hide {
  display: none !important;
}

.w100 {
  width: 100% !important;
}

.h100 {
  height: 100%;
}

.wMax100 {
  max-width: 100%;
}

.Bold {
  font-weight: bold !important;
}

.Normal {
  font-weight: normal !important;
}

.bold {
  font-weight: bold;
}

/*Color*/
.Red {
  color: #f00 !important;
}

.Green {
  color: #739812 !important;
}

.DepGreen {
  color: green !important;
}

.White {
  color: white !important;
}

.HoverWhite:hover {
  color: white !important;
}

.Hover_49:hover {
  color: #49adfc !important;
}

/*浅黄色 官方群组*/
.DisabledColor {
  color: #cac8bb !important;
}

/*浅灰色 */
.LightGray {
  color: #ddd !important;
}

/*Background-Color */
.HighLightColor {
  background-color: #ffcc66;
}

.GrayBG {
  background-color: #f5f5f5;
}

.GrayBGF8 {
  background-color: #f8f8f8;
}

.GrayBGFA {
  background-color: #fafafa;
}

.BGDDD {
  background-color: #ddd;
}

.WhiteBG {
  background-color: #fff;
}

.BGF7F7F7 {
  background-color: #f7f7f7;
}

.Alpha10 {
  opacity: 1;
  -moz-opacity: 1;
  filter: alpha(opacity=100);
}

.Alpha9 {
  opacity: 0.9;
  -moz-opacity: 0.9;
  filter: alpha(opacity=90);
}

.Alpha8 {
  opacity: 0.8;
  -moz-opacity: 0.8;
  filter: alpha(opacity=80);
}

.Alpha7 {
  opacity: 0.7;
  -moz-opacity: 0.7;
  filter: alpha(opacity=70);
}

.Alpha6 {
  opacity: 0.6;
  -moz-opacity: 0.6;
  filter: alpha(opacity=60);
}

.Alpha5 {
  opacity: 0.5;
  -moz-opacity: 0.5;
  filter: alpha(opacity=50);
}

.Alpha4 {
  opacity: 0.4;
  -moz-opacity: 0.4;
  filter: alpha(opacity=40);
}

.Alpha3 {
  opacity: 0.3;
  -moz-opacity: 0.3;
  filter: alpha(opacity=30);
}

.Alpha2 {
  opacity: 0.2;
  -moz-opacity: 0.2;
  filter: alpha(opacity=20);
}

.Alpha1 {
  opacity: 0.1;
  -moz-opacity: 0.1;
  filter: alpha(opacity=10);
}

.Alpha0 {
  opacity: 0;
  -moz-opacity: 0;
  filter: alpha(opacity=0);
}

/*Margin And Padding*/
.mAll0 {
  margin: 0px !important;
}
.mAll5 {
  margin: 5px !important;
}

.mAll10 {
  margin: 10px !important;
}

.mAll12 {
  margin: 12px !important;
}

.mAll15 {
  margin: 15px !important;
}

.mAll20 {
  margin: 20px !important;
}

.mAll25 {
  margin: 25px !important;
}

.mAll30 {
  margin: 30px !important;
}

.mTop0 {
  margin-top: 0px !important;
}

.mTop1 {
  margin-top: 1px !important;
}

.mTop2 {
  margin-top: 2px !important;
}

.mTop3 {
  margin-top: 3px !important;
}

.mTop4 {
  margin-top: 4px !important;
}

.mTop5 {
  margin-top: 5px !important;
}

.mTop6 {
  margin-top: 6px !important;
}

.mTop7 {
  margin-top: 7px !important;
}

.mTop8 {
  margin-top: 8px !important;
}

.mTop9 {
  margin-top: 9px !important;
}

.mTop10 {
  margin-top: 10px !important;
}

.mTop11 {
  margin-top: 11px !important;
}

.mTop12 {
  margin-top: 12px !important;
}

.mTop13 {
  margin-top: 13px !important;
}

.mTop15 {
  margin-top: 15px !important;
}

.mTop16 {
  margin-top: 16px !important;
}

.mTop18 {
  margin-top: 18px !important;
}

.mTop20 {
  margin-top: 20px !important;
}

.mTop24 {
  margin-top: 24px !important;
}

.mTop25 {
  margin-top: 25px !important;
}

.mTop28 {
  margin-top: 28px !important;
}

.mTop30 {
  margin-top: 30px !important;
}

.mTop32 {
  margin-top: 32px !important;
}

.mTop35 {
  margin-top: 35px !important;
}

.mTop36 {
  margin-top: 36px !important;
}

.mTop40 {
  margin-top: 40px !important;
}

.mTop42 {
  margin-top: 42px !important;
}

.mTop45 {
  margin-top: 45px !important;
}

.mTop46 {
  margin-top: 46px !important;
}

.mTop50 {
  margin-top: 50px !important;
}

.mTop80 {
  margin-top: 80px !important;
}
.mTop90 {
  margin-top: 90px !important;
}

.mRight0 {
  margin-right: 0px !important;
}

.mRight2 {
  margin-right: 2px !important;
}

.mRight3 {
  margin-right: 3px !important;
}

.mRight5 {
  margin-right: 5px !important;
}

.mRight6 {
  margin-right: 6px !important;
}

.mRight7 {
  margin-right: 7px !important;
}

.mRight8 {
  margin-right: 8px !important;
}

.mRight9 {
  margin-right: 9px !important;
}

.mRight10 {
  margin-right: 10px !important;
}

.mRight12 {
  margin-right: 12px !important;
}

.mRight13 {
  margin-right: 13px !important;
}

.mRight14 {
  margin-right: 14px !important;
}

.mRight15 {
  margin-right: 15px !important;
}

.mRight16 {
  margin-right: 16px !important;
}

.mRight18 {
  margin-right: 18px !important;
}

.mRight20 {
  margin-right: 20px !important;
}

.mRight24 {
  margin-right: 24px !important;
}

.mRight25 {
  margin-right: 25px !important;
}

.mRight26 {
  margin-right: 26px !important;
}

.mRight30 {
  margin-right: 30px !important;
}

.mRight32 {
  margin-right: 32px !important;
}

.mRight35 {
  margin-right: 35px !important;
}

.mRight40 {
  margin-right: 40px !important;
}

.mRight60 {
  margin-right: 60px !important;
}

.mBottom0 {
  margin-bottom: 0px !important;
}

.mBottom1 {
  margin-bottom: 1px !important;
}

.mBottom2 {
  margin-bottom: 2px !important;
}

.mBottom3 {
  margin-bottom: 3px !important;
}

.mBottom4 {
  margin-bottom: 4px !important;
}

.mBottom5 {
  margin-bottom: 5px !important;
}

.mBottom6 {
  margin-bottom: 6px !important;
}

.mBottom7 {
  margin-bottom: 7px !important;
}

.mBottom8 {
  margin-bottom: 8px !important;
}

.mBottom10 {
  margin-bottom: 10px !important;
}

.mBottom12 {
  margin-bottom: 12px !important;
}

.mBottom13 {
  margin-bottom: 13px !important;
}

.mBottom14 {
  margin-bottom: 14px !important;
}

.mBottom15 {
  margin-bottom: 15px !important;
}

.mBottom16 {
  margin-bottom: 16px !important;
}

.mBottom18 {
  margin-bottom: 18px !important;
}

.mBottom20 {
  margin-bottom: 20px !important;
}

.mBottom24 {
  margin-bottom: 24px !important;
}

.mBottom25 {
  margin-bottom: 25px !important;
}

.mBottom30 {
  margin-bottom: 30px !important;
}

.mBottom32 {
  margin-bottom: 32px !important;
}

.mBottom40 {
  margin-bottom: 40px !important;
}

.mBottom45 {
  margin-bottom: 45px !important;
}

.mBottom50 {
  margin-bottom: 50px !important;
}

.mBottom72 {
  margin-bottom: 72px !important;
}

.mLeft0 {
  margin-left: 0px !important;
}

.mLeft2 {
  margin-left: 2px !important;
}

.mLeft3 {
  margin-left: 3px !important;
}

.mLeft4 {
  margin-left: 4px !important;
}

.mLeft5 {
  margin-left: 5px !important;
}

.mLeft6 {
  margin-left: 6px !important;
}

.mLeft7 {
  margin-left: 7px !important;
}

.mLeft8 {
  margin-left: 8px !important;
}

.mLeft10 {
  margin-left: 10px !important;
}

.mLeft12 {
  margin-left: 12px !important;
}

.mLeft14 {
  margin-left: 14px !important;
}

.mLeft15 {
  margin-left: 15px !important;
}

.mLeft16 {
  margin-left: 16px !important;
}

.mLeft17 {
  margin-left: 17px !important;
}

.mLeft20 {
  margin-left: 20px !important;
}

.mLeft24 {
  margin-left: 24px !important;
}

.mLeft25 {
  margin-left: 25px !important;
}

.mLeft26 {
  margin-left: 26px !important;
}

.mLeft30 {
  margin-left: 30px !important;
}

.mLeft32 {
  margin-left: 32px !important;
}

.mLeft35 {
  margin-left: 35px !important;
}

.mLeft40 {
  margin-left: 40px !important;
}

.mLeft48 {
  margin-left: 48px !important;
}

.mLeft50 {
  margin-left: 50px !important;
}

.mLeft60 {
  margin-left: 60px !important;
}

.mLeft80 {
  margin-left: 80px !important;
}

.mLeft100 {
  margin-left: 100px !important;
}

.mLeft150 {
  margin-left: 150px !important;
}

.pAll0 {
  padding: 0px !important;
}

.pAll2 {
  padding: 2px !important;
}

.pAll3 {
  padding: 3px !important;
}

.pAll5 {
  padding: 5px !important;
}

.pAll10 {
  padding: 10px !important;
}

.pAll15 {
  padding: 15px !important;
}

.pAll16 {
  padding: 16px !important;
}

.pAll20 {
  padding: 20px !important;
}

.pAll25 {
  padding: 25px !important;
}

.pAll30 {
  padding: 30px !important;
}

.pAll35 {
  padding: 35px !important;
}

.pTop0 {
  padding-top: 0px !important;
}

.pTop2 {
  padding-top: 2px !important;
}

.pTop3 {
  padding-top: 3px !important;
}

.pTop5 {
  padding-top: 5px !important;
}

.pTop6 {
  padding-top: 6px !important;
}

.pTop7 {
  padding-top: 7px !important;
}

.pTop8 {
  padding-top: 8px !important;
}

.pTop10 {
  padding-top: 10px !important;
}

.pTop12 {
  padding-top: 12px !important;
}

.pTop13 {
  padding-top: 13px !important;
}

.pTop15 {
  padding-top: 15px !important;
}

.pTop16 {
  padding-top: 16px !important;
}

.pTop20 {
  padding-top: 20px !important;
}

.pTop25 {
  padding-top: 25px !important;
}

.pTop30 {
  padding-top: 30px !important;
}

.pTop35 {
  padding-top: 35px !important;
}

.pTop40 {
  padding-top: 40px !important;
}

.pTop45 {
  padding-top: 45px !important;
}

.pTop50 {
  padding-top: 50px !important;
}

.pTop100 {
  padding-top: 100px !important;
}

.pRight0 {
  padding-right: 0px !important;
}

.pRight5 {
  padding-right: 5px !important;
}

.pRight6 {
  padding-right: 6px !important;
}

.pRight8 {
  padding-right: 8px !important;
}

.pRight9 {
  padding-right: 9px !important;
}

.pRight10 {
  padding-right: 10px !important;
}

.pRight12 {
  padding-right: 12px !important;
}

.pRight15 {
  padding-right: 15px !important;
}

.pRight16 {
  padding-right: 16px !important;
}

.pRight20 {
  padding-right: 20px !important;
}

.pRight24 {
  padding-right: 24px !important;
}

.pRight25 {
  padding-right: 25px !important;
}

.pRight30 {
  padding-right: 30px !important;
}

.pRight36 {
  padding-right: 36px !important;
}

.pRight40 {
  padding-right: 40px !important;
}

.pRight60 {
  padding-right: 60px !important;
}

.pBottom0 {
  padding-bottom: 0px !important;
}

.pBottom2 {
  padding-bottom: 2px !important;
}

.pBottom3 {
  padding-bottom: 3px !important;
}

.pBottom5 {
  padding-bottom: 5px !important;
}

.pBottom6 {
  padding-bottom: 6px !important;
}

.pBottom7 {
  padding-bottom: 7px !important;
}

.pBottom8 {
  padding-bottom: 8px !important;
}

.pBottom10 {
  padding-bottom: 10px !important;
}

.pBottom12 {
  padding-bottom: 12px !important;
}

.pBottom13 {
  padding-bottom: 13px !important;
}

.pBottom15 {
  padding-bottom: 15px !important;
}

.pBottom16 {
  padding-bottom: 16px !important;
}

.pBottom20 {
  padding-bottom: 20px !important;
}

.pBottom25 {
  padding-bottom: 25px !important;
}

.pBottom30 {
  padding-bottom: 30px !important;
}

.pBottom40 {
  padding-bottom: 40px !important;
}

.pBottom50 {
  padding-bottom: 50px !important;
}

.pBottom100 {
  padding-bottom: 100px !important;
}

.pLeft0 {
  padding-left: 0px !important;
}

.pLeft5 {
  padding-left: 5px !important;
}

.pLeft6 {
  padding-left: 6px !important;
}

.pLeft7 {
  padding-left: 7px !important;
}

.pLeft8 {
  padding-left: 8px !important;
}

.pLeft9 {
  padding-left: 9px !important;
}

.pLeft10 {
  padding-left: 10px !important;
}

.pLeft11 {
  padding-left: 11px !important;
}

.pLeft12 {
  padding-left: 12px !important;
}

.pLeft15 {
  padding-left: 15px !important;
}

.pLeft16 {
  padding-left: 16px !important;
}

.pLeft20 {
  padding-left: 20px !important;
}

.pLeft24 {
  padding-left: 24px !important;
}

.pLeft25 {
  padding-left: 25px !important;
}

.pLeft28 {
  padding-left: 28px !important;
}

.pLeft30 {
  padding-left: 30px !important;
}

.pLeft29 {
  padding-left: 29px !important;
}

.pLeft35 {
  padding-left: 35px !important;
}

.pLeft36 {
  padding-left: 36px !important;
}

.pLeft40 {
  padding-left: 40px !important;
}

.pLeft42 {
  padding-left: 42px !important;
}

.pLeft45 {
  padding-left: 45px !important;
}

.pLeft50 {
  padding-left: 50px !important;
}

.pLeft160 {
  padding-left: 160px !important;
}

.pTopBottom3 {
  padding-top: 3px !important;
  padding-bottom: 3px !important;
}

.pTopBottom5 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.pLeftRight5 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.pLeftRight10 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.pLeftRight30 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.mp0 {
  margin: 0px !important;
  padding: 0px !important;
}

.Top0 {
  top: 0 !important;
}

.Top1 {
  top: 1px;
}

.Top3 {
  top: 3px;
}

.Top5 {
  top: 5px;
}

.Top10 {
  top: 10px;
}

.Top15 {
  top: 15px !important;
}

.Top20 {
  top: 20px;
}

.Top60 {
  top: 60px !important;
}

.Right1 {
  right: 1px;
}

.Right5 {
  right: 5px;
}

.Right8 {
  right: 8px;
}

.minHeight0 {
  min-height: 0;
}

.minHeight30 {
  min-height: 30px !important;
}

.minHeight36 {
  min-height: 36px !important;
}

.minWidth0 {
  min-width: 0 !important;
}

.Width70 {
  width: 70px !important;
}

.Width80 {
  width: 80px !important;
}

.Width90 {
  width: 90px !important;
}

.Width95 {
  width: 95px !important;
}

.Width110 {
  width: 110px !important;
}

.Width120 {
  width: 120px !important;
}

.Width180 {
  width: 180px !important;
}

.Width190 {
  width: 190px !important;
}

.Width200 {
  width: 200px !important;
}

.Width250 {
  width: 250px !important;
}

.Width300 {
  width: 300px !important;
}

.Width400 {
  width: 400px !important;
}

.Width500 {
  width: 500px !important;
}

.White {
  color: white !important;
}

.Gray {
  color: #151515 !important;
}

.Gray14 {
  color: #151515 !important;
  font-size: 14px;
}

.Gray_6 {
  color: #666 !important;
}

.Gray_8 {
  color: #888 !important;
}

.Gray_9 {
  color: #999 !important;
}

.Gray_a {
  color: #aaa !important;
}

.Gray_c {
  color: #ccc !important;
}

.Gray_d {
  color: #eee !important;
}

.Gray_df {
  color: #dfdfdf !important;
}

.Gray_c6 {
  color: #c6c6c6 !important;
}

.Gray_70 {
  color: #707070 !important;
}

.Gray_7e {
  color: #7e7e7e !important;
}

.Gray_9e {
  color: #9e9e9e !important;
}

.Gray_9d {
  color: #9d9d9d !important;
}

.Gray_75 {
  color: #757575 !important;
}

.Gray_bd {
  color: #bdbdbd !important;
}

.Gray_76 {
  color: #767676 !important;
}

.Black {
  color: #000 !important;
}

.Black18 {
  color: #000 !important;
  font-size: 18px;
}

.ThemeColor {
  color: #2196f3;
}

.Hover_21:hover {
  color: #2196f3 !important;
}

.Hover_9e:hover {
  color: #9e9e9e !important;
}

.font8 {
  font-size: 16px;
  transform: scale(0.5);
  display: inline-block;
}

.font10 {
  font-size: 20px;
  transform: scale(0.5);
  display: inline-block;
}

.Font12 {
  font-size: 12px !important;
}

.Font13 {
  font-size: 13px !important;
}

.Font14 {
  font-size: 14px !important;
}

.Font15 {
  font-size: 15px !important;
}

.Font16 {
  font-size: 16px !important;
}

.Font17 {
  font-size: 17px !important;
}

.Font18 {
  font-size: 18px !important;
}

.Font19 {
  font-size: 19px !important;
}

.Font20 {
  font-size: 20px !important;
}

.Font22 {
  font-size: 22px !important;
}

.Font24 {
  font-size: 24px !important;
}

.Font26 {
  font-size: 26px !important;
}

.Font28 {
  font-size: 28px !important;
}

.Font30 {
  font-size: 30px !important;
}

.Font32 {
  font-size: 32px !important;
}

.Font34 {
  font-size: 34px !important;
}

.Font36 {
  font-size: 36px !important;
}

.Font38 {
  font-size: 38px !important;
}

.Font40 {
  font-size: 40px !important;
}

.Font48 {
  font-size: 48px !important;
}

.Font50 {
  font-size: 50px !important;
}

.Font56 {
  font-size: 56px !important;
}

.Font64 {
  font-size: 64px !important;
}

.Font130 {
  font-size: 130px !important;
}
.LineHeight0 {
  line-height: 0;
}
.LineHeight1em {
  line-height: 1em;
}
.LineHeight15 {
  line-height: 15px !important;
}

.LineHeight16 {
  line-height: 16px !important;
}

.LineHeight20 {
  line-height: 20px !important;
}

.LineHeight22 {
  line-height: 22px !important;
}

.LineHeight24 {
  line-height: 24px !important;
}

.LineHeight25 {
  line-height: 25px !important;
}

.LineHeight26 {
  line-height: 26px !important;
}

.LineHeight27 {
  line-height: 27px !important;
}

.LineHeight28 {
  line-height: 28px !important;
}

.LineHeight30 {
  line-height: 30px !important;
}

.LineHeight32 {
  line-height: 32px !important;
}

.LineHeight34 {
  line-height: 34px !important;
}

.LineHeight35 {
  line-height: 35px !important;
}

.LineHeight36 {
  line-height: 36px !important;
}

.LineHeight40 {
  line-height: 40px !important;
}

.LineHeight44 {
  line-height: 44px !important;
}

.LineHeight50 {
  line-height: 50px !important;
}

.LineHeight60 {
  line-height: 60px !important;
}

.LineHeight80 {
  line-height: 80px !important;
}

.Height30 {
  height: 30px;
}

.Height36 {
  height: 36px;
}

.Height50 {
  height: 50px;
}

.Height80 {
  height: 80px;
}

/*边框圆角*/
.boderRadAll_3 {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
}

.boderRadAll_4 {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.boderRadAll_5 {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}

.boderRadAll_50 {
  -moz-border-radius: 50px;
  -webkit-border-radius: 50px;
  border-radius: 50px;
}

.TextBox {
  height: 20px;
  font-size: 12px;
  line-height: 18px;
  border: 1px solid #d5d5d5;
  background-color: #fff;
  padding-left: 5px;
  box-sizing: initial;
}

.TextArea {
  border: 1px solid #d5d5d5;
  outline: none;
  overflow-y: auto;
  resize: none;
  font-size: 12px;
  line-height: 18px;
  word-wrap: break-word;
  font-family: Tahoma, Arial, 'Microsoft Yahei';
  background-color: #fff;
}

/*静态页面使用的按钮样式 扁平*/
.btn-small {
  padding: 7px 14px;
  font-size: 16px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

.btn-large {
  padding: 11px 19px;
  font-size: 17.5px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}

.btn {
  display: inline-block;
  margin-bottom: 0;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #006dcc;
  background-repeat: repeat-x;
  background-image: linear-gradient(to bottom, #08c, #04c);
  border-left-color: #04c;
  border-right-color: #04c;
  border-top-color: #04c;
  border-bottom-color: #002a80;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  color: #fff;
  background-color: #04c;
}

.btn:hover,
.btn:focus {
  color: #fff;
  text-decoration: none;
  background-position: 0 -15px;
  -webkit-transition: background-position 0.1s linear;
  -moz-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear;
}

.btn-primary-black {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15);
  background-color: #aaa;
  background-repeat: repeat-x;
  background-image: linear-gradient(to bottom, #aaa, #aaa);
  border-left-color: #aaa;
  border-right-color: #aaa;
  border-top-color: #aaa;
  border-bottom-color: #aaa;
}

.btn-primary-black:hover,
.btn-primary-black:focus,
.btn-primary-black:active,
.btn-primary-black.active,
.btn-primary-black.disabled,
.btn-primary-black[disabled] {
  color: #fff;
  background-color: #aaa;
}

/*bootstrap按钮样式 立体*/
.btnBootstrap-middle {
  padding: 5px 19px;
  font-size: 17.5px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}

.btnBootstrap-small {
  padding: 2px 6px;
  font-size: 12px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  border: 0;
}

.btnBootstrap-large {
  padding: 11px 19px;
  font-size: 17.5px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}

.btnBootstrap {
  display: inline-block;
  margin-bottom: 0;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  border: 0px;
}

.btnBootstrap:hover,
.btnBootstrap:focus {
  text-decoration: none;
  background-position: 0 -15px;
  -webkit-transition: background-position 0.1s linear;
  -moz-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear;
}

.btnBootstrap-primary {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15);
  background-color: #1e88e5;
}

.btnBootstrap-primary:hover,
.btnBootstrap-primary:focus,
.btnBootstrap-primary:active,
.btnBootstrap-primary.active,
.btnBootstrap-primary.disabled,
.btnBootstrap-primary[disabled] {
  background-color: #006cc4;
}

.btnBootstrap-black {
  cursor: default;
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15);
  background-color: #aaa;
  background-repeat: repeat-x;
  background-image: linear-gradient(to bottom, #aaa, #aaa);
  border-left-color: #aaa;
  border-right-color: #aaa;
  border-top-color: #aaa;
  border-bottom-color: #aaa;
}

.btnBootstrap-black:hover,
.btnBootstrap-black:focus,
.btnBootstrap-black:active,
.btnBootstrap-black.active,
.btnBootstrap-black.disabled,
.btnBootstrap-black[disabled] {
  color: #fff;
  background-color: #aaa;
}

/* 链接式按钮 */
.btn-link {
  border: none;
  cursor: pointer;
  background: none;
  padding: 3px 6px;
  line-height: 20px;
  margin-top: 2px;
  vertical-align: top;
}

.btn-link:focus,
.btn-link:hover,
.btn-link:active {
  outline: none;
  text-decoration: underline;
}

.Button {
  display: inline-block;
  font-weight: normal;
  text-align: center;
  padding: 1px 5px;
  background-color: #f5f5f5;
  cursor: pointer;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  /*text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);*/
  border: 1px solid #ccc;
}

.Button img {
  vertical-align: middle;
}

.Button:hover {
  text-decoration: none;
}

.Disabled {
  color: #d5d5d5 !important;
  background-color: #999 !important;
  border: 1px solid #666 !important;
}

/*带 border 三角图标 background:#f8f8f8 border:#cfcfcf  上 */
.arrowUpOuter {
  width: 0px;
  height: 0px;
  border-width: 10px 9px;
  border-style: solid;
  border-color: #fff #fff #cfcfcf #fff;
  position: relative;
}

.arrowUpInner {
  width: 0px;
  height: 0px;
  border: 9px solid;
  border-color: transparent transparent #f8f8f8 transparent;
  position: absolute;
  top: -7px;
  left: -9px;
}

/*带 border 三角图标 background:#f8f8f8 border:#cfcfcf  左 */
.arrowRight {
  display: inline-block;
  width: 0px;
  height: 0px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #aaa;
  margin-left: 5px;
}

.arrowLeft {
  display: inline-block;
  width: 0px;
  height: 0px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent #aaa transparent transparent;
  margin-left: 5px;
}

/*文本超出长度后 省略*/
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background: rgba(187, 187, 187, 0.8);
  background-clip: padding-box;
  border: 2px solid rgba(0, 0, 0, 0);
}

::-webkit-scrollbar-thumb:active,
::-webkit-scrollbar-thumb:hover {
  background: rgba(125, 125, 125, 0.8);
  background-clip: padding-box;
}

::-webkit-scrollbar-button,
::-webkit-scrollbar-corner {
  display: none;
  width: 0;
  height: 0;
}

.box-sizing,
.borderBox,
.boxSizing {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.icon-chatnetwork-type2 {
  color: #00bcd4;
}

.icon-chatnetwork-type3 {
  color: #00bcd4;
}

.icon-chatnetwork-type4 {
  color: #8bc34a;
}

.icon-chatnetwork-type5 {
  color: #ff9802;
}

.icon-chatnetwork-type6 {
  color: #ba68c8;
}

.icon-custom-01,
.icon-ai,
.icon-ai1 {
  background: linear-gradient(316deg, #c822eb, #6e00ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 移除input在 ie和chrome下的样式*/
/* IE10+ */
input[type='text']::-ms-clear {
  display: none;
}

/* Chrome */
::-webkit-search-decoration,
::-webkit-search-cancel-button,
::-webkit-search-results-button,
::-webkit-search-results-decoration {
  display: none;
}

/* button */
.tgl {
  display: none;
}

.tgl,
.tgl:after,
.tgl:before,
.tgl *,
.tgl *:after,
.tgl *:before,
.tgl + .tgl-btn {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.tgl::-moz-selection,
.tgl:after::-moz-selection,
.tgl:before::-moz-selection,
.tgl *::-moz-selection,
.tgl *:after::-moz-selection,
.tgl *:before::-moz-selection,
.tgl + .tgl-btn::-moz-selection {
  background: none;
}

.tgl::selection,
.tgl:after::selection,
.tgl:before::selection,
.tgl *::selection,
.tgl *:after::selection,
.tgl *:before::selection,
.tgl + .tgl-btn::selection {
  background: none;
}

.tgl + .tgl-btn {
  position: relative;

  display: block;

  width: 46px;
  height: 23px;

  cursor: pointer;

  outline: 0;
}

.tgl + .tgl-btn:after,
.tgl + .tgl-btn:before {
  position: relative;

  display: block;

  width: 21px;
  height: 100%;

  content: '';
}

.tgl + .tgl-btn:after {
  left: 0;
}

.tgl + .tgl-btn:before {
  display: none;
}

.tgl:checked + .tgl-btn:after {
  left: 23px;
}

.tgl-light + .tgl-btn {
  padding: 1px;

  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;

  border-radius: 1em;
  background: #dbdbdb;
}

.tgl-light + .tgl-btn:after {
  /*background: #4caf5/*0;*/
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;

  border-radius: 50%;
  background: #fff;
}

.tgl-light:checked + .tgl-btn {
  background: #4caf50;
}

.tgl-light:checked + .tgl-btn:after {
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;

  border-radius: 50%;
  background: #fff;
}

.tgl-ios + .tgl-btn {
  padding: 2px;

  -webkit-transition: all 0.4s ease;
  -moz-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;

  border: 1px solid #e8eae9;
  border-radius: 2em;
  background: #fbfbfb;
}

.tgl-ios + .tgl-btn:after {
  -webkit-transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
  -moz-transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
  -o-transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;
  transition: left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.3s ease, margin 0.3s ease;

  border-radius: 2em;
  background: #fbfbfb;
  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 4px 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1), 0 4px 0 rgba(0, 0, 0, 0.08);
}

.tgl-ios + .tgl-btn:active {
  -webkit-box-shadow: inset 0 0 0 2em #e8eae9;
  box-shadow: inset 0 0 0 2em #e8eae9;
}

.tgl-ios + .tgl-btn:active:after {
  padding-right: 0.8em;
}

.tgl-ios:checked + .tgl-btn {
  background: #86d993;
}

/* 刷新时白块 */
body > object {
  width: 1px;
  height: 1px;
}

/* nanoscroller css override */
.nano > .nano-pane {
  background: transparent !important;
  width: 10px !important;
}

.nano > .nano-pane > .nano-slider {
  background: rgba(187, 187, 187, 0.8) !important;
  width: 6px !important;
  margin: 0 2px !important;
}

.nano > .nano-pane > .nano-slider:hover,
.nano > .nano-pane.active > .nano-slider {
  background: rgba(125, 125, 125, 0.8) !important;
}

.iti .iti__country-container .iti__selected-country,
.iti .iti__flag-container .iti__selected-flag {
  outline: none;
}

.iti--show-selected-dial-code .iti__selected-country,
.iti--separate-dial-code .iti__selected-flag {
  background-color: rgba(0, 0, 0, 0) !important;
}

.pre {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.preWrap {
  white-space: pre-wrap;
}

.MdLoader {
  animation: rotate 2s linear infinite;
  transform-origin: 50% 50%;
  display: inline-block;
}

.nowrap {
  white-space: nowrap;
}

/* 强制显示五星红旗 */
.iti__tw {
  background-position: -1049px 0 !important;
}

.cursorDefault {
  cursor: default !important;
}

.zIndex99 {
  z-index: 99;
}

.zIndex99999 {
  z-index: 99999;
}

.msgExpandDiv .faceBtn {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-position: left bottom !important;
  cursor: pointer;
}

#main {
  width: 1000px;
  max-width: 1000px;
  padding: 0 0px;
  font-size: 0;
  margin: 0px auto;
}

.overLimi_130 {
  width: expression(this.width > 130 ? '130px': true);
  max-width: 130px;
}

.shadow {
  -moz-box-shadow: 0px 0px 6px #e0e0e0;
  -webkit-box-shadow: 0px 0px 6px #e0e0e0;
  box-shadow: 0px 0px 6px #e0e0e0;
}

.z-depth-1,
nav,
.card-panel,
.card {
  box-shadow: 0 2px 6px 0px rgba(0, 0, 0, 0.15);
}

.z-depth-1-half {
  box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.12), 0 4px 15px 0 rgba(0, 0, 0, 0.1);
}

.z-depth-2 {
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.18), 0 6px 20px 0 rgba(0, 0, 0, 0.12);
}

.z-depth-3 {
  box-shadow: 0 12px 15px 0 rgba(0, 0, 0, 0.2), 0 17px 50px 0 rgba(0, 0, 0, 0.12);
}

.z-depth-4,
.modal {
  box-shadow: 0 16px 28px 0 rgba(0, 0, 0, 0.19), 0 25px 55px 0 rgba(0, 0, 0, 0.14);
}

.z-depth-5 {
  box-shadow: 0 27px 24px 0 rgba(0, 0, 0, 0.18), 0 40px 77px 0 rgba(0, 0, 0, 0.15);
}

.z-depth-6 {
  box-shadow: 0 0px 16px 0px rgba(0, 0, 0, 0.2);
}

.tipBoxShadow {
  box-shadow: 0 3px 10px 2px rgba(0, 0, 0, 0.16), 0 5px 20px 4px rgba(0, 0, 0, 0.1);
  -weblit-box-shadow: 0 3px 10px 2px rgba(0, 0, 0, 0.16), 0 5px 20px 4px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 10px 2px rgba(0, 0, 0, 0.16), 0 5px 20px 4px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 3px 10px 2px rgba(0, 0, 0, 0.16), 0 5px 20px 4px rgba(0, 0, 0, 0.1);
}

.taskBoxShadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.13), 0 2px 6px rgba(0, 0, 0, 0.1);
}

.card {
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  display: block;
}

.card:after {
  content: '';
  display: table;
  clear: both;
}

.circle {
  border-radius: 50%;
}

.valignWrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.valignWrapper .valign {
  display: block;
}

input[type='radio'],
input[type='radio']:before,
input[type='radio']:after,
input[type='checkbox'],
input[type='checkbox']:before,
input[type='checkbox']:after {
  box-sizing: border-box;
  vertical-align: middle;
  border: 1px solid #ccc;
}

input[type='radio'] {
  position: relative;
  margin-top: 5px;
  margin-right: 4px;
  vertical-align: -4px;
  border: none;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
}

input[type='radio']:focus {
  outline: none;
}

input[type='radio']:before,
input[type='radio']:after {
  content: '';
  display: block;
  width: 18px;
  height: 18px;
  margin-top: -3px;
  border-radius: 50%;
  -webkit-transition: 240ms;
  -o-transition: 240ms;
  -ms-transition: 240ms;
  -moz-transition: 240ms;
  transition: 240ms;
}

input[type='radio']:before {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #2196f3 !important;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  -moz-transform: scale(0);
  transform: scale(0);
}

input[type='radio']:checked:before {
  -webkit-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -o-transform: scale(0.7);
  -moz-transform: scale(0.7);
  transform: scale(0.7);
}

input[type='radio']:disabled:checked:before {
  background-color: #bbbbbb !important;
}

input[type='radio']:checked:after {
  border-color: #2196f3 !important;
}

input[type='radio']:disabled:after,
input[type='radio']:disabled:checked:after {
  border-color: #bbbbbb !important;
}

input[type='checkbox'] {
  position: relative;
  /*vertical-align: -4px;*/
  border: none;
  -webkit-appearance: none;
  -ms-appearance: none;
  -moz-appearance: none;
  -o-appearance: none;
  appearance: none;
  cursor: pointer;
}

input[type='checkbox']:focus {
  outline: none;
}

input[type='checkbox']:after {
  content: '';
  display: block;
  width: 18px;
  height: 18px;
  margin-top: -2px;
  margin-right: 5px;
  border-radius: 2px;
  -webkit-transition: 240ms;
  -o-transition: 240ms;
  -ms-transform: 240ms;
  -moz-transform: 240ms;
  transition: 240ms;
  background: white;
}

input[type='checkbox']:checked:before {
  content: '';
  position: absolute;
  top: 0;
  left: 6px;
  width: 6px;
  height: 12px;
  border: 2px solid #fff;
  border-top-width: 0;
  border-left-width: 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  transform: rotate(45deg);
}

input[type='checkbox']:checked:after {
  background-color: #2196f3 !important;
  border-color: #2196f3 !important;
}

input[type='checkbox']:disabled {
  cursor: not-allowed;
}

input[type='checkbox']:disabled:after {
  border-color: #bbbbbb !important;
}

input[type='checkbox']:disabled:checked:after {
  background-color: #bbbbbb !important;
  border-color: transparent !important;
}

@-webkit-keyframes clipLoader {
  0% {
    transform: rotate(0deg) scale(1);
    -ms-transform: rotate(0deg) scale(1);
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(0.8);
    -ms-transform: rotate(180deg) scale(0.8);
    -webkit-transform: rotate(180deg) scale(0.8);
    -moz-transform: rotate(180deg) scale(0.8);
    -o-transform: rotate(180deg) scale(0.8);
  }

  100% {
    transform: rotate(360deg) scale(1);
    -ms-transform: rotate(360deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }
}

@-moz-keyframes clipLoader {
  0% {
    transform: rotate(0deg) scale(1);
    -ms-transform: rotate(0deg) scale(1);
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(0.8);
    -ms-transform: rotate(180deg) scale(0.8);
    -webkit-transform: rotate(180deg) scale(0.8);
    -moz-transform: rotate(180deg) scale(0.8);
    -o-transform: rotate(180deg) scale(0.8);
  }

  100% {
    transform: rotate(360deg) scale(1);
    -ms-transform: rotate(360deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }
}

@-o-keyframes clipLoader {
  0% {
    transform: rotate(0deg) scale(1);
    -ms-transform: rotate(0deg) scale(1);
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(0.8);
    -ms-transform: rotate(180deg) scale(0.8);
    -webkit-transform: rotate(180deg) scale(0.8);
    -moz-transform: rotate(180deg) scale(0.8);
    -o-transform: rotate(180deg) scale(0.8);
  }

  100% {
    transform: rotate(360deg) scale(1);
    -ms-transform: rotate(360deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }
}

@-ms-keyframes clipLoader {
  0% {
    transform: rotate(0deg) scale(1);
    -ms-transform: rotate(0deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(0.8);
    -ms-transform: rotate(180deg) scale(0.8);
    -webkit-transform: rotate(180deg) scale(0.8);
    -moz-transform: rotate(180deg) scale(0.8);
    -o-transform: rotate(180deg) scale(0.8);
  }

  100% {
    transform: rotate(360deg) scale(1);
    -ms-transform: rotate(360deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }
}

@keyframes clipLoader {
  0% {
    transform: rotate(0deg) scale(1);
    -ms-transform: rotate(0deg) scale(1);
    -webkit-transform: rotate(0deg) scale(1);
    -moz-transform: rotate(0deg) scale(1);
    -o-transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(0.8);
    -ms-transform: rotate(180deg) scale(0.8);
    -webkit-transform: rotate(180deg) scale(0.8);
    -moz-transform: rotate(180deg) scale(0.8);
    -o-transform: rotate(180deg) scale(0.8);
  }

  100% {
    transform: rotate(360deg) scale(1);
    -ms-transform: rotate(360deg) scale(1);
    -webkit-transform: rotate(360deg) scale(1);
    -moz-transform: rotate(360deg) scale(1);
    -o-transform: rotate(360deg) scale(1);
  }
}

.clipLoader {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 2px solid;
  border-bottom-color: transparent !important;
  background: transparent !important;
  animation: clipLoader 0.75s 0s infinite linear;
  -webkit-animation: clipLoader 0.75s 0s infinite linear;
  -ms-animation: clipLoader 0.75s 0s infinite linear;
  -moz-animation: clipLoader 0.75s 0s infinite linear;
  -o-animation: clipLoader 0.75s 0s infinite linear;
  animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -ms-animation-fill-mode: both;
  -webkit-animation-fill-mode: both;
  -o-animation-fill-mode: both;
}

.fileIcon-folder {
  background: url('newfileIcons/folder.png') no-repeat;
  width: 249px;
  height: 205px;
}

.fileIcon-folderShared {
  background: url('newfileIcons/folderShared.png') no-repeat;
  width: 249px;
  height: 205px;
}

.fileIcon-myfile {
  background: url('newfileIcons/folder.png') no-repeat;
  width: 249px;
  height: 205px;
}

.fileIcon-7z {
  background: url('newfileIcons/rar.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-ai {
  background: url('newfileIcons/ai.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-doc,
.fileIcon-docx {
  background: url('newfileIcons/doc.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-excel {
  background: url('newfileIcons/xls.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-img {
  background: url('newfileIcons/jpg.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-link {
  background: url('newfileIcons/url.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-mmap {
  background: url('newfileIcons/mmap.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-pdf {
  background: url('newfileIcons/pdf.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-ppt {
  background: url('newfileIcons/ppt.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-psd {
  background: url('newfileIcons/psd.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-rar {
  background: url('newfileIcons/rar.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-txt {
  background: url('newfileIcons/txt.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-vsd {
  background: url('newfileIcons/vsd.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-word {
  background: url('newfileIcons/docx.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-mdy {
  background: url('newfileIcons/mdy.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-xmind {
  background: url('newfileIcons/xmind.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-zip {
  background: url('newfileIcons/rar.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-aep {
  background: url('newfileIcons/aep.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-apk {
  background: url('newfileIcons/apk.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-ascx {
  background: url('newfileIcons/ascx.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-db {
  background: url('newfileIcons/db.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-dmg {
  background: url('newfileIcons/dmg.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-dwg {
  background: url('newfileIcons/dwg.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-eps {
  background: url('newfileIcons/eps.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-exe {
  background: url('newfileIcons/exe.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-html {
  background: url('newfileIcons/html.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-indd {
  background: url('newfileIcons/indd.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-iso {
  background: url('newfileIcons/iso.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-key {
  background: url('newfileIcons/key.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-ma {
  background: url('newfileIcons/ma.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-max {
  background: url('newfileIcons/max.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-mp3 {
  background: url('newfileIcons/mp3.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-mp4 {
  background: url('newfileIcons/mp4.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-numbers {
  background: url('newfileIcons/numbers.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-obj {
  background: url('newfileIcons/obj.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-pages {
  background: url('newfileIcons/pages.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-prt {
  background: url('newfileIcons/prt.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-rp {
  background: url('newfileIcons/rp.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-skp {
  background: url('newfileIcons/skp.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-xd {
  background: url('newfileIcons/xd.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-code {
  background: url('newfileIcons/code.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-md {
  background: url('newfileIcons/md.png') no-repeat;
  width: 175px;
  height: 200px;
}

.fileIcon-worksheet {
  background: url('newfileIcons/ic_file_worksheet.png') no-repeat;
}

.fileIcon-folder,
.fileIcon-folderShared,
.fileIcon-myfile,
.fileIcon-7z,
.fileIcon-ai,
.fileIcon-doc,
.fileIcon-docx,
.fileIcon-excel,
.fileIcon-img,
.fileIcon-link,
.fileIcon-mmap,
.fileIcon-pdf,
.fileIcon-ppt,
.fileIcon-psd,
.fileIcon-rar,
.fileIcon-txt,
.fileIcon-vsd,
.fileIcon-word,
.fileIcon-mdy,
.fileIcon-xmind,
.fileIcon-zip,
.fileIcon-aep,
.fileIcon-apk,
.fileIcon-ascx,
.fileIcon-db,
.fileIcon-dmg,
.fileIcon-dwg,
.fileIcon-eps,
.fileIcon-exe,
.fileIcon-html,
.fileIcon-indd,
.fileIcon-iso,
.fileIcon-key,
.fileIcon-ma,
.fileIcon-max,
.fileIcon-mp3,
.fileIcon-mp4,
.fileIcon-numbers,
.fileIcon-obj,
.fileIcon-pages,
.fileIcon-prt,
.fileIcon-rp,
.fileIcon-skp,
.fileIcon-xd,
.fileIcon-code,
.fileIcon-md,
.fileIcon-worksheet {
  display: inline-block;
  -webkit-background-size: cover !important;
  background-size: cover !important;
  overflow: hidden;
  text-indent: -9999px;
  text-align: left;
}

.flexColumn {
  display: -ms-flexbox;
  display: flex;
  display: -webkit-flex;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}

.flexRow {
  display: flex;
  min-width: 0;
}

.flexCenter {
  display: flex;
  align-items: center;
}

.inlineFlexRow {
  display: inline-flex;
  min-width: 0;
}

.flex {
  -webkit-flex: 1;
  flex: 1;
  -ms-flex: 1;
}

.alignItemsCenter {
  align-items: center;
}

.justifyContentCenter {
  justify-content: center;
}

.justifyContentLeft {
  justify-content: left !important;
}

.justifyContentRight {
  justify-content: right !important;
}

.rc-trigger-popup-mask {
  background-color: rgba(0, 0, 0, 0) !important;
}

.ant-checkbox-input,
.ant-radio-input {
  position: absolute !important;
}

html:not(.appHomeMobile) .ant-message {
  top: 54px;
}

.pcToast.ant-message-notice .ant-message-notice-content {
  font-size: 14px;
  font-weight: 500;
  border-radius: 40px;
  padding: 10px 20px 10px 18px;
  word-break: break-word;
}

.ant-select-dropdown.withIsEmpty .ant-select-item.isEmpty {
  margin-bottom: 10px;
}

.ant-select-dropdown.withIsEmpty .ant-select-item.isEmpty .ant-select-item-option-content > span {
  padding: 0 !important;
  color: inherit !important;
}

.ant-select-dropdown.withIsEmpty .ant-select-item.isEmpty::after {
  position: absolute;
  bottom: -6px;
  content: '';
  left: 0;
  right: 0;
  border-top: 1px solid #ddd;
}

.pointerEventsAuto {
  pointer-events: auto !important;
}

/* antd Select 样式  begin---> */
.mdAntSelect {
  font-size: 13px !important;
}

.mdAntSelect.ant-select-open .ant-select-selector {
  border-color: #2196f3;
  box-shadow: none;
}

.mdAntSelect.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  border: 1px solid #2196f3;
  box-shadow: none;
}

.mdAntSelect:not(.ant-select-customize-input) .ant-select-selector {
  border: 1px solid #ddd;
  height: 36px !important;
  border-radius: 4px !important;
}

.mdAntSelect:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
  height: 34px;
}

.noBorder:not(.ant-select-customize-input) .ant-select-selector {
  border: none !important;
}

.mdAntSelect.ant-select-single.ant-select-show-arrow .ant-select-selection-item,
.mdAntSelect.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
  line-height: 34px;
}

.mdAntSelect.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
  color: #bdbdbd !important;
}

.mdAntSelect.ant-select-single.ant-select-show-arrow .ant-select-selection-item {
  color: #151515 !important;
}

.mdAntSelect.ant-select-show-arrow .ant-select-selection-search {
  right: 25px;
}

.mdAntSelect .ant-select-dropdown {
  overflow: auto;
}

.mdAntSelect .ant-select-arrow {
  width: 16px;
  height: 16px;
  margin-top: -8px;
  top: 50%;
}

.mdAntSelect .ant-select-clear {
  width: 16px;
  height: 16px;
  right: 9px;
  margin-top: -7px;
}

.mdAntSelect .ant-select-selection-overflow-item {
  margin-top: -1px;
}
.mdAntSelectOption.ant-select-item {
  min-height: 36px;
  line-height: 26px;
}
.mdAntSelectOption.ant-select-item:hover {
  color: #fff;
  background-color: #2196f3;
}
.mdAntSelectOption.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  color: #fff;
  background-color: #2196f3;
}
/* ----> antd Select 样式 end */

.ant-tooltip {
  color: #212121;
}

.ant-tooltip .ant-tooltip-inner {
  background-color: #212121;
  border-radius: 4px;
  font-size: 13px;
  font-weight: bold;
  padding: 5px 10px;
}

.ant-tooltip .ant-tooltip-arrow-content {
  --antd-arrow-background-color: #212121;
}

.hoverText:hover {
  cursor: pointer;
  color: #2196f3 !important;
}

.hoverText:hover i {
  color: #2196f3;
}

.fixedScreen {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#kf5-support-btn,
.fullWrapper .appPkgHeaderWrap,
.fullWrapper .workSheetLeft {
  display: none !important;
}

.ant-dropdown .ming.Menu {
  width: 100%;
}

input::-webkit-contacts-auto-fill-button {
  visibility: hidden;
  display: none !important;
  pointer-events: none;
  position: absolute;
  right: 0;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.hr-line:not(:last-child):not(:first-child) {
  border-top: 1px solid #f3f3f3;
  margin: 4px 0;
}

.ant-modal .ant-modal-content,
.ant-modal .ant-modal-body {
  font-size: 13px;
}
