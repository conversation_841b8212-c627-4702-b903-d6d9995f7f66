﻿[data-tip] {
  position: relative;
  display: inline-block;
}

[data-tip]:before,
[data-tip]:after {
  text-decoration: none;
  position: absolute;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  visibility: hidden;
  opacity: 0;
  z-index: 1000000;
  pointer-events: none;
  -webkit-transition: 0.3s ease;
  -moz-transition: 0.3s ease;
  transition: 0.3s ease;
  -webkit-transition-delay: 0ms;
  -moz-transition-delay: 0ms;
  transition-delay: 0ms;
}

[data-tip].hover:before,
[data-tip].hover:after,
[data-tip]:hover:before,
[data-tip]:hover:after {
  visibility: visible;
  opacity: 1;
}

[data-tip].hover:before,
[data-tip].hover:after,
[data-tip]:hover:before,
[data-tip]:hover:after {
  -webkit-transition-delay: 100ms;
  -moz-transition-delay: 100ms;
  transition-delay: 100ms;
}

[data-tip]:before {
  content: '';
  position: absolute;
  background: transparent;
  border: 6px solid transparent;
  z-index: 1000001;
}

[data-tip]:after {
  content: attr(data-tip);
  background: #212121;
  color: #fff;
  padding: 5px 10px;
  font-size: 13px;
  line-height: 1.5;
  white-space: nowrap;
  text-shadow: 0 -1px 0 #000;
  border-radius: 4px;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
  font-weight: bold;
}

[data-tip='']:before,
[data-tip='']:after {
  display: none !important;
}

.tip-top:before,
.tip-top-left:before,
.tip-top-right:before {
  border-top-color: #212121;
}

.tip-bottom:before,
.tip-bottom-left:before,
.tip-bottom-right:before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):before {
  border-bottom-color: #212121;
}

.tip-left:before {
  border-left-color: #212121;
}

.tip-right:before {
  border-right-color: #212121;
}

/**
 * top tooltip
 */

.tip-top:before {
  margin-bottom: -11px;
}

.tip-top:before,
.tip-top:after {
  bottom: 100%;
  left: 50%;
}

.tip-top:before {
  left: calc(50% - 6px);
}

.tip-top:after {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  transform: translateX(-50%);
}

.tip-top.hover:before,
.tip-top:hover:before,
.tip-top:focus:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px);
}

.tip-top.hover:after,
.tip-top:hover:after,
.tip-top:focus:after {
  -webkit-transform: translateX(-50%) translateY(-8px);
  -moz-transform: translateX(-50%) translateY(-8px);
  transform: translateX(-50%) translateY(-8px);
}

/**
 * bottom tooltip
 */

.tip-bottom:before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):before {
  margin-top: -11px;
}

.tip-bottom:before,
.tip-bottom:after,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):after {
  top: 100%;
  left: 50%;
}

.tip-bottom:before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):before {
  left: calc(50% - 6px);
}

.tip-bottom:after,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):after {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  transform: translateX(-50%);
}

.tip-bottom.hover:before,
.tip-bottom:hover:before,
.tip-bottom:focus:before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):hover:before,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):focus:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px);
}

.tip-bottom.hover:after,
.tip-bottom:hover:after,
.tip-bottom:focus:after,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right).hover:after,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):hover:after,
[data-tip]:not(.tip-top):not(.tip-left):not(.tip-right):not(.tip-top-left):not(.tip-top-right):not(.tip-bottom-left):not(.tip-bottom-right):focus:after {
  -webkit-transform: translateX(-50%) translateY(8px);
  -moz-transform: translateX(-50%) translateY(8px);
  transform: translateX(-50%) translateY(8px);
}

/**
 * right tooltip
 */

.tip-right:before {
  margin-left: -11px;
  margin-bottom: -6px;
}

.tip-right:after {
  margin-bottom: -14px;
}

.tip-right:before,
.tip-right:after {
  left: 100%;
  bottom: 50%;
}

.tip-right.hover:before,
.tip-right:hover:before,
.tip-right:focus:before {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px);
}

.tip-right.hover:after,
.tip-right:hover:after,
.tip-right:focus:after {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px);
}

/**
 * left tooltip
 */

.tip-left:before {
  margin-right: -11px;
  margin-bottom: -6px;
}

.tip-left:after {
  margin-bottom: -14px;
}

.tip-left:before,
.tip-left:after {
  right: 100%;
  bottom: 50%;
}

.tip-left.hover:before,
.tip-left:hover:before,
.tip-left:focus:before {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px);
}

.tip-left.hover:after,
.tip-left:hover:after,
.tip-left:focus:after {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px);
}

/**
 * top-left tooltip
 */

.tip-top-left:before {
  margin-bottom: -11px;
}

.tip-top-left:before,
.tip-top-left:after {
  bottom: 100%;
  left: 50%;
}

.tip-top-left:before {
  left: calc(50% - 6px);
}

.tip-top-left:after {
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  transform: translateX(-100%);
}

.tip-top-left:after {
  margin-left: 12px;
}

.tip-top-left.hover:before,
.tip-top-left:hover:before,
.tip-top-left:focus:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px);
}

.tip-top-left.hover:after,
.tip-top-left:hover:after,
.tip-top-left:focus:after {
  -webkit-transform: translateX(-100%) translateY(-8px);
  -moz-transform: translateX(-100%) translateY(-8px);
  transform: translateX(-100%) translateY(-8px);
}

/**
 * top-right tooltip
 */

.tip-top-right:before {
  margin-bottom: -11px;
}

.tip-top-right:before,
.tip-top-right:after {
  bottom: 100%;
  left: 50%;
}

.tip-top-right:before {
  left: calc(50% - 6px);
}

.tip-top-right:after {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  transform: translateX(0);
}

.tip-top-right:after {
  margin-left: -12px;
}

.tip-top-right.hover:before,
.tip-top-right:hover:before,
.tip-top-right:focus:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px);
}

.tip-top-right.hover:after,
.tip-top-right:hover:after,
.tip-top-right:focus:after {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px);
}

/**
 * bottom-left tooltip
 */

.tip-bottom-left:before {
  margin-top: -11px;
}

.tip-bottom-left:before,
.tip-bottom-left:after {
  top: 100%;
  left: 50%;
}

.tip-bottom-left:before {
  left: calc(50% - 6px);
}

.tip-bottom-left:after {
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  transform: translateX(-100%);
}

.tip-bottom-left:after {
  margin-left: 12px;
}

.tip-bottom-left.hover:before,
.tip-bottom-left:hover:before,
.tip-bottom-left:focus:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px);
}

.tip-bottom-left.hover:after,
.tip-bottom-left:hover:after,
.tip-bottom-left:focus:after {
  -webkit-transform: translateX(-100%) translateY(8px);
  -moz-transform: translateX(-100%) translateY(8px);
  transform: translateX(-100%) translateY(8px);
}

/**
 * bottom-right tooltip
 */

.tip-bottom-right:before {
  margin-top: -11px;
}

.tip-bottom-right:before,
.tip-bottom-right:after {
  top: 100%;
  left: 50%;
}

.tip-bottom-right:before {
  left: calc(50% - 6px);
}

.tip-bottom-right:after {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  transform: translateX(0);
}

.tip-bottom-right:after {
  margin-left: -12px;
}

.tip-bottom-right.hover:before,
.tip-bottom-right:hover:before,
.tip-bottom-right:focus:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px);
}

.tip-bottom-right.hover:after,
.tip-bottom-right:hover:after,
.tip-bottom-right:focus:after {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px);
}

.tip-delay {
  -webkit-transition-delay: 1000ms;
  -moz-transition-delay: 1000ms;
  transition-delay: 1000ms;
}
