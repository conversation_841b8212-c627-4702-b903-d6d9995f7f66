# AI Assistant Component

众织 Copilot AI 助手组件，提供智能对话、代码编辑和执行等功能。

## 主要功能

- 智能对话：支持与AI进行自然语言交互
- 代码生成与编辑：可以生成、查看和编辑代码
- 多模型支持：可以切换不同的AI模型
- 多级树形菜单：支持无限层级的功能分类
- 定向启动：支持通过指定key直接打开特定功能页

## 使用方法

### 基本使用

```jsx
import { AIAssistant } from 'components/AIAssistant/components';

// 在应用中任意位置添加
const YourComponent = () => {
  return (
    <div>
      {/* 你的组件内容 */}
      <AIAssistant />
    </div>
  );
};
```

### 定向启动助手

使用 `targetKey` 属性可以直接定位到特定菜单，只显示该菜单及其子菜单：

```jsx
import { FloatingButton } from 'components/AIAssistant/components';

// 创建只打开"模型"功能的按钮
const ModelAssistant = () => {
  return <FloatingButton targetKey="model" />;
};

// 创建只打开"元数据"功能的按钮
const MetadataAssistant = () => {
  return <FloatingButton targetKey="metadata" />;
};
```

> 注意：`targetKey` 只对一级菜单生效，使用时需确保菜单数据中存在对应的key。

### 在应用顶层使用

为避免多次渲染AIAssistant组件，建议在应用顶层使用：

```jsx
import { AIAssistant, FloatingButton } from 'components/AIAssistant/components';

// 应用顶层组件
const App = () => {
  return (
    <>
      <Router>
        {/* 路由配置 */}
      </Router>
      
      {/* 在不同页面使用定向按钮 */}
      <FloatingButton targetKey="model" />
      
      {/* 只需要一个AIAssistant实例 */}
      <AIAssistant />
    </>
  );
};
```

## 菜单配置

AIAssistant支持多级树形菜单结构，可以通过后端API获取或在前端配置。菜单数据格式示例：

```javascript
const menuData = [
  {
    key: 'model',            // 菜单键名(必须)
    icon: <AppstoreOutlined />, // 图标(可选)
    title: 'AI 生成模型',     // 显示名称(必须)
    children: [              // 子菜单(可选)
      {
        key: 'model.domain',
        title: '业务域',
        children: [          // 支持多级嵌套
          {
            key: 'model.domain.a',
            title: '业务域子类A'
          },
          {
            key: 'model.domain.b',
            title: '业务域子类B'
          }
        ]
      },
      {
        key: 'model.entities',
        title: '实体'
      }
    ]
  },
  // 更多菜单项...
];
```

## 技术实现

- 使用React函数式组件和Hooks管理状态
- Ant Design提供UI组件支持
- 递归渲染支持无限层级的树形菜单
- 支持响应式布局，适配移动端和桌面端 
