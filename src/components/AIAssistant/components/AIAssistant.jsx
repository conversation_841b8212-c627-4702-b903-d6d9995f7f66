import React, { useState, useEffect, Fragment } from 'react';
import { Drawer, message } from 'antd';
import { CloseOutlined} from '@ant-design/icons';
import { AIMenuLayout } from '../styles';
import { useResponsive, useAIChat } from '../hooks';
import {
  ChatContent,
  ChatInput,
  CodeEditor,
  ExecutionConfirm,
  FloatingButton,
  FunctionMenu,
  SplitView
} from './';
import { copyToClipboard } from '../utils';

/**
 * AI助手主组件
 */
const AIAssistant = () => {
  const [visible, setVisible] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [executingCode, setExecutingCode] = useState(null);
  const [activeTargetKey, setActiveTargetKey] = useState(null);
  const [contextData, setContextData] = useState({});
  const [uiContext, setUiContext] = useState({});

  // 使用自定义钩子
  const { isMobile } = useResponsive();
  const {
    conversations,
    loading,
    currentTab,
    selectedModel,
    inputValue,
    editingCode,
    viewMode,
    handleTabChange,
    handleModelChange,
    handleInputChange,
    sendMessage,
    handleKeyPress,
    clearConversation,
    viewCode: originalViewCode,
    closeCodeEditor,
    updateEditingCode,
    regenerateAIResponse,
    executeCode,
    hasPendingClarification,
    markClarificationAsSubmitted,
    setConversations
  } = useAIChat();

  // 添加事件监听器，用于从外部组件打开AI助手
  useEffect(() => {
    const handleOpenAssistant = (event) => {
      const { targetKey, contextData, uiContext } = event.detail;
      showDrawer(targetKey, contextData, uiContext);
    };

    document.addEventListener('openAIAssistant', handleOpenAssistant);

    return () => {
      document.removeEventListener('openAIAssistant', handleOpenAssistant);
    };
  }, []);

  // 覆盖原始viewCode方法，添加自动全屏功能
  const viewCode = (block) => {
    setIsFullscreen(true); // 自动设置为全屏模式
    originalViewCode(block);
  };

  // 打开抽屉
  const showDrawer = (targetKey, contextData = {}, uiContextData = {}) => {
    setVisible(true);

    // 设置后端上下文数据（将发送到API）
    setContextData(contextData);

    // 设置UI上下文数据（仅用于UI显示，不发送到API）
    setUiContext(uiContextData);

    if (targetKey) {
      setActiveTargetKey(targetKey);
      handleTabChange(targetKey);

      // 如果有上下文数据且不为空，自动填入输入框
      if (contextData && contextData.initialPrompt) {
        handleInputChange({ target: { value: contextData.initialPrompt } });
      }

      // 如果设置了自动发送标志，自动发送初始消息
      if (uiContextData && uiContextData.autoSend && contextData && contextData.initialPrompt) {
        setTimeout(() => {
          sendMessage(contextData.initialPrompt, { contextData });
        }, 300);
      }
    }
  };

  // 关闭抽屉
  const onClose = () => {
    setVisible(false);
    if (editingCode) {
      closeCodeEditor();
    }
    if (executingCode) {
      setExecutingCode(null);
    }
    setIsFullscreen(false);
    setActiveTargetKey(null);
    setContextData({});
    setUiContext({});
  };

  // 处理标签切换
  const handleMenuClick = (e) => {
    // 只处理一级菜单项或子菜单项
    const key = e.key;
    handleTabChange(key);
  };

  // 复制消息内容
  const handleCopyMessage = async (message) => {
    try {
      await copyToClipboard(message.content);
      message.success('消息已复制到剪贴板');
    } catch (error) {
      message.error('复制失败，请重试');
    }
  };

  // 编辑用户消息
  const handleEditMessage = (msg, index) => {
    // 设置输入框的值为要编辑的消息内容
    handleInputChange({ target: { value: msg.content } });
    // 可以添加一些指示当前正在编辑消息的状态
    message.info('您正在编辑原始消息');
  };

  // 保存代码
  const handleSaveCode = (updatedBlock) => {
    updateEditingCode(updatedBlock.code);
  };

  // 确认执行代码
  const confirmExecuteCode = (codeBlock) => {
    setExecutingCode(codeBlock);
  };

  // 取消执行代码
  const cancelExecuteCode = () => {
    setExecutingCode(null);
  };

  // 执行代码
  const handleExecuteCode = (codeBlock) => {
    executeCode(codeBlock);
    setExecutingCode(null);
  };

  // 切换全屏模式
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理澄清问题完成
  const handleClarificationComplete = (messageId) => {
    // 标记澄清问题为已提交
    if (messageId) {
      markClarificationAsSubmitted(messageId);
    }
    console.log('澄清问题已完成，消息ID:', messageId);
  };

  // 新建聊天
  const handleNewChat = () => {
    clearConversation();
    message.success('已创建新对话');
  };

  // 打开设置
  const handleOpenSettings = () => {
    message.info('设置功能即将上线');
  };

  // 渲染主界面
  const renderMainContent = () => {
    const messages = conversations[currentTab] || [];

    // 渲染代码执行确认模态框
    if (executingCode) {
      return (
        <ExecutionConfirm
          codeBlock={executingCode}
          onExecute={handleExecuteCode}
          onCancel={cancelExecuteCode}
          onView={(block) => {
            cancelExecuteCode();
            viewCode(block);
          }}
        />
      );
    }

    // 根据视图模式渲染内容
    switch (viewMode) {
      case 'edit':
        return (
          <CodeEditor
            codeBlock={editingCode}
            onClose={closeCodeEditor}
            onExecute={confirmExecuteCode}
            onSave={handleSaveCode}
          />
        );
      case 'split':
        return (
          <SplitView
            codeBlock={editingCode}
            messages={messages}
            currentTab={currentTab}
            loading={loading}
            isFullscreen={isFullscreen}
            onViewCode={viewCode}
            onExecuteCode={confirmExecuteCode}
            onCopyMessage={handleCopyMessage}
            onRegenerate={regenerateAIResponse}
            onEditMessage={handleEditMessage}
            onClose={closeCodeEditor}
            onSave={handleSaveCode}
            onToggleFullscreen={toggleFullscreen}
            contextData={contextData}
            uiContext={uiContext}
            onClarificationComplete={handleClarificationComplete}
            setConversations={setConversations}
          />
        );
      case 'chat':
      default:
        return (
          <React.Fragment>
            {/* <ChatHeader
              currentTab={currentTab}
              onClear={clearConversation}
              onNewChat={handleNewChat}
              onOpenSettings={handleOpenSettings}
            /> */}
            <ChatContent
              messages={messages}
              currentTab={currentTab}
              loading={loading}
              onViewCode={viewCode}
              onExecuteCode={confirmExecuteCode}
              onCopyMessage={handleCopyMessage}
              onRegenerate={regenerateAIResponse}
              onEditMessage={handleEditMessage}
              contextData={contextData}
              uiContext={uiContext}
              onClarificationComplete={handleClarificationComplete}
              setConversations={setConversations}
            />
            <ChatInput
              value={inputValue}
              onChange={handleInputChange}
              onSend={sendMessage}
              onKeyPress={handleKeyPress}
              loading={loading}
              currentTab={currentTab}
              onClear={clearConversation}
              onNewChat={handleNewChat}
              onOpenSettings={handleOpenSettings}
              modelValue={selectedModel}
              onModelChange={handleModelChange}
              contextData={contextData}
              uiContext={uiContext}
              hasPendingClarification={hasPendingClarification()}
            />
          </React.Fragment>
        );
    }
  };

  // 渲染功能菜单
  const renderFunctionMenu = () => {
    if (viewMode === 'edit') return null;

    return (
      <FunctionMenu
        currentTab={currentTab}
        activeTargetKey={activeTargetKey}
        onClick={handleMenuClick}
        isMobile={isMobile}
        contextData={contextData}
        uiContext={uiContext}
      />
    );
  };

  return (
    <Fragment>
      <FloatingButton onClick={showDrawer} />

      <Drawer
        placement="right"
        width={isFullscreen ? '100%' : (isMobile ? '100%' : 1600)}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ padding: 0, height: '100%' }}
        title={uiContext.title || "众织 Copilot"}
        headerStyle={{
          borderBottom: '1px solid #f0f0f0',
          padding: '12px 24px',
          background: 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
          color: '#fff'
        }}
        destroyOnClose={false}
        closeIcon={<CloseOutlined style={{ color: '#fff' }} />}
      >
        <AIMenuLayout>
          {/* 左侧菜单 */}
          {renderFunctionMenu()}

          {/* 右侧内容区 */}
          <div className={`ai-chat-area ${viewMode === 'edit' || viewMode === 'split' ? 'code-view-mode' : ''}`}>
            {renderMainContent()}
          </div>
        </AIMenuLayout>
      </Drawer>
    </Fragment>
  );
};

export default AIAssistant;
