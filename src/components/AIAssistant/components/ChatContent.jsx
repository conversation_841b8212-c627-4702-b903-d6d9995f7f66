import React, { useRef, useEffect, useState } from 'react';
import { Spin, Tag, Space } from 'antd';
import Message from './Message';
import EmptyChat from './EmptyChat';

/**
 * 上下文信息横幅组件
 * @param {Object} contextData - 上下文数据
 * @param {Object} uiContext - UI上下文数据
 */
const ContextBanner = ({ contextData, uiContext }) => {
  // 如果没有上下文数据，则不显示横幅
  if ((!contextData || Object.keys(contextData).length === 0) &&
      (!uiContext || Object.keys(uiContext).length === 0)) {
    return null;
  }

  return (
    <div className="context-banner" style={{
      padding: '8px 16px',
      backgroundColor: '#f0f7ff',
      borderRadius: '4px',
      marginBottom: '16px',
      borderLeft: '4px solid #1890ff'
    }}>
      <Space wrap>
        {/* 显示UI上下文数据 */}
        {uiContext && uiContext.modelName && (
          <Tag color="blue">模型: {uiContext.modelName}</Tag>
        )}
        {uiContext && uiContext.pageType && (
          <Tag color="purple">页面: {uiContext.pageType}</Tag>
        )}

        {/* 显示API上下文数据 */}
        {contextData && Object.entries(contextData).map(([key, value]) => {
          // 跳过特殊字段
          if (['initialPrompt'].includes(key)) {
            return null;
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return <Tag key={key} color="default">{key}: {value}</Tag>;
          }
          return null;
        })}

        {/* 显示其他UI上下文数据 */}
        {uiContext && Object.entries(uiContext).map(([key, value]) => {
          // 跳过已显示的和特殊字段
          if (['modelName', 'pageType', 'title', 'autoSend'].includes(key)) {
            return null;
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return <Tag key={`ui-${key}`} color="cyan">{key}: {value}</Tag>;
          }
          return null;
        })}
      </Space>
    </div>
  );
};

/**
 * 将消息分组（按照日期和发送者）
 * @param {Array} messages - 消息数组
 * @returns {Array} 分组后的消息数组
 */
const groupMessages = (messages) => {
  if (!messages || messages.length === 0) return [];

  // 按照相同角色分组
  const groups = [];
  let currentGroup = [];

  messages.forEach((message, index) => {
    // 如果是第一条消息或者角色与上一条不同，创建新组
    if (index === 0 || message.role !== messages[index - 1].role) {
      if (currentGroup.length > 0) {
        groups.push([...currentGroup]);
      }
      currentGroup = [message];
    } else {
      currentGroup.push(message);
    }
  });

  // 添加最后一组
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
};

/**
 * 聊天内容显示组件
 * @param {Object} props - 组件属性
 * @param {Array} props.messages - 消息数组
 * @param {string} props.currentTab - 当前标签
 * @param {boolean} props.loading - 加载状态
 * @param {Function} props.onViewCode - 查看代码回调
 * @param {Function} props.onExecuteCode - 执行代码回调
 * @param {Function} props.onCopyMessage - 复制消息回调
 * @param {Function} props.onRegenerate - 重新生成回调
 * @param {Function} props.onEditMessage - 编辑消息回调（可选）
 * @param {Object} props.contextData - 后端上下文数据（可选）
 * @param {Object} props.uiContext - UI上下文数据（可选）
 * @param {Function} props.onClarificationComplete - 澄清问题完成回调（可选）
 * @param {Function} props.setConversations - 设置对话状态的函数（用于流式更新）
 */
const ChatContent = ({
  messages = [],
  currentTab,
  loading,
  onViewCode,
  onExecuteCode,
  onCopyMessage,
  onRegenerate,
  onEditMessage,
  contextData = {},
  uiContext = {},
  onClarificationComplete,
  setConversations
}) => {
  const contentRef = useRef(null);
  const messageGroups = groupMessages(messages);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [isScrolled, setIsScrolled] = useState(false);
  const scrollInterval = useRef(null);

  // 滚动到底部
  const scrollToBottom = () => {
    if (contentRef.current && isAutoScrollEnabled) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current) return;

      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      // 当用户在底部附近时，启用自动滚动
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;

      setIsAutoScrollEnabled(isNearBottom);
      setIsScrolled(!isNearBottom);
    };

    const contentElement = contentRef.current;
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (contentElement) {
        contentElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // 在消息变化时滚动到底部
  useEffect(() => {
    if (messages.length > 0 && isAutoScrollEnabled) {
      scrollToBottom();
    }
  }, [messages, isAutoScrollEnabled]);

  // 在加载状态变化时处理滚动
  useEffect(() => {
    // 清理之前的任何定时器
    if (scrollInterval.current) {
      clearInterval(scrollInterval.current);
      scrollInterval.current = null;
    }

    // 如果正在加载且自动滚动已启用，创建一个定时器以确保平滑滚动
    if (loading && isAutoScrollEnabled) {
      scrollInterval.current = setInterval(scrollToBottom, 100);

      // 立即滚动一次
      scrollToBottom();
    }

    return () => {
      if (scrollInterval.current) {
        clearInterval(scrollInterval.current);
        scrollInterval.current = null;
      }
    };
  }, [loading, isAutoScrollEnabled]);

  return (
    <div className="ai-chat-content" ref={contentRef}>
      <div className="messages-container" style={{ width: '100%' }}>
        {/* 显示上下文信息横幅 */}
        <ContextBanner contextData={contextData} uiContext={uiContext} />

        {messages.length > 0 ? (
          messageGroups.map((group, groupIndex) => (
            <div key={`group-${groupIndex}`} className="message-group">
              {group.map((message, index) => (
                <Message
                  key={`message-${groupIndex}-${index}`}
                  message={message}
                  index={messages.indexOf(message)}
                  onViewCode={onViewCode}
                  onExecuteCode={onExecuteCode}
                  onCopyMessage={onCopyMessage}
                  onRegenerate={onRegenerate}
                  onEditMessage={onEditMessage}
                  contextData={contextData}
                  uiContext={uiContext}
                  onClarificationComplete={onClarificationComplete}
                  setConversations={setConversations}
                  currentTab={currentTab}
                />
              ))}
            </div>
          ))
        ) : (
          <EmptyChat currentTab={currentTab} contextData={contextData} uiContext={uiContext} />
        )}
      </div>

      {loading && (
        <div style={{ textAlign: 'center', padding: '16px', width: '100%' }}>
          <Spin tip="AI正在思考..." />
        </div>
      )}

      {isScrolled && (
        <div
          className="scroll-to-bottom"
          onClick={() => {
            setIsAutoScrollEnabled(true);
            setIsScrolled(false);
            scrollToBottom();
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4V20M12 20L6 14M12 20L18 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      )}
    </div>
  );
};

export default ChatContent;
