import React from 'react';
import { Typography } from 'antd';
import { getMenuTitle } from '../utils';

const { Title, Text } = Typography;

/**
 * 聊天头部组件
 * @param {Object} props - 组件属性
 * @param {string} props.currentTab - 当前标签
 */
const ChatHeader = ({
  currentTab
}) => {
  return (
    <div className="ai-chat-header">
      <Title level={4} style={{ margin: 0 }}>{getMenuTitle(currentTab)}</Title>
      <Text type="secondary">
        向AI助手询问如何生成
        {currentTab === 'model' ? '数据模型' :
         currentTab === 'metadata' ? '元数据' :
         currentTab === 'ui' ? '界面设计' : '相关内容'}
      </Text>
    </div>
  );
};

export default ChatHeader;
