import React, { useRef, useState } from 'react';
import { Button, Input, Tooltip, Tag } from 'antd';
import { SendOutlined, DeleteOutlined, PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { Icon } from 'ming-ui';
import MentionList from './MentionList';
import { getMenuTitle } from '../utils';
import ModelSelector from './ModelSelector';
import { AI_MODELS } from '../constants';

const { TextArea } = Input;

/**
 * 聊天输入组件
 * @param {Object} props - 组件属性
 * @param {string} props.value - 输入值
 * @param {Function} props.onChange - 输入变化回调
 * @param {Function} props.onSend - 发送消息回调
 * @param {Function} props.onKeyPress - 按键事件回调
 * @param {boolean} props.loading - 加载状态
 * @param {string} props.currentTab - 当前标签
 * @param {Function} props.onClear - 清空聊天回调
 * @param {Function} props.onNewChat - 新建聊天回调
 * @param {Function} props.onOpenSettings - 打开设置回调
 * @param {string} props.modelValue - 当前选择的模型
 * @param {Function} props.onModelChange - 模型变化回调
 * @param {boolean} props.hasPendingClarification - 是否有待回答的澄清问题
 */
const ChatInput = ({
  value,
  onChange,
  onSend,
  onKeyPress,
  loading,
  currentTab,
  onClear,
  onNewChat,
  onOpenSettings,
  modelValue,
  onModelChange,
  hasPendingClarification = false
}) => {
  const [showMentions, setShowMentions] = useState(false);
  const inputRef = useRef(null);

  // 计算是否应该禁用输入
  const isInputDisabled = loading || hasPendingClarification;

  // 插入@提及内容
  const insertMention = (mention) => {
    const cursorPos = inputRef.current.resizableTextArea.textArea.selectionStart;
    const textBeforeCursor = value.substring(0, cursorPos);
    const textAfterCursor = value.substring(cursorPos);

    // 检查光标前是否已经有@符号
    const lastAtPos = textBeforeCursor.lastIndexOf('@');
    if (lastAtPos >= 0 && !textBeforeCursor.substring(lastAtPos).includes(' ')) {
      // 替换现有的@
      const newText = textBeforeCursor.substring(0, lastAtPos) +
                     `@${mention.id} ` +
                     textAfterCursor;
      onChange({ target: { value: newText } });
    } else {
      // 直接在光标位置插入
      const newText = textBeforeCursor + `@${mention.id} ` + textAfterCursor;
      onChange({ target: { value: newText } });
    }

    setShowMentions(false);

    // 重新聚焦输入框
    setTimeout(() => {
      inputRef.current.focus();
    }, 50);
  };

  // 处理输入变化，检测@符号
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(e);

    // 检测是否需要显示@菜单
    const cursorPos = e.target.selectionStart;
    const textBeforeCursor = newValue.substring(0, cursorPos);
    const lastAtPos = textBeforeCursor.lastIndexOf('@');

    if (lastAtPos >= 0 && !textBeforeCursor.substring(lastAtPos).includes(' ')) {
      setShowMentions(true);
    } else {
      setShowMentions(false);
    }
  };

  // 点击@符号按钮
  const handleAddMention = () => {
    const newValue = value + '@';
    onChange({ target: { value: newValue } });
    setShowMentions(true);

    setTimeout(() => {
      inputRef.current.focus();
    }, 50);
  };

  // 获取当前选择的模型名称
  const getCurrentModelName = () => {
    const model = AI_MODELS.find(m => m.value === modelValue);
    return model ? model.label : '模型';
  };

  return (
    <div className="ai-chat-input">
      <div className="input-wrapper">
        {showMentions && (
          <MentionList onSelect={insertMention} />
        )}

        <TextArea
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onKeyPress={onKeyPress}
          placeholder={
            hasPendingClarification
              ? "请先回答上面的澄清问题，或点击'新对话'开始新的对话..."
              : `询问关于${getMenuTitle(currentTab)}的问题，或输入@引用内容...`
          }
          autoSize={{ minRows: 2, maxRows: 4 }}
          disabled={isInputDisabled}
        />

        <div className="input-tools">
          <div className="left-tools">
            <div className="model-selector-area">
              <span className="model-label">模型:</span>
              <ModelSelector
                value={modelValue}
                onChange={onModelChange}
              />
            </div>
            <Tooltip title="添加引用 (@)">
              <span
                className="tool-button"
                onClick={handleAddMention}
              >
                <Icon icon="at" />
              </span>
            </Tooltip>
            <Tooltip title="上传文件">
              <span className="tool-button">
                <Icon icon="file-upload" />
              </span>
            </Tooltip>
          </div>

          <div className="right-tools">
            {/* <Tooltip title="清空对话">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={onClear}
                style={{ marginRight: 8 }}
              />
            </Tooltip> */}

            <Tooltip title="新对话">
              <Button
                type="text"
                icon={<PlusOutlined />}
                onClick={onNewChat}
                style={{ marginRight: 8 }}
              />
            </Tooltip>

            <Tooltip title="设置">
              <Button
                type="text"
                icon={<SettingOutlined />}
                onClick={onOpenSettings}
                style={{ marginRight: 8 }}
              />
            </Tooltip>
            <Button
              type="primary"
              shape="round"
              icon={<SendOutlined />}
              onClick={onSend}
              disabled={!value.trim() || isInputDisabled}
            >
              发送
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
