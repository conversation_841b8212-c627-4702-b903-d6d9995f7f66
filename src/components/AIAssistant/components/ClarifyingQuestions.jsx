import React, { useState } from 'react';
import '../styles/ClarifyingQuestions.css';

const ClarifyingQuestions = ({ clarifyingData, onSubmit, onCancel, loading = false }) => {
  const { executionId, qaLoopId, questions } = clarifyingData;

  const [answers, setAnswers] = useState(
    questions.reduce((acc, q) => ({
      ...acc,
      [q.questionId]: ''
    }), {})
  );
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 合并内部和外部的loading状态
  const actuallySubmitting = isSubmitting || loading;

  const handleAnswerChange = (questionId, answer) => {
    if (isSubmitted || actuallySubmitting) return; // 已提交或正在提交时不允许修改

    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmit = async () => {
    if (isSubmitted || actuallySubmitting) return;

    setIsSubmitting(true);
    try {
      const qnData = questions.map(q => ({
        questionId: q.questionId,
        answer: answers[q.questionId] || ''
      }));

      const submitData = {
        executionId, qaLoopId, answers: qnData
      }

      await onSubmit(submitData);
      setIsSubmitted(true); // 标记为已提交
    } catch (error) {
      // 提交失败，允许重试
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (isSubmitted || actuallySubmitting) return; // 已提交或正在提交时不允许取消
    onCancel();
  };

  const isAllAnswered = questions.every(q => answers[q.questionId] && answers[q.questionId].trim());

  return (
    <div className="clarifying-questions-container">
      <div className="clarifying-header">
        <h3>🤔 需要您的澄清</h3>
        <p>
          {isSubmitted
            ? "感谢您的回答！答案已成功提交。"
            : actuallySubmitting
            ? "正在提交您的回答，请稍候..."
            : "为了更好地为您服务，请回答以下问题："
          }
        </p>
      </div>

      <div className="questions-list">
        {questions.map((question, index) => (
          <div key={question.questionId} className={`question-card ${isSubmitted ? 'submitted' : ''} ${actuallySubmitting ? 'submitting' : ''}`}>
            <div className="question-header">
              <span className="question-number">问题 {question.sequence}</span>
              {isSubmitted && <span className="submitted-badge">✓ 已提交</span>}
              {actuallySubmitting && !isSubmitted && <span className="submitting-badge">⏳ 提交中</span>}
            </div>

            <div className="question-content">
              <h4 className="question-text">{question.question}</h4>

              {question.rationale && (
                <div className="question-rationale">
                  <span className="rationale-label">💡 说明：</span>
                  <p>{question.rationale}</p>
                </div>
              )}

              {question.potentialRecommendations && question.potentialRecommendations.length > 0 && (
                <div className="recommendations">
                  <span className="recommendations-label">💭 参考建议：</span>
                  <ul>
                    {question.potentialRecommendations.map((rec, idx) => (
                      <li key={idx}>{rec}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="answer-section">
                <label htmlFor={`answer-${question.questionId}`}>您的回答：</label>
                <textarea
                  id={`answer-${question.questionId}`}
                  value={answers[question.questionId] || ''}
                  onChange={(e) => handleAnswerChange(question.questionId, e.target.value)}
                  placeholder={isSubmitted ? "已提交的回答" : actuallySubmitting ? "正在提交..." : "请在此输入您的回答..."}
                  rows={4}
                  className="answer-textarea"
                  disabled={isSubmitted || actuallySubmitting}
                  readOnly={isSubmitted || actuallySubmitting}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {!isSubmitted && (
        <div className="actions">
          <button
            className="btn-cancel"
            onClick={handleCancel}
            disabled={actuallySubmitting}
          >
            取消
          </button>
          <button
            className="btn-submit"
            onClick={handleSubmit}
            disabled={!isAllAnswered || actuallySubmitting}
          >
            {actuallySubmitting ? '提交中...' : `提交回答 (${questions.filter(q => answers[q.questionId] && answers[q.questionId].trim()).length}/${questions.length})`}
          </button>
        </div>
      )}

      {isSubmitted && (
        <div className="submitted-message">
          <p>✅ 您的回答已成功提交！AI助手将根据您的回答提供更准确的帮助。</p>
        </div>
      )}
    </div>
  );
};

export default ClarifyingQuestions;
