import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Tooltip, message } from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  FormatPainterOutlined,
} from '@ant-design/icons';
import { CodeEditorWrapper } from '../styles';
import { useCodeEditor } from '../hooks';
import CodeMirror from 'codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/idea.css' // 主题
import 'codemirror/mode/javascript/javascript';
import 'codemirror/mode/jsx/jsx';
import 'codemirror/mode/css/css';
import 'codemirror/mode/htmlmixed/htmlmixed';
import 'codemirror/mode/markdown/markdown';
import 'codemirror/mode/xml/xml';
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/edit/matchbrackets';
import 'codemirror/addon/selection/active-line';
import 'codemirror/addon/comment/comment';
import 'codemirror/addon/fold/foldcode';
import 'codemirror/addon/fold/foldgutter';
import 'codemirror/addon/fold/brace-fold';
import 'codemirror/addon/fold/comment-fold';
import 'codemirror/addon/fold/indent-fold';
import 'codemirror/addon/fold/foldgutter.css';
import xmlFormat from 'xml-formatter';


/**
 * 代码编辑器组件
 * @param {Object} props - 组件属性
 * @param {Object} props.codeBlock - 代码块数据
 * @param {Function} props.onClose - 关闭编辑器回调
 * @param {Function} props.onExecute - 执行代码回调
 * @param {Function} props.onSave - 保存代码回调
 */
const CodeEditor = ({ codeBlock, onClose, onExecute, onSave }) => {
  const editorRef = useRef(null);
  const codeMirrorRef = useRef(null);

  // 预处理代码块，如果是XML类型则格式化初始值
  const formattedCodeBlock = React.useMemo(() => {
    if (codeBlock && (codeBlock.language === 'xml' || codeBlock.language === 'svg') && codeBlock.code) {
      try {
        return {
          ...codeBlock,
          code: xmlFormat(codeBlock.code)
        };
      } catch (error) {
        console.error('XML格式化失败:', error);
        return codeBlock;
      }
    }
    return codeBlock;
  }, [codeBlock]);

  const {
    editorValue,
    isEdited,
    handleEditorChange,
    saveCode,
    executeCode,
    resetEditor
  } = useCodeEditor(formattedCodeBlock);

  // 初始化 CodeMirror
  useEffect(() => {
    if (editorRef.current && !codeMirrorRef.current) {
      const editor = CodeMirror(editorRef.current, {
        value: editorValue,
        mode: getCodeMirrorMode(codeBlock && codeBlock.language || 'javascript'),
        theme: 'idea',
        lineNumbers: true,
        lineWrapping: true,
        tabSize: 2,
        indentWithTabs: false,
        autoCloseBrackets: true,
        matchBrackets: true,
        styleActiveLine: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        extraKeys: {
          'Ctrl-Space': 'autocomplete',
          'Cmd-/': 'toggleComment',
          'Ctrl-/': 'toggleComment',
          'Tab': (cm) => {
            if (cm.somethingSelected()) {
              cm.indentSelection('add');
            } else {
              cm.replaceSelection('  ', 'end');
            }
          }
        }
      });

      editor.on('change', (cm) => {
        handleEditorChange(cm.getValue());
      });

      codeMirrorRef.current = editor;
    }

    return () => {
      if (codeMirrorRef.current) {
        codeMirrorRef.current = null;
      }
    };
  }, [codeBlock]);

  // 监听值变化，更新编辑器
  useEffect(() => {
    if (codeMirrorRef.current && editorValue !== codeMirrorRef.current.getValue()) {
      codeMirrorRef.current.setValue(editorValue);
    }
  }, [editorValue]);

  // 获取 CodeMirror 模式
  const getCodeMirrorMode = (language) => {
    const modeMap = {
      javascript: 'javascript',
      jsx: 'jsx',
      typescript: 'text/typescript',
      tsx: 'text/typescript-jsx',
      html: 'htmlmixed',
      css: 'css',
      json: { name: 'javascript', json: true },
      markdown: 'markdown',
      xml: 'xml',
      svg: 'xml',
    };

    return modeMap[language && language.toLowerCase()] || 'javascript';
  };

  const formatCode = () => {
    if (!codeMirrorRef.current) return;

    try {
      const code = codeMirrorRef.current.getValue();
      // 确保code是字符串类型
      if (typeof code !== 'string') {
        message.error('无法格式化：代码内容不是有效的字符串');
        return;
      }

      if (code.trim() === '') {
        message.warning('代码为空，无需格式化');
        return;
      }

      const language = codeBlock && codeBlock.language && codeBlock.language.toLowerCase() || 'javascript';      console.log('正在格式化代码，语言:', language);

      // 根据不同语言选择不同的解析器
      const formattedCode = xmlFormat(code);

      codeMirrorRef.current.setValue(formattedCode);
      handleEditorChange(formattedCode);
      message.success(`代码格式化成功 (${language})`);

    } catch (error) {
      console.error('格式化代码失败:', error);
      message.error(error);
    }
  };

  // 处理保存
  const handleSave = () => {
    const updatedBlock = saveCode();
    if (onSave && updatedBlock) {
      onSave(updatedBlock);
    }
  };

  return (
    <CodeEditorWrapper>
      <div
        ref={editorRef}
        className="code-editor"
      />

      <div className="editor-footer">
        <div className="status">
          <span>{codeBlock && codeBlock.language} | {editorValue.split('\n').length} 行</span>
          {isEdited && <span style={{ marginLeft: 12, color: '#faad14' }}>(已修改)</span>}
        </div>

        <div className="editor-actions">
          <Tooltip title="格式化代码">
            <Button
              icon={<FormatPainterOutlined />}
              size="small"
              onClick={formatCode}
              style={{ marginRight: 8 }}
            />
          </Tooltip>

          {isEdited && (
            <Tooltip title="撤销修改">
              <Button
                icon={<UndoOutlined />}
                size="small"
                onClick={resetEditor}
                style={{ marginRight: 8 }}
                danger
              />
            </Tooltip>
          )}

          <Tooltip title="保存修改">
            <Button
              icon={<SaveOutlined />}
              size="small"
              type={isEdited ? "primary" : "default"}
              onClick={handleSave}
              disabled={!isEdited}
            />
          </Tooltip>
        </div>
      </div>
    </CodeEditorWrapper>
  );
};

export default CodeEditor;
