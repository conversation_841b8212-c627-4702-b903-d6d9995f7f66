import React from 'react';
import { Avatar, Typography, Tag, Space, Button } from 'antd';
import { RobotOutlined } from '@ant-design/icons';
import { getMenuTitle } from '../utils';
import { useAIChat } from '../hooks';

const { Title, Paragraph } = Typography;

/**
 * 空聊天状态组件
 * @param {Object} props - 组件属性
 * @param {string} props.currentTab - 当前标签
 * @param {Object} props.contextData - 后端参数上下文数据
 * @param {Object} props.uiContext - UI上下文数据
 */
const EmptyChat = ({ currentTab, contextData = {}, uiContext = {} }) => {
  // 使用AI聊天钩子获取发送消息的功能
  const { handleInputChange, sendMessage } = useAIChat();

  const hasContext = (contextData && Object.keys(contextData).length > 0) ||
                     (uiContext && Object.keys(uiContext).length > 0);
  const modelName = uiContext.modelName || '';

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      padding: '40px 20px',
      textAlign: 'center'
    }}>
      <Avatar style={{ backgroundColor: '#e6f7ff', color: '#1890ff', marginBottom: '24px' }} size={64}>
        <RobotOutlined style={{ fontSize: '32px' }} />
      </Avatar>

      <Title level={4}>
        {hasContext
          ? `开始向AI助手询问关于${modelName ? `"${modelName}"` : ''}的问题吧`
          : `开始向AI助手询问关于${getMenuTitle(currentTab)}的问题吧`}
      </Title>

      <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: '8px', marginTop: '24px' }}>
        <Tag color="blue">智能推荐</Tag>
        <Tag color="blue">最佳实践</Tag>
        <Tag color="blue">代码生成</Tag>
        <Tag color="blue">性能优化</Tag>
        <Tag color="blue">上下文感知</Tag>
      </div>
    </div>
  );
};

export default EmptyChat;
