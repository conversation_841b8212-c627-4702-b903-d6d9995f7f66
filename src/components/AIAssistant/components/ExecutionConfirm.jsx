import React from 'react';
import { Button, Typography } from 'antd';
import { CodeOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { ExecutionPanel } from '../styles';

const { Text, Paragraph } = Typography;

/**
 * 代码执行确认组件
 * @param {Object} props - 组件属性
 * @param {Object} props.codeBlock - 代码块数据
 * @param {Function} props.onExecute - 确认执行回调
 * @param {Function} props.onCancel - 取消执行回调
 * @param {Function} props.onView - 查看代码回调
 */
const ExecutionConfirm = ({ codeBlock, onExecute, onCancel, onView }) => {
  if (!codeBlock) return null;

  return (
    <ExecutionPanel>
      <div className="execution-header">
        <div className="title">
          <CodeOutlined className="icon" />
          <span>确认执行 {codeBlock.fileName}</span>
        </div>
      </div>

      <div className="execution-content">
        <Paragraph>
          <Text strong>语言:</Text> {codeBlock.language}
        </Paragraph>

        <Paragraph>
          <Text strong>文件名:</Text> {codeBlock.fileName}
        </Paragraph>

        <Paragraph>
          <Text type="warning">
            执行代码可能会对当前环境产生影响，请确认是否继续？
          </Text>
        </Paragraph>

        <div className="action-buttons">
          <Button
            icon={<CloseOutlined />}
            onClick={onCancel}
          >
            取消
          </Button>

          <Button
            icon={<CodeOutlined />}
            onClick={() => onView && onView(codeBlock)}
            style={{ marginLeft: 8 }}
          >
            查看代码
          </Button>

          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={() => onExecute && onExecute(codeBlock)}
            style={{ marginLeft: 8 }}
          >
            确认执行
          </Button>
        </div>
      </div>
    </ExecutionPanel>
  );
};

export default ExecutionConfirm;
