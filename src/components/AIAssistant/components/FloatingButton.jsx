import React from 'react';
import { Button, Tooltip } from 'antd';
import { Icon } from 'ming-ui';
import { AIFloatingButton } from '../styles';

/**
 * 悬浮按钮组件
 * @param {Object} props - 组件属性
 * @param {Function} props.onClick - 点击回调
 * @param {string} [props.targetKey] - 要直接定位的菜单key
 * @param {Object} [props.contextData] - 传递给AI助手的上下文数据
 * @param {Object} [props.uiContext] - UI相关的上下文数据，不会传递给后端
 * @param {string} [props.tooltipTitle] - 按钮提示文本, 默认为 "众织 Copilot"
 * @param {ReactNode} [props.icon] - 自定义图标
 * @param {Object} [props.buttonProps] - 额外的按钮属性
 */
const FloatingButton = ({
  onClick,
  targetKey,
  contextData = {},
  uiContext = {},
  tooltipTitle = "众织 Copilot",
  icon = <Icon icon="ai" className="Font24" />,
  buttonProps = {}
}) => {
  const handleClick = () => {
    onClick(targetKey, contextData, uiContext);
  };

  return (
    <AIFloatingButton>
      <Tooltip title={tooltipTitle}>
        <Button
          type="primary"
          className="ai-button"
          onClick={handleClick}
          {...buttonProps}
        >
          {icon}
        </Button>
      </Tooltip>
    </AIFloatingButton>
  );
};

export default FloatingButton;
