import React,{ useState, useEffect } from 'react';
import { Menu } from 'antd';
import {
  AppstoreOutlined,
  DatabaseOutlined,
  LayoutOutlined,
} from '@ant-design/icons';

/**
 * 功能菜单组件
 * @param {Object} props - 组件属性
 * @param {Array} props.menuData - 菜单数据
 * @param {string} props.currentTab - 当前标签
 * @param {string} props.activeTargetKey - 活动目标键
 * @param {Function} props.onClick - 菜单点击回调
 * @param {boolean} props.isMobile - 是否移动设备
 */
const FunctionMenu = ({
  currentTab,
  activeTargetKey,
  onClick,
  isMobile,
}) => {

  const [menuData, setMenuData] = useState(null);

  useEffect(() => {
    // 这里应该调用后端API获取菜单数据
    // 暂时使用模拟数据
    const mockMenuData = [
      {
        key: 'model',
        icon: <AppstoreOutlined />,
        title: 'AI 生成模型',
        children: [
          {
            key: 'model.domain',
            title: '业务域',
            children: [
              {
                key: 'model.domain.a',
                title: '业务域子类A'
              },
              {
                key: 'model.domain.b',
                title: '业务域子类B'
              }
            ]
          },
          {
            key: 'model.entities',
            title: '实体'
          }
        ]
      },
      {
        key: 'metadata',
        icon: <DatabaseOutlined />,
        title: 'AI 生成元数据',
        children: [
          {
            key: 'metadata.fields',
            title: '字段'
          },
          {
            key: 'metadata.relations',
            title: '关系'
          }
        ]
      },
      {
        key: 'ui',
        icon: <LayoutOutlined />,
        title: 'AI 生成界面',
        children: [
          {
            key: 'ui.forms',
            title: '表单'
          },
          {
            key: 'ui.pages',
            title: '页面'
          }
        ]
      }
    ];

    setMenuData(mockMenuData);
  }, []);

  // 渲染菜单项
  const renderMenuItems = (menuItems) => {
    if (!menuItems) return null;

    // 递归渲染菜单及其子菜单
    const renderMenuItem = (item) => {
      // 如果有子菜单
      if (item.children && item.children.length > 0) {
        // 如果有activeTargetKey，只显示激活的一级菜单
        if (activeTargetKey) {
          // 如果是当前激活的一级菜单，显示该菜单及其所有子菜单
          if (item.key === activeTargetKey) {
            return (
              <Menu.SubMenu
                key={item.key}
                icon={item.icon}
                title={item.title}
              >
                {item.children.map(child => renderMenuItem(child))}
              </Menu.SubMenu>
            );
          }
          // 否则不显示
          return null;
        }
        // 没有activeTargetKey，显示所有菜单
        return (
          <Menu.SubMenu
            key={item.key}
            icon={item.icon}
            title={item.title}
          >
            {item.children.map(child => renderMenuItem(child))}
          </Menu.SubMenu>
        );
      }
      // 没有子菜单，渲染普通菜单项
      else {
        // 如果有activeTargetKey，且不是一级菜单，始终显示
        if (activeTargetKey && item.key.includes('.')) {
          return (
            <Menu.Item key={item.key} icon={item.icon}>
              {item.title}
            </Menu.Item>
          );
        }
        // 如果有activeTargetKey，且是一级菜单，只显示激活的菜单
        else if (activeTargetKey) {
          if (item.key === activeTargetKey) {
            return (
              <Menu.Item key={item.key} icon={item.icon}>
                {item.title}
              </Menu.Item>
            );
          }
          return null;
        }
        // 没有activeTargetKey，显示所有菜单
        return (
          <Menu.Item key={item.key} icon={item.icon}>
            {item.title}
          </Menu.Item>
        );
      }
    };

    return menuItems.map(item => renderMenuItem(item));
  };

  return (
    <div className="ai-menu">
      <div className="ai-menu-header">
        <div className="menu-title">功能菜单</div>
      </div>

      <Menu
        mode={isMobile ? "horizontal" : "inline"}
        selectedKeys={[currentTab]}
        onClick={onClick}
        style={{ height: isMobile ? 'auto' : 'calc(100% - 70px)' }}
        defaultOpenKeys={activeTargetKey ? [activeTargetKey] : []}
      >
        {renderMenuItems(menuData)}
      </Menu>
    </div>
  );
};

export default FunctionMenu;
