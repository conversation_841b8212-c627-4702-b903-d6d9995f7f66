import React from 'react';
import { MentionDropdown } from '../styles';
import { MENTIONS } from '../constants';

/**
 * 可@引用的内容列表组件
 * @param {Object} props - 组件属性
 * @param {Function} props.onSelect - 选择项回调
 */
const MentionList = ({ onSelect }) => {
  return (
    <MentionDropdown>
      <div className="mention-header">选择要引用的内容</div>
      {MENTIONS.map((mention) => (
        <div
          key={mention.id}
          className="mention-item"
          onClick={() => onSelect && onSelect(mention)}
        >
          <div className="mention-icon">{mention.icon}</div>
          <div>
            <div className="mention-name">{mention.name}</div>
            <div className="mention-desc">{mention.desc}</div>
          </div>
        </div>
      ))}
    </MentionDropdown>
  );
};

export default MentionList;
