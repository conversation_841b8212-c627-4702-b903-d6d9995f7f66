import React, { useState, useEffect, useRef } from 'react';
import { Avatar, Button, message as antdMessage } from 'antd';
import { UserOutlined, CopyOutlined, HistoryOutlined, CodeOutlined, CheckOutlined } from '@ant-design/icons';
import { Icon } from 'ming-ui';
import { MessageItem } from '../styles';
import { formatTime } from '../utils';
import codeBlockManager from '../utils/codeBlockManager';
import ClarifyingQuestions from '../components/ClarifyingQuestions';
import chatApi from '../service';
import StreamMessageHandler from '../utils/streamMessageHandler';

/**
 * 单条消息组件
 * @param {Object} props - 组件属性
 * @param {Object} props.message - 消息对象
 * @param {number} props.index - 消息索引
 * @param {Function} props.onViewCode - 查看代码回调
 * @param {Function} props.onExecuteCode - 执行代码回调
 * @param {Function} props.onCopyMessage - 复制消息回调
 * @param {Function} props.onRegenerate - 重新生成回调
 * @param {Function} props.onEditMessage - 编辑消息回调（可选）
 * @param {Object} props.contextData - 后端上下文数据（可选）
 * @param {Object} props.uiContext - UI上下文数据（可选）
 * @param {Function} props.onClarificationComplete - 澄清问题完成回调（可选）
 * @param {Function} props.setConversations - 设置对话状态的函数（用于流式更新）
 * @param {string} props.currentTab - 当前标签页
 */
const Message = ({
  message,
  index,
  onViewCode,
  onExecuteCode,
  onCopyMessage,
  onRegenerate,
  onEditMessage,
  contextData = {},
  uiContext = {},
  onClarificationComplete,
  setConversations,
  currentTab
}) => {
  const isUser = message.role === 'user';
  const [codeBlocks, setCodeBlocks] = useState([]);
  const [processedContent, setProcessedContent] = useState('');
  const messageRef = useRef(null);
  const [messageHeight, setMessageHeight] = useState(null);
  const [submittingAnswers, setSubmittingAnswers] = useState(false);
  const abortControllerRef = useRef(null);

  // 当消息内容更新时，提取代码块并处理内容
  useEffect(() => {
    if (message.content) {
      // 如果正在加载，记录当前高度以防止抖动
      if (message.isLoading && messageRef.current && !messageHeight) {
        const height = messageRef.current.offsetHeight;
        if (height > 0) {
          setMessageHeight(height);
        }
      }

      // 提取代码块
      const extractedBlocks = codeBlockManager.extractCodeBlocks(message.content);
      setCodeBlocks(extractedBlocks);

      // 处理消息内容
      setProcessedContent(message.content);
    } else {
      setProcessedContent('');
      setCodeBlocks([]);
    }

    // 当消息加载完成时，重置高度
    if (!message.isLoading) {
      setMessageHeight(null);
    }
  }, [message.content, message.isLoading]);

  // 高亮代码块
  useEffect(() => {
    if (!message.isLoading && codeBlocks.length > 0) {
      codeBlockManager.highlightCodeBlocks();
    }
  }, [codeBlocks, message.isLoading]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        console.log("Message组件卸载，中止澄清请求");
      }
    };
  }, []);

  // 处理澄清问题提交 - 流式版本
  const handleClarificationSubmit = async (submitData) => {
    // 使用流式提交
    setSubmittingAnswers(true);

    try {
      await StreamMessageHandler.handleClarificationSubmission({
        clarificationData: submitData,
        setConversations,
        currentTab,
        setLoading: setSubmittingAnswers,
        setCurrentMessageId: () => {}, // Message组件不需要跟踪当前消息ID
        abortControllerRef,
        apiCall: chatApi.submitClarificationAnswers,
        onSuccess: () => {
          console.log("澄清答案提交成功");
          antdMessage.success('澄清答案已提交成功！');
          if (onClarificationComplete) {
            onClarificationComplete(message.id);
          }
        },
        onError: (error) => {
          console.error('提交澄清答案失败:', error);
          antdMessage.error(`提交澄清答案失败: ${error.message}`);
        }
      });
    } catch (error) {
      console.error('启动澄清答案提交失败:', error);
      antdMessage.error('启动澄清答案提交失败，请重试');
    } finally {
      setSubmittingAnswers(false);
    }
  };

  // 处理澄清问题取消
  const handleClarificationCancel = () => {
    // 如果有正在进行的请求，取消它
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      console.log("用户取消澄清问题，中止请求");
    }
    antdMessage.info('已取消澄清问题回答');
  };

  // 渲染代码块按钮或澄清问题
  const renderCodeBlockButton = (block) => {
    // 特殊处理澄清问题
    if (block.type === 'clarifying' && block.clarifyingData) {
      return (
        <div key={`clarifying-${block.index}`} className="clarifying-questions-wrapper">
          <ClarifyingQuestions
            clarifyingData={block.clarifyingData}
            onSubmit={handleClarificationSubmit}
            onCancel={handleClarificationCancel}
            loading={submittingAnswers}
          />
        </div>
      );
    }

    // 普通代码块
    return (
      <div key={`code-button-${block.index}`} className="footer-actions">
        <Button
          type="primary"
          icon={<CodeOutlined />}
          onClick={() => onViewCode && onViewCode(block)}
          size="middle"
          style={{ marginRight: '8px', fontWeight: '500' }}
        >
          查看代码
        </Button>

        {!isUser && (
          <Button
            type="default"
            icon={<CheckOutlined />}
            onClick={() => onExecuteCode && onExecuteCode(block)}
          >
            确认执行
          </Button>
        )}
      </div>
    );
  };

  // 渲染消息内容
  const renderMessageContent = () => {
    // 如果正在加载，使用简单的预渲染
    if (message.isLoading) {
      const style = messageHeight ? { minHeight: `${messageHeight}px` } : {};

      return (
        <div
          className="streaming-content"
          style={style}
          dangerouslySetInnerHTML={{ __html: processedContent }}
        />
      );
    }

    // 如果没有代码块，直接显示内容
    if (codeBlocks.length === 0) {
      return (
        <div className="paragraphs-container">
          <div
            className="message-paragraph"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />
        </div>
      );
    }

    // 将消息内容分割为段落并将代码块替换为按钮或澄清问题组件
    const segments = [];
    let lastIndex = 0;

    // 按索引顺序处理每个代码块
    const sortedBlocks = [...codeBlocks].sort((a, b) => a.index - b.index);

    for (const block of sortedBlocks) {
      // 添加代码块之前的文本
      if (block.index > lastIndex) {
        const textBefore = message.content.substring(lastIndex, block.index);
        if (textBefore.trim()) {
          segments.push(
            <div
              key={`text-${lastIndex}`}
              className="message-paragraph"
              dangerouslySetInnerHTML={{ __html: textBefore }}
            />
          );
        }
      }

      // 添加代码块按钮或澄清问题组件
      segments.push(renderCodeBlockButton(block));

      // 更新处理位置
      lastIndex = block.index + block.raw.length;
    }

    // 添加最后部分的文本
    if (lastIndex < message.content.length) {
      const textAfter = message.content.substring(lastIndex);
      if (textAfter.trim()) {
        segments.push(
          <div
            key={`text-end`}
            className="message-paragraph"
            dangerouslySetInnerHTML={{ __html: textAfter }}
          />
        );
      }
    }

    return <div className="paragraphs-container">{segments}</div>;
  };

  return (
    <MessageItem data-role={isUser ? 'user' : 'ai'}>
      <div className="message-header">
        <Avatar
          className="avatar"
          icon={isUser ? <UserOutlined /> : undefined}
          style={isUser ? { backgroundColor: '#1890ff' } : { backgroundColor: '#d6e580ef' }}
        >
          {!isUser && <Icon icon="ai" className="Font16" style={{ color: '#fff' }} />}
        </Avatar>
        <span className="name">{isUser ? '我' : 'AI 助手'}</span>
        {message.time && <span className="time">{formatTime(message.time)}</span>}
      </div>

      <div
        ref={messageRef}
        className={`message-content ${isUser ? 'user-message' : ''}`}
      >
        {renderMessageContent()}
      </div>

      {/* 消息操作按钮 */}
      {!isUser && (
        <div className="message-actions">
          <button
            className="action-button"
            onClick={() => onCopyMessage && onCopyMessage(message)}
          >
            <CopyOutlined /> 复制
          </button>
          <button
            className="action-button"
            onClick={() => onRegenerate && onRegenerate(index)}
          >
            <HistoryOutlined /> 重新生成
          </button>
        </div>
      )}
    </MessageItem>
  );
};

export default Message;
