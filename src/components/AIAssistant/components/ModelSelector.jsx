import React from 'react';
import { Select } from 'antd';
import { AI_MODELS } from '../constants';

const { Option } = Select;

/**
 * AI模型选择器组件
 * @param {Object} props - 组件属性
 * @param {string} props.value - 当前选择的模型ID
 * @param {Function} props.onChange - 模型变更回调
 */
const ModelSelector = ({ value, onChange }) => {
  return (
    <Select
      className="menu-select"
      value={value}
      onChange={onChange}
      optionLabelProp="label"
    >
      {AI_MODELS.map(model => (
        <Option key={model.value} value={model.value} label={model.label} disabled={model.disabled}>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <span>{model.label}</span>
            <span style={{ fontSize: '12px', color: '#8c8c8c' }}>{model.desc}</span>
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default ModelSelector;
