import React, { useEffect } from 'react';
import { SplitLayout } from '../styles';
import CodeEditor from './CodeEditor';
import ChatContent from './ChatContent';
import { Button, Tooltip, Divider } from 'antd';
import {
  ArrowLeftOutlined,
  ExpandOutlined,
  CompressOutlined,
  CodeOutlined,
  SaveOutlined,
  CopyOutlined,
  CheckOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import codeBlockManager from '../utils/codeBlockManager';

/**
 * 分割视图组件
 * @param {Object} props - 组件属性
 * @param {Object} props.codeBlock - 代码块数据
 * @param {Array} props.messages - 消息数组
 * @param {string} props.currentTab - 当前标签
 * @param {boolean} props.loading - 加载状态
 * @param {boolean} props.isFullscreen - 是否全屏
 * @param {Function} props.onViewCode - 查看代码回调
 * @param {Function} props.onExecuteCode - 执行代码回调
 * @param {Function} props.onCopyMessage - 复制消息回调
 * @param {Function} props.onRegenerate - 重新生成回调
 * @param {Function} props.onClose - 关闭编辑器回调
 * @param {Function} props.onSave - 保存代码回调
 * @param {Function} props.onToggleFullscreen - 切换全屏回调
 * @param {Object} props.contextData - 后端上下文数据（可选）
 * @param {Object} props.uiContext - UI上下文数据（可选）
 * @param {Function} props.onClarificationComplete - 澄清问题完成回调（可选）
 * @param {Function} props.setConversations - 设置对话状态的函数（用于流式更新）
 */
const SplitView = ({
  codeBlock,
  messages,
  currentTab,
  loading,
  isFullscreen,
  onViewCode,
  onExecuteCode,
  onCopyMessage,
  onRegenerate,
  onClose,
  onSave,
  onToggleFullscreen,
  contextData = {},
  uiContext = {},
  onClarificationComplete,
  setConversations
}) => {
  // 代码语法高亮
  useEffect(() => {
    if (codeBlock) {
      setTimeout(() => {
        codeBlockManager.highlightCodeBlocks();
      }, 100);
    }
  }, [codeBlock]);

  // 复制代码到剪贴板
  const handleCopyCode = () => {
    if (codeBlock && codeBlock.code) {
      navigator.clipboard.writeText(codeBlock.code)
        .then(() => {
          // 可以添加复制成功的提示
          console.log('代码复制成功');
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  // 下载代码文件
  const handleDownloadCode = () => {
    if (codeBlock && codeBlock.code) {
      const fileName = codeBlock.fileName || `code-${codeBlock.language || 'text'}.${codeBlockManager.getFileExtension(codeBlock.language || 'text')}`;
      const blob = new Blob([codeBlock.code], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <SplitLayout>
      <div className="left-panel">
        <div className="split-header">
          <div className="header-left">
            <Tooltip title="返回聊天">
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={onClose}
              />
            </Tooltip>
            <span className="code-title">
              <CodeOutlined /> {codeBlock && codeBlock.fileName || '代码查看'}
            </span>
            {codeBlock && codeBlock.language && (
              <Tooltip title={`${codeBlock.language} 文件`}>
                <div className="language-tag">
                  {codeBlock.language}
                </div>
              </Tooltip>
            )}
          </div>
          <div className="header-right">
            <Tooltip title="复制代码">
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={handleCopyCode}
              />
            </Tooltip>
            <Tooltip title="下载代码">
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={handleDownloadCode}
              />
            </Tooltip>
            {/* <Tooltip title="保存更改">
              <Button
                type="text"
                icon={<SaveOutlined />}
                onClick={() => onSave && onSave(codeBlock)}
              />
            </Tooltip> */}
            <Divider type="vertical" />
            <Tooltip title={isFullscreen ? '退出全屏' : '全屏编辑'}>
              <Button
                type="text"
                icon={isFullscreen ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={onToggleFullscreen}
              />
            </Tooltip>
          </div>
        </div>

        <div className="editor-container">
          <CodeEditor
            codeBlock={codeBlock}
            onClose={onClose}
            onExecute={onExecuteCode}
            onSave={onSave}
          />
        </div>

        <div className="footer-actions">
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={() => onExecuteCode && onExecuteCode(codeBlock)}
          >
            确认执行此代码
          </Button>
          <Button
            onClick={onClose}
          >
            返回对话
          </Button>
        </div>
      </div>

      <div className="right-panel">
        <ChatContent
          messages={messages}
          currentTab={currentTab}
          loading={loading}
          onViewCode={onViewCode}
          onExecuteCode={onExecuteCode}
          onCopyMessage={onCopyMessage}
          onRegenerate={onRegenerate}
          contextData={contextData}
          uiContext={uiContext}
          onClarificationComplete={onClarificationComplete}
          setConversations={setConversations}
        />
      </div>
    </SplitLayout>
  );
};

export default SplitView;
