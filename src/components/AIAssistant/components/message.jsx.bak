import React, { useState, useEffect, Fragment } from 'react';
import { Avatar, Button, Tooltip, Tag, Badge } from 'antd';
import { UserOutlined, CopyOutlined, HistoryOutlined, EyeOutlined, CheckOutlined, FileTextOutlined, ExpandOutlined, CompressOutlined, DownloadOutlined, EditOutlined } from '@ant-design/icons';
import { Icon } from 'ming-ui';
import { MessageItem } from '../styles';
import { formatTime } from '../utils';
import codeBlockManager from '../codeBlockManager';

/**
 * 单条消息组件
 * @param {Object} props - 组件属性
 * @param {Object} props.message - 消息对象
 * @param {number} props.index - 消息索引
 * @param {Function} props.onViewCode - 查看代码回调
 * @param {Function} props.onExecuteCode - 执行代码回调
 * @param {Function} props.onCopyMessage - 复制消息回调
 * @param {Function} props.onRegenerate - 重新生成回调
 * @param {Function} props.onEditMessage - 编辑消息回调（可选）
 */
const Message = ({
  message,
  index,
  onViewCode,
  onExecuteCode,
  onCopyMessage,
  onRegenerate,
  onEditMessage
}) => {
  const isUser = message.role === 'user';
  const [expanded, setExpanded] = useState(false);
  const [codeBlocks, setCodeBlocks] = useState([]);
  const [processedContent, setProcessedContent] = useState('');

  // 在消息内容更新时提取代码块
  useEffect(() => {
    if (message.content) {
      // 使用统一的代码块提取函数
      const extractedBlocks = codeBlockManager.extractCodeBlocks(message.content, message.isLoading);
      setCodeBlocks(extractedBlocks);
    }
  }, [message.content, message.isLoading]);

  // 处理消息内容，利用 codeBlockManager 的功能
  useEffect(() => {
    if (message.content) {
      if (message.isLoading) {
        // 正在加载时的简单处理
        setProcessedContent(message.content
          .replace(/\n/g, '<br/>')
          .replace(/  /g, '&nbsp;&nbsp;'));
        return;
      }

      // 如果没有代码块，直接用 codeBlockManager 处理
      if (codeBlocks.length === 0) {
        setProcessedContent(message.content
          .replace(/\n/g, '<br/>')
          .replace(/  /g, '&nbsp;&nbsp;'));
        return;
      }

      // 有代码块时，先从原始内容中移除代码块，仅保留文本
      let result = message.content;

      // 从后向前处理，避免索引变化
      for (let i = codeBlocks.length - 1; i >= 0; i--) {
        const block = codeBlocks[i];
        const before = result.substring(0, block.index);
        const after = result.substring(block.index + block.raw.length);

        // 处理前面部分的换行
        const processedBefore = before
          .replace(/\n/g, '<br/>')
          .replace(/  /g, '&nbsp;&nbsp;');

        // 将结果拼接回去
        result = processedBefore + `<div class="code-block-placeholder" data-index="${i}"></div>` + after;
      }

      // 处理最后一部分
      if (codeBlocks.length > 0) {
        const lastBlock = codeBlocks[codeBlocks.length - 1];
        const lastPart = result.substring(result.lastIndexOf('<div class="code-block-placeholder"') +
            `<div class="code-block-placeholder" data-index="${codeBlocks.length-1}"></div>`.length);

        const processedLastPart = lastPart
          .replace(/\n/g, '<br/>')
          .replace(/  /g, '&nbsp;&nbsp;');

        result = result.substring(0, result.lastIndexOf('<div class="code-block-placeholder"') +
          `<div class="code-block-placeholder" data-index="${codeBlocks.length-1}"></div>`.length) +
          processedLastPart;
      }

      setProcessedContent(result);
    } else {
      setProcessedContent('');
    }
  }, [message.content, message.isLoading, codeBlocks]);

  // 高亮所有代码块
  useEffect(() => {
    if (!message.isLoading && codeBlocks.length > 0) {
      codeBlockManager.highlightCodeBlocks();
    }
  }, [processedContent, message.isLoading, codeBlocks]);

  // 切换展开/收起状态
  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  // 自定义渲染函数 - 替换占位符为实际代码块
  const renderContentWithCodeBlocks = () => {
    if (!processedContent) return null;

    // 将 HTML 字符串分割为数组
    const parts = processedContent.split(/<div class="code-block-placeholder" data-index="\d+"><\/div>/);
    const placeholders = processedContent.match(/<div class="code-block-placeholder" data-index="\d+"><\/div>/g) || [];
    const result = [];

    // 逐个替换占位符
    for (let i = 0; i < parts.length; i++) {
      // 添加文本部分
      if (parts[i]) {
        result.push(
          <div key={`text-${i}`} dangerouslySetInnerHTML={{ __html: parts[i] }} />
        );
      }

      // 添加代码块
      if (i < placeholders.length) {
        const index = parseInt(placeholders[i].match(/data-index="(\d+)"/)[1], 10);
        const block = codeBlocks[index];

        if (block) {
          result.push(
            <div key={`code-${i}`} className="code-block-card">
              <div className="code-block-header">
                <div className="code-info">
                  <Tag color="blue" className="code-language">{block.language}</Tag>
                  <Tooltip title={block.fileName}>
                    <span className="code-filename">{block.fileName}</span>
                  </Tooltip>
                </div>
              </div>
              <pre><code className={`language-${block.language}`}>{block.code}</code></pre>
              <div className="code-actions">
                <Button
                  type="primary"
                  size="middle"
                  icon={<EyeOutlined />}
                  onClick={() => onViewCode && onViewCode(block)}
                  className="view-code-btn"
                >
                  查看代码
                </Button>
                {!isUser && (
                  <Fragment>
                    <Button
                      type="default"
                      size="middle"
                      icon={<CheckOutlined />}
                      onClick={() => onExecuteCode && onExecuteCode(block)}
                      style={{ marginLeft: 12 }}
                    >
                      确认执行
                    </Button>
                    <Tooltip title="下载代码">
                      <Button
                        type="text"
                        size="middle"
                        icon={<DownloadOutlined />}
                        onClick={() => window.dispatchEvent(new CustomEvent('download-code', { detail: block }))}
                        style={{ marginLeft: 8 }}
                      />
                    </Tooltip>
                  </Fragment>
                )}
              </div>
            </div>
          );
        }
      }
    }

    return result;
  };

  return (
    <MessageItem data-role={isUser ? 'user' : 'ai'}>
      <div className="message-header">
        <Avatar
          className="avatar"
          icon={isUser ? <UserOutlined /> : undefined}
          style={isUser ? { backgroundColor: '#1890ff' } : { backgroundColor: '#d6e580ef' }}
        >
          {!isUser && <Icon icon="ai" className="Font16" style={{ color: '#fff' }} />}
        </Avatar>
        <span className="name">{isUser ? '我' : 'AI 助手'}</span>
        {message.time && <span className="time">{formatTime(message.time)}</span>}
      </div>

      <div className={`message-content ${isUser ? 'user-message' : ''}`}>
        {/* 渲染消息内容 - 使用自定义渲染函数 */}
        {renderContentWithCodeBlocks()}

        {/* 用户消息的快速操作按钮 */}
        {/* {isUser && (
          <React.Fragment>
            <div className="user-message-actions edit-action">
              <Tooltip title="编辑消息">
                <div
                  className="action-icon"
                  onClick={() => onEditMessage && onEditMessage(message, index)}
                >
                  <EditOutlined />
                </div>
              </Tooltip>
            </div>
            <div className="user-message-actions copy-action">
              <Tooltip title="复制消息">
                <div
                  className="action-icon"
                  onClick={() => onCopyMessage && onCopyMessage(message)}
                >
                  <CopyOutlined />
                </div>
              </Tooltip>
            </div>
          </React.Fragment>
        )} */}

        {/* 渲染代码块 */}
        {/* {codeBlocks.length > 0 && (
          <div className={`context-dialog ${expanded ? 'expanded' : ''}`}>
            <div className="context-dialog-header">
              <FileTextOutlined className="context-icon" />
              <span className="context-title">代码块</span>
              <Badge count={codeBlocks.length} size="small" style={{ backgroundColor: '#1890ff', marginRight: '8px' }} />
              <Tooltip title={expanded ? "收起" : "展开"}>
                <Button
                  type="text"
                  size="small"
                  icon={expanded ? <CompressOutlined /> : <ExpandOutlined />}
                  onClick={toggleExpand}
                  className="expand-button"
                />
              </Tooltip>
            </div>
            <div className="context-dialog-content">
              {codeBlocks.map((block, i) => (
                <div key={i} className="code-block-card">
                  <div className="code-block-header">
                    <div className="code-info">
                      <Tag color="blue" className="code-language">{block.language}</Tag>
                      <Tooltip title={block.fileName}>
                        <span className="code-filename">{block.fileName}</span>
                      </Tooltip>
                    </div>
                  </div>
                  <pre><code className={`language-${block.language}`}>{block.code}</code></pre>
                  <div className="code-actions">
                    <Button
                      type="primary"
                      size="middle"
                      icon={<EyeOutlined />}
                      onClick={() => onViewCode && onViewCode(block)}
                      className="view-code-btn"
                    >
                      查看代码
                    </Button>
                    {!isUser && (
                      <React.Fragment>
                        <Button
                          type="default"
                          size="middle"
                          icon={<CheckOutlined />}
                          onClick={() => onExecuteCode && onExecuteCode(block)}
                          style={{ marginLeft: 12 }}
                        >
                          确认执行
                        </Button>
                        <Tooltip title="下载代码">
                          <Button
                            type="text"
                            size="middle"
                            icon={<DownloadOutlined />}
                            onClick={() => window.dispatchEvent(new CustomEvent('download-code', { detail: block }))}
                            style={{ marginLeft: 8 }}
                          />
                        </Tooltip>
                      </React.Fragment>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )} */}
      </div>

      {/* 消息操作按钮 */}
      {!isUser && (
        <div className="message-actions">
          <button
            className="action-button"
            onClick={() => onCopyMessage && onCopyMessage(message)}
          >
            <CopyOutlined /> 复制
          </button>
          <button
            className="action-button"
            onClick={() => onRegenerate && onRegenerate(index)}
          >
            <HistoryOutlined /> 重新生成
          </button>
        </div>
      )}
    </MessageItem>
  );
};

export default Message;
