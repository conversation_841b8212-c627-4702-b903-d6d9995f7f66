import {
  FileTextOutlined,
  CodeOutlined,
  ApiOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import React from 'react';

// AI 模型列表
export const AI_MODELS = [
  { value: 'qwen-max', label: 'Qwen-Max', desc: '高性能生成式语言模型' },
  { value: 'deepseek-v3', label: 'deepseekV3', desc: '最强大的大语言模型', disabled: true },
  { value: 'claude-3.7-sonnet', label: 'Claude-3.7', desc: '高效通用的对话模型', disabled: true },
  { value: 'gpt-4', label: 'GPT-4', desc: '最强大的大语言模型', disabled: true },
  { value: 'deepseek-r1', label: 'deepseekR1', desc: '高效通用的对话模型', disabled: true },
];

// 可@的对象
export const MENTIONS = [
  { id: 'orm', icon: React.createElement(CodeOutlined), name: 'Or<PERSON>', desc: 'Orm 领域模型' },
  { id: 'entities', icon: React.createElement(CodeOutlined), name: 'Entities', desc: 'Entitie 领域模型' },
  { id: 'columns', icon: React.createElement(CodeOutlined), name: 'Columns', desc: 'Column 领域模型' },

  // { id: 'code', icon: <CodeOutlined />, name: '代码', desc: '插入代码片段到对话' },
  // { id: 'api', icon: <ApiOutlined />, name: 'API', desc: '插入API文档到对话' },
  // { id: 'table', icon: <DatabaseOutlined />, name: '表格', desc: '插入数据结构到对话' },
  // { id: 'workflow', icon: <AppstoreOutlined />, name: '工作流', desc: '插入工作流到对话' },
  // { id: 'file', name: '文件', desc: '插入文件内容到对话' },
  // { id: 'code', name: '代码', desc: '插入代码片段到对话' },
  // { id: 'api', name: 'API', desc: '插入API文档到对话' },
  // { id: 'table', name: '表格', desc: '插入数据结构到对话' },
  // { id: 'workflow', name: '工作流', desc: '插入工作流到对话' },
];

// 示例对话数据
export const initialConversations = {
  'model': [],
  'metadata': [],
  'ui': []
};

// 模拟代码示例
export const CODE_EXAMPLES = {
  javascript: `// 获取用户信息
async function getUserInfo(userId) {
  try {
    const response = await fetch(\`/api/users/\${userId}\`);
    const data = await response.xml();

    if (data.error) {
      throw new Error(data.error);
    }

    return data;
  } catch (error) {
    console.error('Failed to fetch user info:', error);
    return null;
  }
}`,
  jsx: `import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUser() {
      try {
        const userData = await getUserInfo(userId);
        setUser(userData);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>Email: {user.email}</p>
      <p>Role: {user.role}</p>
    </div>
  );
}`,
  xml: `{
  "modelDefinition": {
    "name": "User",
    "fields": [
      {
        "name": "id",
        "type": "string",
        "required": true,
        "unique": true
      },
      {
        "name": "username",
        "type": "string",
        "required": true,
        "minLength": 3,
        "maxLength": 20
      },
      {
        "name": "email",
        "type": "string",
        "required": true,
        "format": "email"
      },
      {
        "name": "role",
        "type": "enum",
        "values": ["admin", "user", "guest"],
        "default": "user"
      },
      {
        "name": "createdAt",
        "type": "datetime",
        "defaultValue": "now()"
      }
    ],
    "indexes": [
      {
        "fields": ["email"],
        "unique": true
      }
    ]
  }
}`
};

// 模拟AI响应
export const mockAIResponse = {
  'model': [
    {
      content: '我可以帮您生成数据模型设计。请描述您需要的业务对象和关系。',
      time: new Date().toISOString()
    },
    {
      content: '基于您的描述，建议以下数据模型结构：',
      time: new Date().toISOString(),
      codeBlocks: [
        {
          language: 'xml',
          code: CODE_EXAMPLES.xml,
          fileName: 'productModel.xml'
        }
      ]
    }
  ],
  'metadata': [
    {
      content: '我可以帮您设计元数据结构。请告诉我您的应用场景。',
      time: new Date().toISOString()
    },
    {
      content: '根据您的需求，这里是推荐的元数据结构：',
      time: new Date().toISOString(),
      codeBlocks: [
        {
          language: 'xml',
          code: `{
              "entityTypes": [
                {
                  "id": "user",
                  "name": "用户",
                  "properties": [
                    {"id": "name", "type": "string", "required": true},
                    {"id": "email", "type": "string", "required": true},
                    {"id": "role", "type": "enum", "options": ["admin", "user", "guest"]}
                  ]
                },
                {
                  "id": "document",
                  "name": "文档",
                  "properties": [
                    {"id": "title", "type": "string", "required": true},
                    {"id": "content", "type": "text"},
                    {"id": "owner", "type": "reference", "refType": "user"}
                  ]
                }
              ]
            }`,
          fileName: 'metadataSchema.xml'
        }
      ]
    }
  ],
  'ui': [
    {
      content: '我可以帮您设计UI界面。请描述您需要的功能和布局。',
      time: new Date().toISOString()
    },
    {
      content: '基于您的需求，这是一个简洁的UI设计方案：',
      time: new Date().toISOString(),
      codeBlocks: [
        {
          language: 'jsx',
          code: CODE_EXAMPLES.jsx,
          fileName: 'Dashboard.jsx'
        }
      ]
    }
  ]
};
