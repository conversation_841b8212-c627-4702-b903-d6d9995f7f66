import React from 'react';
import { AIAssistant, FloatingButton } from '../components';

/**
 * 具有目标菜单的AI助手示例组件
 *
 * 这个组件展示了如何使用targetKey直接定位到特定菜单
 * 注意：targetKey只对一级菜单生效，子菜单通过自动展开父级菜单实现
 */
const ModelAssistant = () => {
  return <FloatingButton targetKey="model" />;
};

const MetadataAssistant = () => {
  return <FloatingButton targetKey="metadata" />;
};

const UIAssistant = () => {
  return <FloatingButton targetKey="ui" />;
};

/**
 * 使用例子：在不同页面放置不同的功能入口
 *
 * 模型设计页面：<ModelAssistant />
 * 元数据管理页面：<MetadataAssistant />
 * 界面设计页面：<UIAssistant />
 */

// 为了确保AIAssistant只被渲染一次，应该在应用的顶层组件中引入
const AppWithAIAssistant = ({ children }) => {
  return (
    <>
      {children}
      <AIAssistant />
    </>
  );
};

/**
 * 使用例子：
 *
 * import { AppWithAIAssistant } from './components/AIAssistant/example/TargetedAssistant';
 *
 * function App() {
 *   return (
 *     <AppWithAIAssistant>
 *       <YourAppRouter />
 *     </AppWithAIAssistant>
 *   );
 * }
 */

export {
  ModelAssistant,
  MetadataAssistant,
  UIAssistant,
  AppWithAIAssistant
};
