import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';
import { mockAIResponse, initialConversations } from '../constants';
import chatApi from '../service';
import codeBlockManager from '../utils/codeBlockManager';
import StreamMessageHandler from '../utils/streamMessageHandler';

/**
 * AI聊天状态管理钩子
 * @returns {Object} AI聊天状态和操作方法
 */
const useAIChat = () => {
  const [conversations, setConversations] = useState(initialConversations);
  const [loading, setLoading] = useState(false);
  const [currentTab, setCurrentTab] = useState('model');
  const [selectedModel, setSelectedModel] = useState('qwen-max');
  const [inputValue, setInputValue] = useState('');
  const [editingCode, setEditingCode] = useState(null);
  const [viewMode, setViewMode] = useState('chat'); // 'chat', 'edit', 'split'
  const [currentAiMessageId, setCurrentAiMessageId] = useState(null);
  const [submittedClarifications, setSubmittedClarifications] = useState(new Set()); // 跟踪已提交的澄清问题
  const abortControllerRef = useRef(null);

  // 检查是否有待回答的澄清问题
  const hasPendingClarification = useCallback(() => {
    const currentMessages = conversations[currentTab] || [];

    // 查找最后一条AI消息
    const lastAiMessage = [...currentMessages].reverse().find(msg => msg.role === 'ai');

    if (!lastAiMessage || !lastAiMessage.content) {
      return false;
    }

    // 提取代码块并检查是否有澄清问题
    const codeBlocks = codeBlockManager.extractCodeBlocks(lastAiMessage.content);
    const clarifyingBlock = codeBlocks.find(block => block.type === 'clarifying');

    if (!clarifyingBlock) {
      return false;
    }

    // 检查这个澄清问题是否已经提交过
    const messageId = lastAiMessage.id;
    return !submittedClarifications.has(messageId);
  }, [conversations, currentTab, submittedClarifications]);

  // 标记澄清问题为已提交
  const markClarificationAsSubmitted = useCallback((messageId) => {
    setSubmittedClarifications(prev => new Set([...prev, messageId]));
  }, []);

  /**
   * 切换聊天标签
   * @param {string} tab - 标签键名
   */
  const handleTabChange = useCallback((tab) => {
    setCurrentTab(tab);
    if (editingCode) {
      setEditingCode(null);
    }
    if (viewMode !== 'chat') {
      setViewMode('chat');
    }
  }, [editingCode, viewMode]);

  /**
   * 处理AI模型变更
   * @param {string} model - 选择的模型ID
   */
  const handleModelChange = useCallback((model) => {
    setSelectedModel(model);
  }, []);

  /**
   * 处理用户输入变化
   * @param {Object} e - 事件对象
   */
  const handleInputChange = useCallback((e) => {
    setInputValue(e.target.value);
  }, []);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        console.log("Aborted request on component unmount");
      }
    };
  }, []);

  /**
   * 发送消息到AI
   * @param {string} [customMessage] - 自定义消息文本，如果不提供则使用输入框中的值
   * @param {Object} [options] - 发送选项
   * @param {Object} [options.contextData] - API上下文数据，将发送给后端
   */
  const sendMessage = useCallback(async (customMessage, options = {}) => {
    const messageToSend = inputValue.trim();
    if (!messageToSend) {
      return;
    }

    // 中止之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      console.log("Aborted previous request");
    }

    // 创建用户消息和AI占位消息
    const userMessage = StreamMessageHandler.createUserMessage(messageToSend);
    const aiPlaceholderMessage = StreamMessageHandler.createPlaceholderMessage('ai');

    // 立即添加用户消息和AI占位消息
    setConversations(prev => ({
      ...prev,
      [currentTab]: [...prev[currentTab], userMessage, aiPlaceholderMessage],
    }));

    setInputValue('');

    // 准备请求数据
    const requestData = {
      message: messageToSend,
      modelType: selectedModel,
      // 如果提供了上下文数据，则一并发送
      ...(options.contextData ? { contextData: options.contextData } : {})
    };

    // 使用工具类处理流式请求
    try {
      await StreamMessageHandler.handleStreamRequest({
        apiCall: chatApi.sendMessageStream,
        requestData,
        setConversations,
        currentTab,
        setLoading,
        setCurrentMessageId: setCurrentAiMessageId,
        abortControllerRef,
        placeholderMessage: aiPlaceholderMessage,
        onSuccess: () => {
          console.log("AI消息发送成功");
        },
        onError: (error) => {
          message.error(`获取AI响应失败: ${error.message}`);
        }
      });
    } catch (error) {
      console.error('Error initiating sendMessageStream:', error);
      message.error('启动AI请求失败，请重试');
    }
  }, [inputValue, currentTab, selectedModel]);

  /**
   * 处理Enter键发送
   * @param {Object} e - 键盘事件
   */
  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  /**
   * 清空当前对话
   */
  const clearConversation = useCallback(() => {
    setConversations(prev => ({
      ...prev,
      [currentTab]: []
    }));
    // 清空已提交的澄清问题记录
    setSubmittedClarifications(new Set());
    message.success('对话已清空');
  }, [currentTab]);

  /**
   * 查看代码
   * @param {Object} codeBlock - 代码块对象
   */
  const viewCode = useCallback((codeBlock) => {
    setEditingCode(codeBlock);
    setViewMode('split');
  }, []);

  /**
   * 关闭代码编辑器
   */
  const closeCodeEditor = useCallback(() => {
    setEditingCode(null);
    setViewMode('chat');
  }, []);

  /**
   * 更新正在编辑的代码
   * @param {string} code - 新的代码内容
   */
  const updateEditingCode = useCallback((code) => {
    if (editingCode) {
      setEditingCode(prev => ({
        ...prev,
        code
      }));
    }
  }, [editingCode]);

  /**
   * 切换视图模式
   * @param {string} mode - 视图模式
   */
  const changeViewMode = useCallback((mode) => {
    setViewMode(mode);
  }, []);

  /**
   * 重新生成AI回复
   * @param {number} messageIndex - 消息索引
   */
  const regenerateAIResponse = useCallback(async (messageIndex) => {
    // 找到对应的用户消息
    const currentConversations = conversations[currentTab];
    if (!currentConversations || messageIndex <= 0 || messageIndex >= currentConversations.length) {
      return;
    }

    // 删除当前AI回复
    setConversations(prev => {
      const updatedConversations = [...prev[currentTab]];
      updatedConversations.splice(messageIndex, 1);

      return {
        ...prev,
        [currentTab]: updatedConversations
      };
    });

    // 模拟加载状态
    setLoading(true);

    try {
      const responsePool = mockAIResponse[currentTab];
      if (responsePool && responsePool.length > 0) {
        const randomIndex = Math.floor(Math.random() * responsePool.length);
        const aiResponse = responsePool[randomIndex];

        setConversations(prev => ({
          ...prev,
          [currentTab]: [
            ...prev[currentTab],
            {
              role: 'ai',
              ...aiResponse,
              time: new Date().toISOString()
            }
          ]
        }));
      }
    } catch (error) {
      console.error('Regenerate AI response error:', error);
      message.error('重新生成回复失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [currentTab, conversations, selectedModel]);

  /**
   * 执行生成的代码
   * @param {Object} codeBlock - 代码块对象
   */
  const executeCode = useCallback((codeBlock) => {
    message.info(`正在执行${codeBlock.language}代码...`);
    // 这里模拟执行过程，实际应该根据不同语言/类型进行处理
    setTimeout(() => {
      message.success('代码执行成功');
    }, 1500);
  }, []);

  return {
    // 状态
    conversations,
    loading,
    currentTab,
    selectedModel,
    inputValue,
    editingCode,
    viewMode,
    setConversations, // 暴露setConversations供Message组件使用

    // 方法
    handleTabChange,
    handleModelChange,
    handleInputChange,
    sendMessage,
    handleKeyPress,
    clearConversation,
    viewCode,
    closeCodeEditor,
    updateEditingCode,
    changeViewMode,
    regenerateAIResponse,
    executeCode,
    hasPendingClarification,
    markClarificationAsSubmitted
  };
};

export default useAIChat;
