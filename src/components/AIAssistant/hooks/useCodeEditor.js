import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import { copyToClipboard, downloadCode } from '../utils';

/**
 * 代码编辑器钩子
 * @param {Object} codeBlock - 初始代码块
 * @returns {Object} 编辑器状态和操作方法
 */
const useCodeEditor = (initialCodeBlock) => {
  const [codeBlock, setCodeBlock] = useState(initialCodeBlock);
  const [isEdited, setIsEdited] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [editorValue, setEditorValue] = useState(initialCodeBlock&& initialCodeBlock.code || '');

  // 当初始代码块变化时更新状态
  useEffect(() => {
    if (initialCodeBlock) {
      setCodeBlock(initialCodeBlock);
      setEditorValue(initialCodeBlock.code || '');
      setIsEdited(false);
    }
  }, [initialCodeBlock]);

  /**
   * 更新编辑器内容
   * @param {string} value - 新的代码内容
   */
  const handleEditorChange = useCallback((value) => {
    setEditorValue(value);
    setIsEdited(value !== codeBlock.code);
  }, [codeBlock]);

  /**
   * 保存编辑后的代码
   */
  const saveCode = useCallback(() => {
    if (!codeBlock) return;

    const updatedCodeBlock = {
      ...codeBlock,
      code: editorValue,
      lastEdited: new Date().toISOString()
    };

    setCodeBlock(updatedCodeBlock);
    setIsEdited(false);

    message.success('代码已保存');

    return updatedCodeBlock;
  }, [codeBlock, editorValue]);

  /**
   * 复制代码到剪贴板
   */
  const copyCode = useCallback(async () => {
    if (!editorValue) return;

    try {
      setIsCopying(true);
      await copyToClipboard(editorValue);
      message.success('代码已复制到剪贴板');
    } catch (error) {
      console.error('复制代码失败:', error);
      message.error('复制失败，请重试');
    } finally {
      setTimeout(() => {
        setIsCopying(false);
      }, 1500);
    }
  }, [editorValue]);

  /**
   * 下载代码文件
   */
  const handleDownload = useCallback(() => {
    if (!editorValue || !codeBlock) return;

    setIsDownloading(true);

    try {
      downloadCode(editorValue, codeBlock.fileName);
      message.success(`文件 ${codeBlock.fileName} 已下载`);
    } catch (error) {
      console.error('下载代码失败:', error);
      message.error('下载失败，请重试');
    } finally {
      setTimeout(() => {
        setIsDownloading(false);
      }, 1500);
    }
  }, [editorValue, codeBlock]);

  /**
   * 执行当前代码
   */
  const executeCode = useCallback(() => {
    if (!editorValue) return;

    setIsExecuting(true);
    message.info('正在执行代码...');

    // 这里应该根据不同语言执行不同的操作
    // 模拟异步执行过程
    setTimeout(() => {
      setIsExecuting(false);
      message.success('代码执行成功');
    }, 1500);
  }, [editorValue]);

  /**
   * 重置编辑器
   */
  const resetEditor = useCallback(() => {
    if (!codeBlock) return;

    setEditorValue(codeBlock.code || '');
    setIsEdited(false);
    message.info('已重置编辑器');
  }, [codeBlock]);

  return {
    codeBlock,
    editorValue,
    isEdited,
    isCopying,
    isDownloading,
    isExecuting,
    handleEditorChange,
    saveCode,
    copyCode,
    handleDownload,
    executeCode,
    resetEditor
  };
};

export default useCodeEditor;
