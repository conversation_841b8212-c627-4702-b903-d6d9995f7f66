import { useState, useEffect } from 'react';

/**
 * 响应式屏幕尺寸钩子
 * @returns {Object} 包含多个响应式标志的对象
 */
const useResponsive = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 576);
      setIsTablet(width > 576 && width <= 992);
      setIsDesktop(width > 992);
    };

    // 初始检查
    checkScreenSize();

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize);

    // 清理监听器
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 0
  };
};

export default useResponsive;
