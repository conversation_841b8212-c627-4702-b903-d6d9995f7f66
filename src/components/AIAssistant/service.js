import StreamRequestUtil from './utils/streamRequest.js';

// 公共前缀地址
const API_BASE_URL = `${window.location.protocol}//${window.location.hostname}:8111`;

const chatApi = {

  /**
   * 提交澄清问题的答案 - 流式输出版本
   * @param {Object} params - 答案内容，包含questionId, executionId, answer
   * @param {Object} callbacks - 回调函数集合
   * @param {function} callbacks.onMessageCallback - 接收流式消息的回调函数
   * @param {function} callbacks.onCompleteCallback - 完成时的回调函数
   * @param {function} callbacks.onErrorCallback - 错误时的回调函数
   * @returns {AbortController} 用于取消请求的控制器
   */
  submitClarificationAnswers: async (params, callbacks = {}) => {
    const url = `${API_BASE_URL}/clarification/loopAnswers`;
    return StreamRequestUtil.createRequest(url, params, callbacks);
  },

  /**
   * 流式发送消息到AI模型
   * @param {Object} params - 请求参数
   * @param {string} params.message - 用户输入文本
   * @param {string} params.modelType - 模型ID
   * @param {Object} callbacks - 回调函数集合
   * @param {function} callbacks.onMessageCallback - 接收流式消息的回调函数
   * @param {function} callbacks.onCompleteCallback - 完成时的回调函数
   * @param {function} callbacks.onErrorCallback - 错误时的回调函数
   * @returns {AbortController} 用于取消请求的控制器
   */
  sendMessageStream: async (params, callbacks = {}) => {
    const url = `${API_BASE_URL}/NewPromptBizModel/executePrompt`;
    return StreamRequestUtil.createRequest(url, params, callbacks);
  },
};

export default chatApi;
