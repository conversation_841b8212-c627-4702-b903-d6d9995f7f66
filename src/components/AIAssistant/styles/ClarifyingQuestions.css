.clarifying-questions-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.clarifying-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f3f4f6;
}

.clarifying-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.clarifying-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

.question-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.question-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.question-card.submitted {
  background: #f0f9ff;
  border-color: #0ea5e9;
  opacity: 0.9;
}

.question-card.submitted:hover {
  border-color: #0ea5e9;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);
}

.question-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-number {
  display: inline-block;
  background: #3b82f6;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.submitted-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.question-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.question-rationale {
  background: #eff6ff;
  border-left: 4px solid #3b82f6;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 0 6px 6px 0;
}

.rationale-label {
  font-weight: 600;
  color: #1e40af;
  display: block;
  margin-bottom: 8px;
}

.question-rationale p {
  margin: 0;
  color: #1e40af;
  font-size: 14px;
  line-height: 1.5;
}

.recommendations {
  background: #f0fdf4;
  border-left: 4px solid #10b981;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 0 6px 6px 0;
}

.recommendations-label {
  font-weight: 600;
  color: #047857;
  display: block;
  margin-bottom: 8px;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
  color: #047857;
}

.recommendations li {
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.answer-section {
  margin-top: 20px;
}

.answer-section label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.answer-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.answer-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.answer-textarea:disabled,
.answer-textarea[readonly] {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

.answer-textarea::placeholder {
  color: #9ca3af;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-cancel {
  background: #f3f4f6;
  color: #374151;
}

.btn-cancel:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-cancel:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-submit {
  background: #3b82f6;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submitted-message {
  text-align: center;
  padding: 15px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
}

.submitted-message p {
  margin: 0;
  color: #047857;
  font-weight: 500;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .clarifying-questions-container {
    margin: 10px;
    padding: 16px;
  }

  .clarifying-header h3 {
    font-size: 20px;
  }

  .question-content h4 {
    font-size: 16px;
  }

  .actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
