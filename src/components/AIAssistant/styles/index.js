import styled from 'styled-components';

// 悬浮按钮样式
export const AIFloatingButton = styled.div`
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;

  .ai-button {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
    border: none;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:hover, &:focus {
      transform: scale(1.08) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.16), 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .icon {
      font-size: 26px;
      color: #fff;
      filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
    }
  }

  @media (max-width: 768px) {
    bottom: 20px;
    right: 20px;

    .ai-button {
      width: 48px;
      height: 48px;

      .icon {
        font-size: 22px;
      }
    }
  }
`;

// 主布局样式
export const AIMenuLayout = styled.div`
  display: flex;
  height: 100%;
  background-color: #f9fafb;

  .ai-menu {
    width: 200px;
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    height: 100%;
    background-color: #fff;
    box-shadow: 1px 0 6px rgba(0, 0, 0, 0.03);
    /* overflow-y: auto; */

    .ai-menu-header {
      padding: 20px 16px 12px;
      border-bottom: 1px solid #f0f0f0;

      .menu-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 8px;
      }

      .menu-select {
        width: 100%;
      }
    }

    .ant-menu {
      border-right: none;
    }

    .ant-menu-item {
      /* margin: 4px 8px; */
      padding: 0 16px;
      border-radius: 6px;
      height: 40px;
      line-height: 40px;

      &.ant-menu-item-selected {
        background-color: #e6f7ff;
        color: #1890ff;
        font-weight: 500;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      .anticon {
        font-size: 16px;
      }
    }

    @media (max-width: 768px) {
      width: 200px;
    }

    @media (max-width: 576px) {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #f0f0f0;
      height: auto;
    }
  }

  .ai-chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 0 8px 8px 0;
    overflow: hidden;
    width: 100%;

    &:not(.code-view-mode) {
      max-width: 800px;
      margin: 0 auto;
    }

    .ai-chat-header {
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fff;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      z-index: 5;

      .header-actions {
        display: flex;
        align-items: center;
        margin-top: 8px;

        .action-button {
          margin-right: 12px;
          cursor: pointer;
          color: #595959;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }

    .ai-chat-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px 24px;
      background-color: #f9fafb;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      .ant-list-item {
        padding: 12px 0;
        background: transparent;

        .ant-list-item-meta-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
      }

      /* 消息组支持 */
      .message-group {
        margin-bottom: 24px;
        /* width: 800px; */

        &:last-child {
          margin-bottom: 0;
        }

        .group-time {
          text-align: center;
          padding: 4px 0;
          margin: 16px 0;
          color: #8c8c8c;
          font-size: 12px;
          position: relative;

          &::before, &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 15%;
            height: 1px;
            background: rgba(0, 0, 0, 0.08);
          }

          &::before {
            left: 25%;
          }

          &::after {
            right: 25%;
          }
        }
      }

      /* 滚动到底部按钮 */
      .scroll-to-bottom {
        position: fixed;
        bottom: 100px;
        right: 24px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #1890ff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 10;
        animation: fadeIn 0.3s ease-in-out;
        transition: all 0.3s;

        &:hover {
          background-color: #40a9ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }
    }

    .ai-chat-input {
      border-top: 1px solid #f0f0f0;
      background-color: #fff;
      position: relative;

      .input-wrapper {
        background-color: #f5f5f5;
        border-radius: 8px;
        position: relative;
        transition: all 0.3s;
        border: 1px solid transparent;

        &:focus-within {
          background-color: #fff;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }

      .ant-input {
        border: none;
        background-color: transparent;
        box-shadow: none;
        padding: 12px 16px;
        font-size: 14px;
        resize: none;

        &:focus {
          box-shadow: none;
        }
      }

      .input-tools {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;

        .left-tools {
          display: flex;
          align-items: center;

          .model-selector-area {
            display: flex;
            align-items: center;
            margin-right: 16px;
            padding-right: 16px;
            /* border-right: 1px solid #e0e0e0; */

            .model-label {
              color: #595959;
              font-size: 13px;
              margin-right: 8px;
            }

            .ant-select {
              min-width: 120px;
            }
          }

          .tool-button {
            color: #8c8c8c;
            font-size: 16px;
            margin-right: 16px;
            cursor: pointer;

            &:hover {
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    flex-direction: column;
  }
`;

// 消息组件样式
export const MessageItem = styled.div`
  margin-bottom: 20px;
  position: relative;
  width: 100%;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  /* 连续消息分组效果 */
  &[data-role="ai"] + &[data-role="ai"] {
    margin-top: -10px;

    .message-header {
      display: none;
    }

    .message-content {
      border-top-left-radius: 4px;
      margin-top: 6px;
    }
  }

  &[data-role="user"] + &[data-role="user"] {
    margin-top: -10px;

    .message-header {
      display: none;
    }

    .message-content {
      border-top-left-radius: 4px;
      margin-top: 6px;
    }
  }

  .message-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .avatar {
      margin-right: 12px;
    }

    .name {
      font-weight: 600;
      font-size: 14px;
      color: #262626;
    }

    .time {
      margin-left: 8px;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .message-content {
    margin-left: 40px;
    padding: 12px 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    position: relative;
    max-width: 100%;
    width: auto;

    /* 用户消息对齐右侧 */
    &.user-message {
      background-color: #e6f7ff;
      /* margin-left: auto;
      margin-right: 40px; */
    }

    /* 流式内容容器，防止在代码块输出时抖动 */
    .streaming-content {
      width: 100%;
      position: relative;
      transition: min-height 0.5s ease;
      overflow-anchor: none; /* 防止浏览器自动滚动锚点行为 */
      overflow: hidden; /* 防止内容溢出 */

      /* 代码提示 */
      code {
        display: inline-block;
        font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        padding: 1px 4px;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 3px;
      }
    }

    /* 代码块容器样式 */
    pre {
      margin: 10px 0;
      padding: 12px;
      border-radius: 8px;
      background-color: #1e1e1e !important;
      color: #f8f8f2;
      overflow-x: auto;
      font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      max-height: 500px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-all;
      tab-size: 2;
    }

    /* Prism主题覆盖 */
    :not(pre) > code[class*="language-"],
    pre[class*="language-"] {
      background: #1e1e1e;
      border-radius: 8px;
      padding: 1em;
      margin: .5em 0;
      overflow: auto;
    }

    code[class*="language-"] {
      text-shadow: none;
    }

    .token.tag,
    .token.attr-name,
    .token.namespace,
    .token.deleted,
    .token.property,
    .token.boolean,
    .token.number,
    .token.function-name {
      color: #f92672;
    }

    .token.function,
    .token.class-name {
      color: #66d9ef;
    }

    .token.selector,
    .token.important,
    .token.atrule,
    .token.keyword,
    .token.builtin {
      color: #0097ff;
    }

    .token.string,
    .token.char,
    .token.attr-value,
    .token.regex,
    .token.variable {
      color: #e6db74;
    }

    .token.operator,
    .token.entity,
    .token.url {
      color: #f8f8f2;
    }

    .token.comment,
    .token.prolog,
    .token.doctype,
    .token.cdata {
      color: #8292a2;
    }

    .paragraphs-container {
      .message-paragraph {
        margin-bottom: 16px;
        position: relative;

        &:not(:last-child) {
          padding-bottom: 16px;
          border-bottom: 1px dashed rgb(2 66 255 / 44%);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .paragraph-text {
          margin-bottom: 10px;
        }
      }

      .footer-actions {
          display: flex;
          gap: 12px;
          justify-content: flex-start;

          .ant-btn {
            border-radius: 6px;
          }
        }

      /* 澄清问题样式 */
      .clarifying-questions-wrapper {
        margin: 16px 0;
        padding: 0;
        border-radius: 8px;
        background: transparent;

        /* 覆盖澄清问题组件的容器样式，使其适应消息流 */
        .clarifying-questions-container {
          margin: 0;
          padding: 20px;
          background: #f8faff;
          border: 1px solid #d6e4ff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08);

          .clarifying-header {
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e6f7ff;

            h3 {
              font-size: 18px;
              margin-bottom: 6px;
            }

            p {
              font-size: 14px;
              color: #666;
            }
          }

          .questions-list {
            gap: 16px;
            margin-bottom: 20px;
          }

          .question-card {
            background: #ffffff;
            border: 1px solid #e6f7ff;

            &:hover {
              border-color: #91d5ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
            }
          }

          .actions {
            padding-top: 16px;
            border-top: 1px solid #e6f7ff;
          }
        }
      }
    }

    /* 用户消息的编辑和复制按钮 */
    .user-message-actions {
      position: absolute;
      display: flex;
      opacity: 0;
      transition: opacity 0.2s ease;
      font-size: 12px;

      &.edit-action {
        bottom: 6px;
        right: 6px;
      }

      &.copy-action {
        bottom: 6px;
        left: 6px;
      }

      .action-icon {
        background: rgba(230, 247, 255, 0.7);
        color: #1890ff;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        backdrop-filter: blur(2px);

        &:hover {
          background: rgba(24, 144, 255, 0.2);
          transform: scale(1.1);
        }
      }
    }

    /* 显示用户消息操作按钮 */
    &.user-message:hover {
      .user-message-actions {
        opacity: 1;
      }
    }

    .context-dialog {
      margin: 24px auto 8px;
      max-width: 90%;
      /* width: 360px; */
      border-radius: 12px;
      background-color: #f9f9f9;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border: 1px solid #eaeaea;
      position: relative;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      transform-origin: center;

      /* 展开状态 */
      &.expanded {
        max-width: 90%;
        width: 480px;
        transform: translateY(0);

        .context-dialog-content {
          max-height: 600px;
        }

        .code-block-card {
          transform: translateY(0);
          opacity: 1;
          transition-delay: 0.1s;
        }
      }

      /* 添加悬停效果 */
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
        border-color: #bae0ff;
      }

      /* 添加动态渐变背景 */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #1890ff, #36cfc9, #52c41a, #36cfc9, #1890ff);
        background-size: 200% 100%;
        animation: gradientShift 8s infinite linear;
        opacity: 0.8;
      }

      @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }

      .context-dialog-header {
        display: flex;
        align-items: center;
        padding: 14px 18px;
        background-color: #f0f5ff;
        border-bottom: 1px solid #e6e6e6;
        position: relative;

        .context-icon {
          font-size: 16px;
          color: #1890ff;
          margin-right: 10px;
          /* 添加图标动效 */
          animation: pulseIcon 2s infinite ease-in-out;
        }

        @keyframes pulseIcon {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }

        .context-title {
          font-weight: 600;
          font-size: 15px;
          color: #262626;
          flex: 1;
        }

        .expand-button {
          color: #8c8c8c;
          transition: all 0.3s;

          &:hover {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }

        .context-count {
          font-size: 13px;
          color: #8c8c8c;
          padding: 2px 8px;
          background-color: rgba(245, 245, 245, 0.8);
          border-radius: 12px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
          backdrop-filter: blur(2px);
          transition: all 0.3s;

          &:hover {
            background-color: #e6f7ff;
            color: #1890ff;
          }
        }
      }

      .context-dialog-content {
        padding: 16px;
        max-height: 450px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

        &::-webkit-scrollbar {
          width: 5px;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;

          &:hover {
            background-color: rgba(24, 144, 255, 0.5);
          }
        }
      }

      /* 代码卡片动画效果 */
      .code-block-card {
        transition: all 0.3s ease;
        transform: translateY(5px);
        opacity: 0.95;

        &:nth-child(2) {
          transition-delay: 0.05s;
        }

        &:nth-child(3) {
          transition-delay: 0.1s;
        }

        &:nth-child(n+4) {
          transition-delay: 0.15s;
        }

        &:hover {
          transform: translateY(-2px) scale(1.01);
          opacity: 1;
        }
      }

      /* 响应式设计优化 */
      @media (max-width: 768px) {
        width: 100%;
        max-width: 95%;

        &.expanded {
          width: 100%;
        }
      }
    }

    .code-blocks {
      margin-top: 12px;
    }

    .code-block-card {
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      margin-bottom: 16px;
      transition: all 0.3s;
      overflow: hidden;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .code-block-header {
        padding: 12px 16px;
        border-bottom: 1px solid #e0e0e0;
        background-color: #fafafa;

        .code-info {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 10px;

          .code-language {
            font-size: 12px;
            font-weight: 500;
            margin: 0;
          }

          .code-filename {
            font-weight: 500;
            color: #333;
            font-size: 14px;
            max-width: 350px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      pre {
        margin: 0;
        padding: 16px;
        background-color: #e1e1e1 !important;
        color: #f8f8f2;
        overflow-x: auto;
        font-family: 'Fira Code', monospace;
        font-size: 14px;
        line-height: 1.5;
        tab-size: 2;
      }

      code {
        font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        text-shadow: none;
      }

      .code-actions {
        padding: 14px 16px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background-color: #fff;

        .view-code-btn {
          background-color: #1890ff;
          border-color: #1890ff;

          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
    }

    .code-block {
      margin: 16px 0;
      background-color: #1e1e1e;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      .code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #2d2d2d;
        color: #e6e6e6;
        padding: 8px 16px;
        font-size: 12px;
      }

      pre {
        margin: 0;
        padding: 12px 16px;
        overflow-x: auto;
        background-color: #1e1e1e !important;
        white-space: pre-wrap;
        word-break: break-word;
        max-height: 500px;
      }

      code {
        font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        color: #f8f8f2;
        text-shadow: none;
      }
    }
  }

  /* 消息操作按钮 */
  .message-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-left: 40px;
    opacity: 0.6;
    transition: opacity 0.3s;

    &:hover {
      opacity: 1;
    }

    .action-button {
      background: none;
      border: none;
      padding: 4px 8px;
      font-size: 13px;
      color: #8c8c8c;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        color: #1890ff;
        background-color: rgba(24, 144, 255, 0.08);
      }
    }
  }

  @media (max-width: 768px) {
    .message-content {
      max-width: 95%;
    }
  }
`;

// 代码查看器样式
export const CodeViewer = styled.div`
  border-radius: 8px;
  overflow: hidden;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

  .code-header {
    background-color: #2d2d2d;
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .code-title {
      color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;

      .code-icon {
        margin-right: 8px;
      }
    }

    .header-actions {
      display: flex;

      .action-button {
        cursor: pointer;
        color: #a0a0a0;
        font-size: 14px;
        margin-left: 16px;

        &:hover {
          color: #fff;
        }
      }
    }
  }

  .code-content {
    max-height: 400px;
    overflow-y: auto;
    background-color: #1e1e1e;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
    }

    pre {
      margin: 0;
      padding: 16px;

      code {
        font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
        font-size: 13px;
      }
    }
  }
`;

// 提及下拉菜单样式
export const MentionDropdown = styled.div`
  position: absolute;
  bottom: 100%;
  left: 20px;
  background: #fff;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  width: 220px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;

  .mention-header {
    padding: 8px 12px;
    font-size: 12px;
    color: #8c8c8c;
    border-bottom: 1px solid #f0f0f0;
  }

  .mention-item {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    .mention-icon {
      margin-right: 8px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      background-color: #e6f7ff;
      color: #1890ff;
    }

    .mention-name {
      font-size: 14px;
      font-weight: 500;
    }

    .mention-desc {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }
`;

// 代码执行确认样式
export const ExecutionPanel = styled.div`
  background-color: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin: 16px 0;
  overflow: hidden;

  .execution-header {
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-weight: 500;
      font-size: 14px;
      color: #262626;
      display: flex;
      align-items: center;

      .icon {
        margin-right: 8px;
        color: #1890ff;
      }
    }
  }

  .execution-content {
    padding: 16px;

    .action-buttons {
      display: flex;
      margin-top: 16px;
      justify-content: flex-end;
      gap: 8px;
    }
  }
`;

// 编辑侧边栏样式
export const EditorSidebar = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;

  .editor-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .file-info {
      display: flex;
      align-items: center;

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #1890ff;
      }

      .file-name {
        font-weight: 500;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .editor-body {
    flex: 1;
    overflow: hidden;
    position: relative;

    .editor-container {
      height: 100%;
      width: 100%;
      overflow: auto;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
    }
  }

  .editor-footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;

    .status {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
`;

// 双向布局容器
export const SplitLayout = styled.div`
  display: flex;
  height: 100%;
  background-color: #f0f2f5;

  .left-panel {
    width: 50%;
    border-right: 1px solid #e8e8e8;
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 6px rgba(0, 0, 0, 0.03);
    position: relative;
    z-index: 1;

    .split-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      background: #fff;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      z-index: 2;
      position: relative;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .code-title {
          font-weight: 500;
          font-size: 14px;
          color: #1f1f1f;
          display: flex;
          align-items: center;
          gap: 8px;
          max-width: 300px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .language-tag {
          font-size: 12px;
          padding: 2px 8px;
          background-color: #f0f5ff;
          color: #1890ff;
          border-radius: 12px;
          border: 1px solid #d6e4ff;
          font-weight: 500;
          display: flex;
          align-items: center;
          transition: all 0.3s;

          &:hover {
            background-color: #e6f7ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
          }
        }

        .ant-btn {
          &:hover {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }
      }

      .header-right {
        display: flex;
        align-items: center;

        .ant-btn {
          &:hover {
            color: #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }
      }
    }

    .editor-container {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background-color: #fafafa;
    }

    .footer-actions {
      display: flex;
      padding: 12px 16px;
      border-top: 1px solid #f0f0f0;
      background-color: #fafafa;
      gap: 12px;
      justify-content: flex-end;
      box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);
      position: relative;
      z-index: 2;

      .ant-btn {
        border-radius: 6px;

        &[type="primary"] {
          background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
          border: none;
          box-shadow: 0 2px 6px rgba(82, 196, 26, 0.2);

          &:hover {
            background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
            transform: translateY(-1px);
          }
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    height: 100%;
    overflow: hidden;
    background-color: #f9fafb;
  }

  @media (max-width: 992px) {
    flex-direction: column;

    .left-panel {
      width: 100%;
      height: 50%;
      border-right: none;
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      z-index: 5;

      .split-header {
        padding: 12px 16px;

        .code-title {
          max-width: 200px;
        }
      }
    }

    .right-panel {
      height: 50%;
    }
  }
`;

// 代码编辑器包装器样式
export const CodeEditorWrapper = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fafafa;
  overflow: hidden;
  /* In code-view-mode it will use full width, otherwise constrained */
  width: 100%;

  .code-editor {
    flex: 1;
    width: 100%;
    height: calc(100% - 40px);
    overflow: hidden;
  }

  /* CodeMirror 样式覆盖 */
  .CodeMirror {
    height: 100%;
    font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    background-color: #fafafa;
    padding: 8px 0;
  }

  .CodeMirror-gutters {
    border-right: 1px solid #eee;
    background-color: #f5f5f5;
  }

  .CodeMirror-linenumber {
    color: #999;
  }

  .CodeMirror-cursor {
    border-left: 2px solid #1890ff;
  }

  .CodeMirror-selected {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  .CodeMirror-activeline-background {
    background-color: rgba(232, 242, 254, 0.8);
  }

  .CodeMirror-matchingbracket {
    color: #52c41a !important;
    font-weight: bold;
    text-shadow: 0 0 2px rgba(82, 196, 26, 0.3);
  }

  .CodeMirror-foldmarker {
    color: #1890ff;
    font-family: Arial, sans-serif;
    text-shadow: 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* 滚动条样式 */
  .CodeMirror-vscrollbar::-webkit-scrollbar,
  .CodeMirror-hscrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .CodeMirror-vscrollbar::-webkit-scrollbar-thumb,
  .CodeMirror-hscrollbar::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
  }

  .CodeMirror-vscrollbar::-webkit-scrollbar-thumb:hover,
  .CodeMirror-hscrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #aaa;
  }

  .editor-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-top: 1px solid #eee;
    background-color: #fafafa;
    z-index: 1;
    position: relative;
    height: 40px;

    .status {
      font-size: 12px;
      color: #8c8c8c;
    }

    .editor-actions {
      display: flex;
      gap: 4px;
    }
  }
`;
