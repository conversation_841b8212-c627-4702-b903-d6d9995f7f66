/**
 * 代码块管理器
 * 提供统一的代码块处理功能
 */
import Prism from 'prismjs';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-markup';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-xml-doc';

/**
 * 根据语言获取文件扩展名
 * @param {string} language - 代码语言
 * @returns {string} 文件扩展名
 */
const getFileExtension = (language) => {
  const extensionMap = {
    javascript: 'js',
    js: 'js',
    typescript: 'ts',
    ts: 'ts',
    jsx: 'jsx',
    tsx: 'tsx',
    html: 'html',
    css: 'css',
    json: 'json',
    python: 'py',
    py: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    go: 'go',
    rust: 'rs',
    php: 'php',
    ruby: 'rb',
    swift: 'swift',
    csharp: 'cs',
    sql: 'sql',
    markdown: 'md',
    md: 'md',
    xml: 'xml',
    text: 'txt',
    // 特殊处理：澄清问题
    clarifyingquestions: 'clarifying'
  };

  return extensionMap[(language || '').toLowerCase()] || 'txt';
};

/**
 * 解析澄清问题数据
 * @param {string} content - JSON字符串内容
 * @returns {Object|null} 解析后的澄清问题对象，包含executionId和questions数组
 */
const parseClarifyingQuestions = (content) => {
  try {
    const data = JSON.parse(content);

    // 新的数据格式：{executionId:'', clarifyingQuestions:**}
    if (data && data.executionId && data.clarifyingQuestions && Array.isArray(data.clarifyingQuestions)) {
      return {
        executionId: data.executionId,
        qaLoopId: data.qaLoopId,
        questions: data.clarifyingQuestions.map(q => ({
          // 显示字段
          sequence: q.sequence,
          question: q.question,
          rationale: q.rationale,
          potentialRecommendations: q.potentialRecommendations || [],
          // 隐藏字段（用于提交）
          questionId: q.questionId,
          // 用户答案（初始为空）
          userAnswer: ''
        }))
      };
    }
  } catch (error) {
    console.error('解析澄清问题失败:', error);
  }
  return null;
};

/**
 * 从文本中提取代码块
 * @param {string} content - 输入文本内容
 * @returns {Array} 代码块数组
 */
const extractCodeBlocks = (content) => {
  if (!content) return [];

  const codeBlocks = [];
  const codeBlockRegex = /```([a-zA-Z0-9+_-]*)\s?([\s\S]*?)```/g;
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const language = match[1] || 'text';
    const code = match[2].trim();

    if (code) {
      const block = {
        language,
        code,
        raw: match[0],
        index: match.index,
        fileName: `code-${language}-${Date.now()}.${getFileExtension(language)}`
      };

      // 特殊处理澄清问题
      if (language.toLowerCase() === 'clarifyingquestions') {
        const clarifyingData = parseClarifyingQuestions(code);
        if (clarifyingData) {
          block.clarifyingData = clarifyingData;
          block.type = 'clarifying';
        }
      }

      codeBlocks.push(block);
    }
  }

  return codeBlocks;
};

/**
 * 高亮代码块
 * @param {function} callback - 高亮后的回调函数
 */
const highlightCodeBlocks = (callback) => {
  setTimeout(() => {
    try {
      Prism.highlightAll();
      if (callback) callback();
    } catch (err) {
      console.error('代码高亮失败:', err);
    }
  }, 100);
};

export default {
  extractCodeBlocks,
  highlightCodeBlocks,
  getFileExtension,
  parseClarifyingQuestions
};
