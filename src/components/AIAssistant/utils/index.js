/**
 * 格式化时间显示
 * @param {string} isoString - ISO格式的时间字符串
 * @returns {string} 格式化后的时间字符串 (HH:MM)
 */
export const formatTime = (isoString) => {
  const date = new Date(isoString);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};


/**
 * 根据功能类型获取菜单标题
 * @param {string} key - 功能类型
 * @returns {string} 对应的菜单标题
 */
export const getMenuTitle = (key) => {
  switch (key) {
    case 'model':
      return 'AI 生成模型';
    case 'metadata':
      return 'AI 生成元数据';
    case 'ui':
      return 'AI 生成界面';
    case 'history':
      return '历史记录';
    default:
      return '聊天';
  }
};

/**
 * 以特定格式下载代码
 * @param {string} code - 代码内容
 * @param {string} fileName - 文件名
 */
export const downloadCode = (code, fileName) => {
  const blob = new Blob([code], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');

  link.href = url;
  link.download = fileName;
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();

  setTimeout(() => {
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, 100);
};

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise} 复制操作的Promise
 */
export const copyToClipboard = (text) => {
  return navigator.clipboard.writeText(text);
};

/**
 * 模拟API调用延迟
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} 延迟Promise
 */
export const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
