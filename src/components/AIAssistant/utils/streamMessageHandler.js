/**
 * 流式消息处理工具类
 * 抽取流式消息处理的通用逻辑，可被不同场景复用
 */
class StreamMessageHandler {

  /**
   * 创建流式消息处理器
   * @param {Object} options - 配置选项
   * @param {Function} options.updateMessage - 更新消息内容的回调函数
   * @param {Function} options.onComplete - 完成时的回调函数
   * @param {Function} options.onError - 错误时的回调函数
   * @param {Function} options.onStart - 开始时的回调函数（可选）
   * @param {boolean} options.processEmptyAsBreak - 是否将空字符串处理为换行，默认true
   * @returns {Object} 包含回调函数的对象
   */
  static createStreamCallbacks(options = {}) {
    const {
      updateMessage,
      onComplete,
      onError,
      onStart,
      processEmptyAsBreak = true
    } = options;

    return {
      onMessageCallback: (chunk) => {
        console.log("收到流式数据块:", chunk);

        if (updateMessage) {
          // 将空字符串替换成<br/>，保持换行（如果启用）
          const processedChunk = processEmptyAsBreak && chunk === '' ? '<br/>' : chunk;
          updateMessage(processedChunk);
        }
      },

      onCompleteCallback: () => {
        console.log("流式响应完成");
        if (onComplete) {
          onComplete();
        }
      },

      onErrorCallback: (error) => {
        console.error('流式响应错误:', error);
        if (onError) {
          onError(error);
        }
      }
    };
  }

  /**
   * 创建消息状态管理器
   * @param {Object} initialMessage - 初始消息对象
   * @param {Function} setConversations - 设置对话状态的函数
   * @param {string} currentTab - 当前标签页
   * @returns {Object} 消息管理器对象
   */
  static createMessageManager(initialMessage, setConversations, currentTab) {
    let messageId = initialMessage.id;
    let isCompleted = false;

    return {
      // 更新消息内容
      updateContent: (chunk) => {
        if (isCompleted) return;

        setConversations(prev => {
          const updatedTabConvos = prev[currentTab].map(msg => {
            if (msg.id === messageId) {
              return {
                ...msg,
                content: msg.content + chunk
              };
            }
            return msg;
          });
          return { ...prev, [currentTab]: updatedTabConvos };
        });
      },

      // 标记消息完成
      markComplete: () => {
        isCompleted = true;
        setConversations(prev => {
          const updatedTabConvos = prev[currentTab].map(msg => {
            if (msg.id === messageId) {
              return { ...msg, isLoading: false };
            }
            return msg;
          });
          return { ...prev, [currentTab]: updatedTabConvos };
        });
      },

      // 标记消息错误
      markError: (errorMessage) => {
        isCompleted = true;
        setConversations(prev => {
          const updatedTabConvos = prev[currentTab].map(msg => {
            if (msg.id === messageId) {
              return {
                ...msg,
                content: `Error: ${errorMessage}`,
                isLoading: false,
                isError: true
              };
            }
            return msg;
          });
          return { ...prev, [currentTab]: updatedTabConvos };
        });
      },

      // 移除消息
      removeMessage: () => {
        setConversations(prev => {
          const filteredTabConvos = prev[currentTab].filter(msg => msg.id !== messageId);
          return { ...prev, [currentTab]: filteredTabConvos };
        });
      },

      // 获取消息ID
      getMessageId: () => messageId,

      // 检查是否已完成
      isCompleted: () => isCompleted
    };
  }

  /**
   * 创建标准的AI响应占位消息
   * @param {string} type - 消息类型，如 'ai', 'clarification'
   * @param {Object} extraProps - 额外属性
   * @returns {Object} 消息对象
   */
  static createPlaceholderMessage(type = 'ai', extraProps = {}) {
    return {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      role: type,
      content: '',
      time: new Date().toISOString(),
      isLoading: true,
      ...extraProps
    };
  }

  /**
   * 创建用户消息
   * @param {string} content - 消息内容
   * @param {Object} extraProps - 额外属性
   * @returns {Object} 消息对象
   */
  static createUserMessage(content, extraProps = {}) {
    return {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      role: 'user',
      content,
      time: new Date().toISOString(),
      ...extraProps
    };
  }

  /**
   * 处理流式请求的完整流程
   * @param {Object} params - 参数对象
   * @param {Function} params.apiCall - API调用函数
   * @param {Object} params.requestData - 请求数据
   * @param {Function} params.setConversations - 设置对话状态的函数
   * @param {string} params.currentTab - 当前标签页
   * @param {Function} params.setLoading - 设置加载状态的函数
   * @param {Function} params.setCurrentMessageId - 设置当前消息ID的函数
   * @param {Object} params.abortControllerRef - 中止控制器引用
   * @param {Object} params.placeholderMessage - 占位消息对象
   * @param {Function} params.onSuccess - 成功回调（可选）
   * @param {Function} params.onError - 错误回调（可选）
   * @returns {Promise} Promise对象
   */
  static async handleStreamRequest(params) {
    const {
      apiCall,
      requestData,
      setConversations,
      currentTab,
      setLoading,
      setCurrentMessageId,
      abortControllerRef,
      placeholderMessage,
      onSuccess,
      onError
    } = params;

    // 创建消息管理器
    const messageManager = this.createMessageManager(
      placeholderMessage,
      setConversations,
      currentTab
    );

    // 设置当前消息ID和加载状态
    setCurrentMessageId(placeholderMessage.id);
    setLoading(true);

    try {
      // 创建流式回调
      const callbacks = this.createStreamCallbacks({
        updateMessage: messageManager.updateContent,
        onComplete: () => {
          setLoading(false);
          setCurrentMessageId(null);
          abortControllerRef.current = null;
          messageManager.markComplete();
          if (onSuccess) onSuccess();
        },
        onError: (error) => {
          setLoading(false);
          setCurrentMessageId(null);
          abortControllerRef.current = null;
          messageManager.markError(error.message);
          if (onError) onError(error);
        }
      });

      // 发起API调用
      abortControllerRef.current = await apiCall(requestData, callbacks);

    } catch (error) {
      console.error('启动流式请求失败:', error);
      setLoading(false);
      setCurrentMessageId(null);
      messageManager.removeMessage();
      if (onError) onError(error);
      throw error;
    }
  }

  /**
   * 创建澄清问题提交的流式处理器
   * @param {Object} clarificationData - 澄清数据
   * @param {Function} setConversations - 设置对话状态的函数
   * @param {string} currentTab - 当前标签页
   * @param {Function} setLoading - 设置加载状态的函数
   * @param {Function} setCurrentMessageId - 设置当前消息ID的函数
   * @param {Object} abortControllerRef - 中止控制器引用
   * @param {Function} apiCall - API调用函数
   * @param {Function} onSuccess - 成功回调（可选）
   * @param {Function} onError - 错误回调（可选）
   * @returns {Promise} Promise对象
   */
  static async handleClarificationSubmission(params) {
    const {
      clarificationData,
      setConversations,
      currentTab,
      setLoading,
      setCurrentMessageId,
      abortControllerRef,
      apiCall,
      onSuccess,
      onError
    } = params;

    // 中止之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      console.log("中止之前的澄清请求");
    }

    // 创建澄清响应占位消息
    const clarificationResponseMessage = this.createPlaceholderMessage('clarification-response', {
      clarificationId: clarificationData.questionId,
      executionId: clarificationData.executionId
    });

    // 添加占位消息到对话中
    setConversations(prev => ({
      ...prev,
      [currentTab]: [...prev[currentTab], clarificationResponseMessage],
    }));

    // 使用通用流式处理
    return this.handleStreamRequest({
      apiCall,
      requestData: clarificationData,
      setConversations,
      currentTab,
      setLoading,
      setCurrentMessageId,
      abortControllerRef,
      placeholderMessage: clarificationResponseMessage,
      onSuccess,
      onError
    });
  }
}

export default StreamMessageHandler;
