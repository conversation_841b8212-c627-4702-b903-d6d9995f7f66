import { useAdapter } from 'src/common/axios/useAdapter';

/**
 * 流式请求工具类
 * 提供通用的流式请求处理功能，可被各个service模块复用
 */
class StreamRequestUtil {

  /**
   * 创建流式请求
   * @param {string} url - 请求URL
   * @param {Object} requestData - 请求数据
   * @param {Object} callbacks - 回调函数集合
   * @param {function} callbacks.onMessageCallback - 接收流式消息的回调函数
   * @param {function} callbacks.onCompleteCallback - 完成时的回调函数
   * @param {function} callbacks.onErrorCallback - 错误时的回调函数
   * @param {Object} options - 可选配置
   * @param {string} options.method - HTTP方法，默认为POST
   * @param {Object} options.headers - 额外的请求头
   * @param {boolean} options.useAuth - 是否使用认证，默认为true
   * @returns {Promise<AbortController>} 用于取消请求的控制器
   */
  static async createRequest(url, requestData, callbacks = {}, options = {}) {
    // 确保callbacks对象存在，并提供默认值
    const {
      onMessageCallback = () => {},
      onCompleteCallback = () => {},
      onErrorCallback = (error) => console.error('流式请求错误:', error)
    } = callbacks;

    const {
      method = 'POST',
      headers: extraHeaders = {},
      useAuth = true
    } = options;

    const controller = new AbortController();
    const signal = controller.signal;

    try {
      // 构建请求头
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        ...extraHeaders,
      };

      // 如果需要认证，添加认证头
      if (useAuth) {
        const { useAuthToken, useTokenPrefix } = useAdapter();
        headers['Authorization'] = `${useTokenPrefix()}${useAuthToken()}`;
      }

      const response = await fetch(url, {
        method,
        headers,
        body: JSON.stringify(requestData),
        signal,
      });

      if (!response.ok) {
        // 尝试读取错误文本，如果失败则使用 statusText
        let errorText = response.statusText;
        try {
          errorText = await response.text();
        } catch (textError) {
          console.warn('无法读取错误响应体:', textError);
        }
        throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
      }

      if (!response.body) {
        throw new Error('响应体为空');
      }

      // 处理流式响应
      await this.processStreamResponse(response.body, {
        onMessageCallback,
        onCompleteCallback,
        onErrorCallback
      });

    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('请求被用户中止');
      } else {
        console.error('请求或流处理过程中发生错误:', error);
        if (onErrorCallback) onErrorCallback(error);
      }
    }

    return controller;
  }

  /**
   * 处理流式响应的核心逻辑
   * @param {ReadableStream} body - 响应体流
   * @param {Object} callbacks - 回调函数集合
   */
  static async processStreamResponse(body, callbacks) {
    const { onMessageCallback, onCompleteCallback, onErrorCallback } = callbacks;

    // 使用 TextDecoderStream 处理流式文本解码
    const reader = body.pipeThrough(new TextDecoderStream()).getReader();

    // 添加缓冲区处理未完成的行
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // 处理最后可能残留的缓冲区数据
          if (buffer && buffer.startsWith('data:')) {
            const lastData = buffer.substring(5);
            if (lastData && onMessageCallback) {
              onMessageCallback(lastData);
            }
          }

          console.log('流式响应完成');
          if (onCompleteCallback) onCompleteCallback();
          break;
        }

        // 将新数据添加到缓冲区
        buffer += value;

        // 尝试从缓冲区中提取完整的行
        const lines = buffer.split('\n');

        // 保留最后一行作为新的缓冲区(可能是不完整的行)
        buffer = lines.pop() || '';

        // 处理所有完整的行
        lines.forEach(line => {
          if (line.startsWith('data:')) {
            const data = line.substring(5); // 移除 'data:'
            if (data !== undefined && onMessageCallback) {
              try {
                // 返回原始数据，不做额外处理
                onMessageCallback(data);
              } catch (err) {
                console.error('处理数据块时发生错误:', err);
              }
            }
          }
          // 可以在此处理其他 SSE 字段，如 'event:', 'id:', 'retry:'
        });
      }
    } catch (streamError) {
      // 捕获流处理过程中的错误
      if (streamError.name !== 'AbortError') {
        console.error('流处理错误:', streamError);
        if (onErrorCallback) onErrorCallback(streamError);
      }
      throw streamError;
    }
  }

  /**
   * 创建带基础URL的流式请求
   * @param {string} baseUrl - 基础URL
   * @param {string} endpoint - API端点路径
   * @param {Object} requestData - 请求数据
   * @param {Object} callbacks - 回调函数集合
   * @param {Object} options - 可选配置
   * @returns {Promise<AbortController>} 用于取消请求的控制器
   */
  static async createRequestWithBaseUrl(baseUrl, endpoint, requestData, callbacks = {}, options = {}) {
    const url = `${baseUrl}${endpoint}`;
    return this.createRequest(url, requestData, callbacks, options);
  }

  /**
   * 批量创建流式请求
   * @param {Array} requests - 请求配置数组
   * @param {Object} globalCallbacks - 全局回调函数
   * @returns {Promise<Array<AbortController>>} 控制器数组
   */
  static async createBatchRequests(requests, globalCallbacks = {}) {
    const controllers = [];

    for (const request of requests) {
      const { url, requestData, callbacks = {}, options = {} } = request;

      // 合并全局回调和单个请求的回调
      const mergedCallbacks = {
        ...globalCallbacks,
        ...callbacks
      };

      try {
        const controller = await this.createRequest(url, requestData, mergedCallbacks, options);
        controllers.push(controller);
      } catch (error) {
        console.error('创建批量请求失败:', error);
        if (globalCallbacks.onErrorCallback) {
          globalCallbacks.onErrorCallback(error);
        }
      }
    }

    return controllers;
  }

  /**
   * 取消所有请求
   * @param {Array<AbortController>} controllers - 控制器数组
   */
  static abortAllRequests(controllers) {
    controllers.forEach(controller => {
      if (controller && typeof controller.abort === 'function') {
        controller.abort();
      }
    });
  }

  /**
   * 创建重试机制的流式请求
   * @param {string} url - 请求URL
   * @param {Object} requestData - 请求数据
   * @param {Object} callbacks - 回调函数集合
   * @param {Object} options - 可选配置
   * @param {number} options.maxRetries - 最大重试次数，默认为3
   * @param {number} options.retryDelay - 重试延迟时间(ms)，默认为1000
   * @returns {Promise<AbortController>} 用于取消请求的控制器
   */
  static async createRequestWithRetry(url, requestData, callbacks = {}, options = {}) {
    const { maxRetries = 3, retryDelay = 1000, ...requestOptions } = options;
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.createRequest(url, requestData, callbacks, requestOptions);
      } catch (error) {
        lastError = error;

        if (attempt < maxRetries && error.name !== 'AbortError') {
          console.warn(`请求失败，${retryDelay}ms后进行第${attempt + 1}次重试:`, error.message);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        } else {
          break;
        }
      }
    }

    throw lastError;
  }
}

export default StreamRequestUtil;
