# StreamRequestUtil 流式请求工具类

## 概述

`StreamRequestUtil` 是一个通用的流式请求处理工具类，提供了完整的流式HTTP请求功能，支持Server-Sent Events (SSE) 数据流处理。该工具类可以被项目中的任何service模块复用，提供统一的流式请求接口。

## 主要特性

- 🚀 **通用性强**：可被任何service模块调用
- 🔄 **重试机制**：支持自动重试失败的请求
- 📦 **批量处理**：支持同时处理多个流式请求
- 🔐 **认证支持**：可选的认证头处理
- ⚡ **错误处理**：完善的错误捕获和处理机制
- 🛑 **请求取消**：支持中止正在进行的请求

## 安装和导入

```javascript
import StreamRequestUtil from 'src/utils/streamRequest.js';
```

## 核心方法

### 1. `createRequest(url, requestData, callbacks, options)`

创建基础的流式请求。

**参数：**
- `url` (string): 完整的请求URL
- `requestData` (Object): 请求体数据
- `callbacks` (Object): 回调函数集合
  - `onMessageCallback` (function): 接收流式数据的回调
  - `onCompleteCallback` (function): 请求完成时的回调
  - `onErrorCallback` (function): 错误处理回调
- `options` (Object): 可选配置
  - `method` (string): HTTP方法，默认'POST'
  - `headers` (Object): 额外的请求头
  - `useAuth` (boolean): 是否使用认证，默认true

**返回值：**
- `Promise<AbortController>`: 用于取消请求的控制器

**示例：**
```javascript
const controller = await StreamRequestUtil.createRequest(
  'https://api.example.com/stream',
  { message: 'Hello' },
  {
    onMessageCallback: (data) => console.log('收到数据:', data),
    onCompleteCallback: () => console.log('完成'),
    onErrorCallback: (error) => console.error('错误:', error)
  },
  {
    method: 'POST',
    headers: { 'X-Custom': 'value' },
    useAuth: true
  }
);
```

### 2. `createRequestWithBaseUrl(baseUrl, endpoint, requestData, callbacks, options)`

使用基础URL和端点创建请求。

**示例：**
```javascript
const controller = await StreamRequestUtil.createRequestWithBaseUrl(
  'https://api.example.com',
  '/chat/stream',
  { message: 'Hello' },
  callbacks
);
```

### 3. `createRequestWithRetry(url, requestData, callbacks, options)`

创建带重试机制的流式请求。

**额外选项：**
- `maxRetries` (number): 最大重试次数，默认3
- `retryDelay` (number): 重试延迟时间(ms)，默认1000

**示例：**
```javascript
const controller = await StreamRequestUtil.createRequestWithRetry(
  'https://api.example.com/stream',
  { message: 'Hello' },
  callbacks,
  {
    maxRetries: 5,
    retryDelay: 2000
  }
);
```

### 4. `createBatchRequests(requests, globalCallbacks)`

批量创建多个流式请求。

**参数：**
- `requests` (Array): 请求配置数组
- `globalCallbacks` (Object): 全局回调函数

**示例：**
```javascript
const requests = [
  {
    url: 'https://api.example.com/stream1',
    requestData: { id: 1 },
    callbacks: { onMessageCallback: (data) => console.log('请求1:', data) }
  },
  {
    url: 'https://api.example.com/stream2',
    requestData: { id: 2 },
    callbacks: { onMessageCallback: (data) => console.log('请求2:', data) }
  }
];

const controllers = await StreamRequestUtil.createBatchRequests(
  requests,
  {
    onCompleteCallback: () => console.log('所有请求完成'),
    onErrorCallback: (error) => console.error('批量请求错误:', error)
  }
);
```

### 5. `abortAllRequests(controllers)`

取消所有请求。

**示例：**
```javascript
StreamRequestUtil.abortAllRequests(controllers);
```

## 在Service中使用

### 基础Service示例

```javascript
import StreamRequestUtil from 'src/utils/streamRequest.js';

const myService = {
  // 基础流式请求
  streamData: async (data, callbacks) => {
    const url = 'https://api.example.com/stream';
    return StreamRequestUtil.createRequest(url, data, callbacks);
  },

  // 使用基础URL
  streamWithBaseUrl: async (endpoint, data, callbacks) => {
    return StreamRequestUtil.createRequestWithBaseUrl(
      'https://api.example.com',
      endpoint,
      data,
      callbacks
    );
  },

  // 带重试机制
  streamWithRetry: async (data, callbacks) => {
    const url = 'https://api.example.com/stream';
    return StreamRequestUtil.createRequestWithRetry(
      url,
      data,
      callbacks,
      { maxRetries: 3, retryDelay: 1000 }
    );
  }
};
```

### 高级Service示例

```javascript
import StreamRequestUtil from 'src/utils/streamRequest.js';

const advancedService = {
  // 自定义数据处理
  processDataStream: async (data, callbacks) => {
    const enhancedCallbacks = {
      ...callbacks,
      onMessageCallback: (rawData) => {
        try {
          const processedData = JSON.parse(rawData);
          if (callbacks.onMessageCallback) {
            callbacks.onMessageCallback(processedData);
          }
        } catch (error) {
          console.warn('数据解析失败，使用原始数据:', rawData);
          if (callbacks.onMessageCallback) {
            callbacks.onMessageCallback(rawData);
          }
        }
      }
    };

    return StreamRequestUtil.createRequest(
      'https://api.example.com/process',
      data,
      enhancedCallbacks
    );
  },

  // 批量处理
  batchProcess: async (items, globalCallbacks) => {
    const requests = items.map(item => ({
      url: `https://api.example.com/process/${item.id}`,
      requestData: item,
      callbacks: {
        onMessageCallback: (data) => {
          console.log(`处理项目 ${item.id}:`, data);
        }
      }
    }));

    return StreamRequestUtil.createBatchRequests(requests, globalCallbacks);
  },

  // 无认证请求
  publicStream: async (data, callbacks) => {
    return StreamRequestUtil.createRequest(
      'https://api.example.com/public/stream',
      data,
      callbacks,
      { useAuth: false }
    );
  }
};
```

## React组件中使用

```javascript
import React, { useState, useRef } from 'react';
import myService from './myService.js';

const StreamComponent = () => {
  const [data, setData] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const controllerRef = useRef(null);

  const handleStartStream = async () => {
    setIsLoading(true);
    setData('');

    const callbacks = {
      onMessageCallback: (chunk) => {
        setData(prev => prev + chunk);
      },
      onCompleteCallback: () => {
        setIsLoading(false);
        console.log('流式请求完成');
      },
      onErrorCallback: (error) => {
        setIsLoading(false);
        console.error('流式请求失败:', error);
      }
    };

    try {
      controllerRef.current = await myService.streamData(
        { message: 'Hello' },
        callbacks
      );
    } catch (error) {
      setIsLoading(false);
      console.error('启动流式请求失败:', error);
    }
  };

  const handleStop = () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
      setIsLoading(false);
    }
  };

  return (
    <div>
      <button onClick={handleStartStream} disabled={isLoading}>
        开始流式请求
      </button>
      {isLoading && (
        <button onClick={handleStop}>停止</button>
      )}
      <div>{data}</div>
    </div>
  );
};
```

## 错误处理

工具类提供了完善的错误处理机制：

1. **网络错误**：自动捕获HTTP错误并提供详细信息
2. **流处理错误**：区分中止错误和其他类型错误
3. **回调错误**：捕获回调函数中的错误并记录
4. **重试机制**：自动重试失败的请求（可配置）

## 最佳实践

### 1. 错误处理
```javascript
const callbacks = {
  onMessageCallback: (data) => {
    // 处理数据
  },
  onCompleteCallback: () => {
    // 完成处理
  },
  onErrorCallback: (error) => {
    // 根据错误类型进行不同处理
    if (error.name === 'AbortError') {
      console.log('用户取消了请求');
    } else {
      console.error('请求失败:', error.message);
      // 显示用户友好的错误信息
    }
  }
};
```

### 2. 内存管理
```javascript
// 在组件卸载时取消请求
useEffect(() => {
  return () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }
  };
}, []);
```

### 3. 性能优化
```javascript
// 对于大量数据，考虑节流处理
const throttledCallback = useCallback(
  throttle((data) => {
    setData(prev => prev + data);
  }, 100),
  []
);
```

## 配置选项

### 认证配置
```javascript
// 使用认证（默认）
const controller = await StreamRequestUtil.createRequest(url, data, callbacks, {
  useAuth: true
});

// 不使用认证
const controller = await StreamRequestUtil.createRequest(url, data, callbacks, {
  useAuth: false
});
```

### 自定义请求头
```javascript
const controller = await StreamRequestUtil.createRequest(url, data, callbacks, {
  headers: {
    'X-Custom-Header': 'value',
    'Content-Language': 'zh-CN'
  }
});
```

### 重试配置
```javascript
const controller = await StreamRequestUtil.createRequestWithRetry(url, data, callbacks, {
  maxRetries: 5,        // 最大重试次数
  retryDelay: 2000,     // 重试延迟(ms)
  method: 'POST',       // HTTP方法
  useAuth: true         // 是否使用认证
});
```

## 注意事项

1. **内存泄漏**：长时间运行的流式请求要及时取消
2. **错误处理**：始终提供 `onErrorCallback` 处理错误情况
3. **性能考虑**：大量数据流时考虑使用节流或防抖
4. **网络状况**：在网络不稳定时使用重试机制
5. **用户体验**：提供取消按钮让用户能够中止请求 
