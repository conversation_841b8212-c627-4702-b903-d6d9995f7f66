.unusualContentWrap {
  display: flex;
  height: 100%;
  .unusualSkeletonWrap {
    width: 240px;
    height: 100%;
    background-color: #fff;
  }
  .unusualContent {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    margin: 15px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);
    .fixAccount:hover {
      color: #2196f3 !important;
    }
  }
  .unusualScrollContent {
    width: 500px;
    max-height: 180px;
    overflow-y: auto;
    text-align: center;
  }
  .applyContent {
    .manager {
      margin-left: -10px;
      img {
        border: 2px solid #fff;
      }
    }
  }
  .imgWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130px;
    height: 130px;
    line-height: 130px;
    border-radius: 50%;
    text-align: center;
    background-color: #f5f5f5;
    img {
      width: 100%;
    }
  }
  .explainText {
    margin: 30px 0 50px 0;
    font-size: 17px;
    color: #757575;
  }
}
