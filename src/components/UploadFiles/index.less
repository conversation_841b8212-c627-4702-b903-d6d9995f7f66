.UploadFiles {
  &-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
  }
  &-arrow {
    width: 0;
    height: 0;
    border: 9px solid;
    border-color: transparent transparent #f5f5f5 transparent;
    position: absolute;
    top: -8px;
    left: 0;
  }
  &-exhibition {
    .UploadFiles-header {
      display: none;
    }
  }

  /* 上传按钮 start */
  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 6px;
    background-color: #f5f5f5;
  }
  &-entrys {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    > div {
      cursor: pointer;
      color: #9e9e9e;
      margin-right: 15px;
      span {
        padding-left: 5px;
      }
      &:hover {
        color: #757575;
        i {
          color: #757575 !important; 
        }
      }
    }
  }
  &-ramSize {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
  &-attachmentProgress {
    border-radius: 5px;
    height: 5px;
    flex-basis: 100px;
    margin-right: 10px;
    background-color: #e0e0e0;
    position: relative;
  }
  &-currentProgress {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 5px;
    height: 100%;
    width: 100%;
    max-width: 100%;
  }
  &-info {
    color: #999;
    font-size: 12px;
  }
  /* 上传按钮 end */

  /* 图片列表 start */
  &-filesWrapper {
    margin-right: -6px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &.rowDisplay {
      padding-bottom: 5px;
      margin-right: 0;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
    }
  }
  &-file-wrapper {
    flex: 1;
    display: inline-block;
    box-sizing: border-box;
    margin: 0 6px 6px 0;
    min-width: 150px;
    max-width: 200px;
    height: 118px;
  }
  &-file {
    height: 100%;
    border: 1px solid #e0e0e0;
    box-sizing: border-box;
    position: relative;
  }
  &-fileEmpty {
    height: 0 !important;
    margin-top: 0;
    margin-bottom: 0;
  }
  &-fileImage {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }
  &-fileAccessory {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  &-fileIconWrapper {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    div.UploadFiles-fileIcon {
      width: 40px;
      height: 55px;
      background-position: 50%;
      background-size: contain !important;
    }
    .linkThumbnailCon {
      .fileIcon-link {
        position: absolute;
        right: 6px;
        top: 6px;
        width: 25px;
        height: 28px;
      }
      .linkThumbnail {
        display: flex;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
        img {
          max-width: 140px;
          max-height: 70px;
        }
      }
    }
  }
  &-fileName {
    font-size: 13px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    >span {
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      /* autoprefixer: on */
    }
  }
  &-forbidFileName {
    font-size: 13px;
    color: #9e9e9e;
    width: 100%;
    text-align: left;
  }
  &-kcIcon {
    font-size: 12px;
    color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    width: 16px;
    height: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &-video-kcIcon {
    font-size: 12px;
    color: #fff;
    position: absolute;
    left: 4px;
    top: -1px;
  }
  &-video {
    font-size: 12px;
    position: absolute;
    left: 0;
    bottom: 0;
    color: #fff;
    padding: 1px 8px;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(70, 70, 70, 0.3));
    i {
      padding-right: 5px;
      font-size: 16px;
    }
  }
  &-kcFileName {
    font-size: 12px;
    color: #bdbdbd;
    position: relative;
    top: -2px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
    span:nth-child(1) {
      margin-right: 10px;
    }
  }
  &-filePanel {
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding: 12px;
    opacity: 0;
    transition: opacity 0.2s;
    cursor: pointer;
    &:hover {
      /*opacity: 1;*/
    }
    &-image {
      background-color: rgba(0, 0, 0, 0.6);
      .UploadFiles-panelTextName {
        color: #fff;
      }
    }
    &-accessory {
      background-color: rgba(245, 245, 245, 1);
    }
    &-confirm {
      .delete {
        color: #fff;
        background-color: #f44336;
        margin-right: 10px;
      }
      .cancel {
        background-color: #fff;
      }
      .delete, .cancel{
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
        flex: 1;
        border-radius: 4px;
        padding: 3px 0;
      }
    }
  }
  &-panelText {
    flex: 1;
  }
  &-editInput {
    width: 100%;
    font-size: 13px;
    display: flex;
    height: 30px;
    border: 1px solid #ccc;
    padding: 0 10px;
    border-radius: 4px;
    box-sizing: border-box;
  }
  &-panelTextName {
    font-size: 12px;
    flex-basis: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-weight: bold;
    span:nth-child(1) {
      /*max-height: 36px;*/
      line-height: 14px;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      /* autoprefixer: on */
    }
  }
  &-panelBtns {
    display: flex;

    > div:nth-child(1),
    > div:nth-child(2) {
      display: flex;
      height: 100%;
      align-items: flex-start;
    }
    > div:nth-child(1) {
      flex: 1;
    }
    > div:nth-child(2) {
      flex: 2;
      justify-content: flex-end;
      div {
        margin-right: 5px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  &-panelBtn {
    width: 32px;
    height: 24px;
    border-radius: 2px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    cursor: pointer;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    position: relative;
    &-delete:hover i {
      color: red;
    }
    > i {
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 24px;
      color: #9e9e9e;
      font-size: 17px;
    }
  }
  &-previewIcon {
    position: absolute;
    top: 8px;
    right: 5px;
    width: 23px;
    height: 23px;
    background-size: contain !important;
  }
  &-previewFileName {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 5px 10px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
  }
  &-panelBtnMask {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  &-mask {
    width: 100%;
    height: 100%;
    background-color: #f3f4f4;
  }
  &-loadfileWrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    .Progress--circle-content {
      font-size: 12px !important;
    }
    > div:nth-child(1) {
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    > div:nth-child(2) {
      padding: 0 10px;
      flex-basis: 30px;
      background-color: #fff;
    }
  }
  &-loadfileClose {
    position: absolute !important;
    right: 5px;
    top: 5px;
    width: 23px;
    height: 23px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    cursor: pointer;
    i {
      display: flex;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
      color: #757575;
      font-size: 18px;
    }
  }
  &-menuWrapper {
    .Item:hover {
      .icon,
      span {
        color: #fff;
      }
    }
    .icon {
      color: #9e9e9e;
      font-size: 16px;
    }
    &-text {
      margin-left: 20px;
      color: #151515;
    }
  }
  /* 图片列表 end */
}
