﻿<div class="createCalendarBox createCalendarTitle">
    <div class="createCalendarLabel">{{= _l('日程标题')}}</div>
    <input
        type="text"
        id="txtCalendarName"
        placeholder="{{= _l('请输入日程标题…')}}"
        class="box-sizing boderRadAll_3 ThemeBorderColor3 Font14 infoInput"
    />
    <div
        class="calendarColorModel boderRadAll_3 calendarColorBlue"
        title="{{=_l('工作日程')}}"
        data-catid="1"
        id="createCategoryID"
    >
        <span class="icon-arrow-down-border"></span>
    </div>
    <ul class="calendarColorMain boderRadAll_3 Font14" id="calendarColorMain"></ul>
</div>
<div class="createCalendarBox calendarTime Font14">
    <div class="createCalendarLabel">{{= _l('起止时间')}}</div>
    <div class="w100" id="calendarDate"></div>
    <span class="timezone boderRadAll_3 Font14 ThemeBorderColor3 ThemeColor3">{{= _l('时区')}}</span>
</div>
<div id="selectTimezone"></div>
<div class="allDay pointer">
    <label for="allDay">
        <input id="allDay" type="checkbox" />
        <span class="pointer">{{= _l("全天日程")}}</span>
    </label>
</div>
<div class="createCalendarBox calendarRemind Font14">
    <div class="createCalendarLabel">{{= _l('提醒%19000')}}</div>
    <span class="remindTextLable" id="remindTextLableCreate">{{= _l('提前%19001') }}</span>
    <input type="text" id="remindTextCreate" class="remindText Font14 pointer ThemeColor3" value="15" />
    <input type="hidden" id="remindSelectCreate" />
    <span class="InlineBlock" id="remindSelectCreateBox" style="margin-top: -5px; margin-left: -12px"></span>
    <div class="telRemindLabel InlineBlock mLeft25 Gray_9e">
        <label class="Hand"> <span class="chekboxIcon"></span> {{= _l('电话提醒')}} </label>
        <span class="iconHelp" data-tip="{{= _l('勾选电话提醒，将以电话语音形式通知日程参与人员') }}">
            <i class="icon-help"></i>
        </span>
    </div>
</div>
<div class="createCalendarBox Font14 Hidden" id="repeatContent">
    <div class="createCalendarLabel"><span class="chekboxIcon"></span>{{= _l('重复')}}</div>
    <div id="noRepeatContent" class="Hidden">...</div>
    <div id="existRepeatContent">
        <span id="updateRepeatBtn"
            ><span class="repeatResultText"></span><span class="ThemeColor3 repeatOperate">修改</span></span
        >
    </div>
</div>
<div class="createCalendarBox inputBox Font14" id="addCalendarMembers">
    <div class="createCalendarLabel">{{= _l('出席者')}}</div>
    <div class="createAddMemberBox">
        <i class="icon-task-add-member-circle createAddMember ThemeColor3"></i>
    </div>
</div>
<div class="createCalendarBox inputBox Font14 Hidden" id="addressContent">
    <div class="createCalendarLabel">{{= _l('会议地点')}}</div>
    <input
        type="text"
        id="txtAddress"
        class="txtAddress box-sizing boderRadAll_3 ThemeBorderColor3 infoInput"
        value=""
        placeholder="{{= _l('添加会议地点…')}}"
    />
</div>
<div class="createCalendarBox inputBox Font14" id="createCalendarDesc">
    <div class="createCalendarLabel">{{= _l('描述和附件')}}</div>
    <textarea
        type="text"
        id="txtDesc"
        class="createCalendarDesc box-sizing boderRadAll_3 ThemeBorderColor3 infoInput"
        value=""
        placeholder="{{= _l('填写描述')}}"
    ></textarea>
    <div id="createCalendarAttachment_updater"></div>
</div>
<div class="createCalendarBox calendarTabs Font14" id="calendarTabs">
    <span data-type="address" class="ThemeBorderColor3 ThemeColor3">{{= _l('会议地点')}}</span>
    <span data-type="repeat" class="ThemeBorderColor3 ThemeColor3 InlineBlock">{{= _l('重复')}}</span>
</div>
<div class="createCalendarBox Font14 calendarPrivate" id="calendarPrivate">
    <label class="pointer isPrivateLabel"> <span class="chekboxIcon"></span> {{= _l('私密日程')}} </label>
    <span class="iconHelp" data-tip="日程内容默认公开给所有同事，勾选私密日程，将仅对参与人员公开。">
        <i class="icon-help"></i>
    </span>
</div>
<div class="box-sizing createCalendarOperator">
    <span class="boderRadAll_3 ThemeBGColor3" id="calendarSubmitBtn">{{= _l('创建')}}</span>
</div>

<div class="repeatDialog Hidden" id="repeatDialog">
    <ul class="repeatDialogMain">
        <li class="box-sizing">
            <div class="createCalendarLabel">{{= _l('重复类型')}}</div>
            <input type="hidden" id="tab_repeatType" value="1" />
            <span class="InlineBlock" id="tab_repeatTypeBox" style="margin-top: -5px; margin-left: -12px"></span>
        </li>
        <li class="box-sizing">
            <div class="createCalendarLabel">{{= _l('重复频率')}}</div>
            <span class="Font14">{{=_l('每') }}</span>
            <input id="repetitionFrequency" defaultvalue="1" value="1" type="text" class="boderRadAll_3" />
            <span id="createRepeatTypeLabel" class="Font14">{{=_l('周')}}</span>
            <span class="repeatTypeGroup" id="repeatTypeGroup">
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="64"
                    index="7"
                    type="button"
                    value="{{=it.moment().day(0).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="1"
                    index="1"
                    type="button"
                    value="{{=it.moment().day(1).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="2"
                    index="2"
                    type="button"
                    value="{{=it.moment().day(2).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="4"
                    index="3"
                    type="button"
                    value="{{=it.moment().day(3).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="8"
                    index="4"
                    type="button"
                    value="{{=it.moment().day(4).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="16"
                    index="5"
                    type="button"
                    value="{{=it.moment().day(5).format('dd')}}"
                />
                <input
                    class="repeatTypeGroupBtn pointer"
                    week="32"
                    index="6"
                    type="button"
                    value="{{=it.moment().day(6).format('dd')}}"
                />
            </span>
        </li>
        <li class="box-sizing">
            <div class="createCalendarLabel">{{= _l('结束日期')}}</div>
            <input type="hidden" id="tab_repeatTime" value="0" />
            <span class="InlineBlock" id="tab_repeatTimeBox" style="margin-top: -5px; margin-left: -12px"></span>
            <span id="overCount" class="Font14">
                {{=_l('发生')}}
                <input class="txtOverCount boderRadAll_3" defaultvalue="1" value="1" type="text" id="txtOverCount" />
                {{=_l('次后')}}
            </span>
            <span id="createCalendarOverTime"></span>
        </li>
        <li class="box-sizing">
            <div class="createCalendarLabel">{{= _l('结果')}}</div>
            <div id="repeatReault" class="Font14"></div>
        </li>

    </ul>
</div>
