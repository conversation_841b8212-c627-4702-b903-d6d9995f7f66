﻿.createShareDialog .qrCode {
  margin: 20px auto 0;
  text-align: center;
}

.createShareDialog .qrCode img {
  height: 150px;
}

.createShareDesc,
.createShareCopy {
  width: 100%;
  text-align: center;
  margin-top: 20px;
  position: relative;
}
.createShareCopy span {
  cursor: pointer;
  color: #888;
}
.createShareCopy span i {
  background: url('../images/bg.png') no-repeat;
  background-size: 100px 130px;
  width: 32px;
  height: 32px;
  background-position: -40px 0;
  display: inline-block;
  vertical-align: top;
  margin-top: -6px;
  margin-right: 5px;
}
.createShareCopy .clipboardswf {
  left: 50%;
  margin-left: -75px;
  top: -5px;
  position: absolute;
}
.createSharePadding {
  padding-bottom: 40px;
}
.createShareDialog {
  margin-bottom: 0;
}
.createShareDialog .noHeader {
  margin-right: 0;
}
.createShareTextRight {
  text-align: right;
}
.createShareLink {
  margin-left: 35px;
}
.createShareDialog .shareOperator {
  width: 100%;
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
.createShareDialog .shareBtn {
  margin-right: 24px;
  font-size: 13px;
  cursor: pointer;
}
.createShareDialog .shareBtn.cancelStyle {
  color: #4caf50 !important;
}
.noShare {
  width: 100%;
  text-align: center;
  margin-top: 48px;
}
.noShare i {
  display: inline-block;
  width: 93px;
  height: 93px;
  background: url('../images/bg.png') no-repeat;
  background-size: 100px 130px;
  background-position: 0 -33px;
}
.noShareContent {
  width: 100%;
  text-align: center;
  margin-top: 20px;
  line-height: 30px;
  padding-bottom: 35px;
}
.noShareContent.noShareContentP {
  padding-bottom: 70px;
}

.shareBtnClose {
  color: #f44336 !important;
}
#createShareAlert {
  border-radius: 5px;
  z-index: 9999;
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 12px 20px 10px;
  width: 290px;
  background-color: #fff;
  border-top-width: 8px;
  border-top-style: solid;
  -moz-box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2), 0 2px 12px rgba(0, 0, 0, 0.2);
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2), 0 2px 12px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2), 0 2px 12px rgba(0, 0, 0, 0.2);
}
.createShareAlertTitle {
  font-size: 16px;
  margin-bottom: 7px;
}
.createShareAlertTitle .icon-plus-interest {
  font-size: 22px;
  vertical-align: top;
  display: inline-block;
  color: #4caf50;
  margin-right: 10px;
}
#createShareAlert .inviteBtn {
  margin-left: 15px;
  cursor: pointer;
}
#createShareAlert .inviteBtn:hover,
#createShareAlert .createShareLink:hover {
  text-decoration: underline;
}
.createShareAlertClose {
  font-size: 24px;
  position: absolute;
  right: 10px;
  top: 0;
  cursor: pointer;
}
.createShareAlertClose:not(:hover) {
  color: #151515 !important;
}
#createShareAlert.ns-hide {
  -webkit-animation-direction: reverse;
  animation-direction: reverse;
}
#createShareAlert.ns-show,
#createShareAlert.ns-hide {
  -webkit-animation-name: animGenie;
  animation-name: animGenie;
  -webkit-animation-duration: 0.4s;
  animation-duration: 0.4s;
}
@-webkit-keyframes animGenie {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, calc(200% + 30px), 0) scale3d(0, 1, 1);
    -webkit-animation-timing-function: ease-in;
  }
  40% {
    opacity: 0.5;
    -webkit-transform: translate3d(0, 0, 0) scale3d(0.02, 1.1, 1);
    -webkit-animation-timing-function: ease-out;
  }
  70% {
    opacity: 0.6;
    -webkit-transform: translate3d(0, -40px, 0) scale3d(0.8, 1.1, 1);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
  }
}

@keyframes animGenie {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, calc(200% + 30px), 0) scale3d(0, 1, 1);
    -webkit-animation-timing-function: ease-in;
    transform: translate3d(0, calc(200% + 30px), 0) scale3d(0, 1, 1);
    animation-timing-function: ease-in;
  }
  40% {
    opacity: 0.5;
    -webkit-transform: translate3d(0, 0, 0) scale3d(0.02, 1.1, 1);
    -webkit-animation-timing-function: ease-out;
    transform: translate3d(0, 0, 0) scale3d(0.02, 1.1, 1);
    animation-timing-function: ease-out;
  }
  70% {
    opacity: 0.6;
    -webkit-transform: translate3d(0, -40px, 0) scale3d(0.8, 1.1, 1);
    transform: translate3d(0, -40px, 0) scale3d(0.8, 1.1, 1);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
  }
}
