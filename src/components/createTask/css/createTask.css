#createTask.dialogBox .header {
  margin-left: 26px;
}
#createTask.dialogBox .dialogContent {
  padding: 0;
  padding-bottom: 35px;
}
.p82 {
  padding: 0 82px 0 116px;
}
.pLeft26 {
  padding-left: 26px;
}
.pRight26 {
  padding-right: 26px;
}
.spanChecked,
.spanUnChecked {
  background: url('../images/checkBg.png') no-repeat;
  height: 17px;
  display: inline-block;
  cursor: pointer;
  padding-left: 25px;
  color: #aaa;
  background-position: 0 1px;
  background-size: 16px;
}
.spanChecked {
  background-position: 0 -18px;
}
.boxShadow5 {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.13);
}
.createTaskLabel {
  color: #9e9e9e;
  font-size: 14px;
  text-align: right;
  position: absolute;
  top: 0;
  left: 0;
  width: 96px;
  height: 20px;
  line-height: 20px;
}
.topHeight {
  height: 20px;
}
.createTaskNetwork {
  height: 20px;
  line-height: 20px;
  cursor: pointer;
  width: 160px;
}
.createTaskNetwork .createTaskNetworkName {
  max-width: 140px;
  height: 20px;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}
.createTaskNetwork .icon-arrow-down-border {
  display: inline-block;
  font-size: 12px;
  color: #9e9e9e;
  vertical-align: top;
  margin-left: 5px;
  margin-top: 5px;
}
.createTaskNetworkList {
  position: absolute;
  top: 22px;
  left: 116px;
  background: #fff;
  max-height: 165px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 100;
}
.createTaskNetworkList li {
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  width: 222px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.createTaskNetworkList li i {
  font-size: 14px;
  width: 20px;
  display: inline-block;
}
.createTaskNetworkList li i.icon-charger {
  vertical-align: top;
  margin-top: 10px;
}
.createTaskNetworkList li:not(:hover) {
  background-color: #fff !important;
}
.createTaskNetworkList li:hover {
  color: #fff;
}
.createTaskNetworkList li:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.createTaskNetworkList li:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.createTaskFolder {
  position: absolute;
  top: -9px;
  left: 295px;
  line-height: 38px;
  vertical-align: top;
}
.createTaskFolder.createUpdatePosition {
  left: 69px;
}
.createTaskFolder.createUpdatePosition .createTaskFolderName {
  margin-left: 14px;
  width: 428px;
}
.createTaskFolder.createUpdatePosition .txtTaskFolder {
  margin-left: 15px;
  width: 428px;
}
.createTaskFolder.createUpdatePosition .linkageFolder {
  left: 47px;
}
.createTaskFolder.createUpdatePosition .folderListName {
  max-width: 330px;
}
.createTaskFolderColor {
  color: #9e9e9e;
}
.createTaskFolderName {
  width: 181px;
  margin-left: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
}
.txtTaskFolder {
  height: 37px;
  padding: 8px 5px;
  width: 207px;
  margin-left: 10px;
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
}
.linkageFolder {
  position: absolute;
  width: 260px;
  background-color: #fff;
  left: 42px;
  top: 40px;
  z-index: 10;
  padding: 5px 0;
}
.linkageFolder ul {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 220px;
}
.createTaskFolder.createUpdatePosition .linkageFolder {
  width: 428px;
}
.linkageFolder li {
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  display: block;
  cursor: pointer;
  position: relative;
}
.linkageFolder li.hover,
.linkageFolder .nullFolder:hover {
  color: #fff;
}
.linkageFolder li:not(.hover),
.linkageFolder .nullFolder:not(:hover) {
  background-color: #fff !important;
}
.linkageFolder .chargeUser {
  width: 26px;
  height: 26px;
  margin-top: 7px;
  vertical-align: top;
}

.linkageFolder .folderListName {
  line-height: 40px;
  margin-left: 5px;
  vertical-align: top;
  max-width: 160px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.linkageFolder .icon-folder-private,
.linkageFolder .icon-folder-public {
  font-size: 16px;
  margin-left: 5px;
  display: inline-block;
  vertical-align: top;
  margin-top: 12px;
}

.linkageFolder .folderMemberCount {
  position: absolute;
  right: 5px;
  top: 0;
  line-height: 40px;
  font-size: 14px;
  color: #999;
}
.linkageFolder li.hover .ThemeColor8 {
  color: #fff !important;
}
.linkageFolder li.hover .folderMemberCount {
  color: #fff;
}
.linkageFolder .nullFolder {
  padding-left: 10px;
  line-height: 42px;
  height: 42px;
  border-top: 1px solid #ddd;
  cursor: pointer;
}
.linkageFolder .nullFolder.clearBorder {
  border-top-width: 0;
}
.linkageFolder .nullFolder .folderListName {
  line-height: 42px;
  height: 42px;
  margin-left: 0;
}
.createTaskTitle {
  margin-top: 22px;
}
#txtTaskName {
  height: 38px;
  width: 100%;
  padding: 8px;
  border-width: 1px;
  border-style: solid;
}
#txtTaskName:not(:focus),
#txtDescriptionbox:not(:focus) {
  border-color: #bdbdbd !important;
}
#txtTaskName:hover:not(:focus),
#txtDescriptionbox:hover:not(:focus) {
  border-color: #9e9e9e !important;
}
.createTaskTitle .createTaskLabel {
  top: 9px;
}
.taskUpdateChargeMain {
  margin-top: 22px;
}
.taskUpdateChargeMain .createTaskLabel {
  top: 6px;
}
#taskUserBox {
  width: 32px;
  height: 32px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  margin-right: 6px;
}
.imgWidth {
  width: 32px;
  height: 32px;
}
.taskUpdateCharge {
  font-size: 32px;
}
.taskUpdateCharge:not(:hover) .ThemeColor3 {
  color: #151515 !important;
}
.taskTabs {
  margin-top: 20px;
}
.taskTabs span {
  border-bottom: 1px solid #9e9e9e;
  margin-right: 36px;
  cursor: pointer;
}
.taskTabs span:not(:hover) {
  border-color: #9e9e9e !important;
  color: #9e9e9e !important;
}
#createTaskDesc {
  margin-top: 20px;
}
#createTaskDesc .createTaskLabel {
  top: 10px;
}
.txtDescriptionbox {
  width: 100%;
  border: 1px solid #9e9e9e;
  padding: 7px;
  line-height: 24px;
  resize: none;
}
.addAttachment {
  margin-top: 10px;
  display: inline-block;
  cursor: pointer;
}
#Attachment_updater_createTask {
  margin-top: 10px;
  font-size: 13px;
}
#Attachment_updater_createTask .uploadDocList,
#Attachment_updater_createTask .uploadPicList {
  margin-left: 0;
}
#taskMembersBox {
  margin-top: 22px;
}
#taskMembersBox .createTaskLabel {
  top: 6px;
}
.createTaskAddMemberBox .imgMemberBox .imgCharge,
.createTaskAddMemberBox .imgMemberBox {
  width: 32px;
  height: 32px;
}
.createTaskAddMemberBox .imgMemberBox {
  display: inline-block;
  vertical-align: top;
  margin-right: 8px;
  margin-bottom: 8px;
}
.createTaskAddMemberBox .removeTaskMember {
  cursor: pointer;
  display: none;
  vertical-align: top;
  width: 32px;
  height: 32px;
  background-color: #666;
  position: absolute;
  margin-right: -32px;
  text-align: center;
  color: #fff;
  font-size: 16px;
  opacity: 0.7;
}
.createTaskAddMemberBox .imgMemberBox:hover .removeTaskMember {
  display: inline-block !important;
}
.createTaskAddMemberBox .removeTaskMember .Icon {
  display: inline-block;
  vertical-align: top;
  height: 7px;
  margin-top: 10px;
}
.createTaskAddMember {
  font-size: 32px;
  display: inline-block;
  height: 32px;
  vertical-align: top;
  cursor: pointer;
  margin-bottom: 11px;
}
.taskUpdateCharge:not(:hover),
.createTaskAddMember:not(:hover) {
  color: #9e9e9e !important;
}
#createTaskDate {
  margin-top: 18px;
}
#createTaskDate .createTaskLabel {
  top: 10px;
}
#txtLastDate {
  width: 100%;
  border-width: 1px;
  border-style: solid;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  cursor: pointer;
}
#txtLastDate span {
  color: #151515 !important;
}
#txtLastDate:not(:hover) {
  border-color: #949494 !important;
}
#createTaskStage {
  margin-top: 16px;
}
#createTaskStage .createTaskLabel {
  top: 10px;
}
#createTaskStage .customSelect {
  width: 100%;
}
#createTaskStage .spanShow {
  width: 100%;
  box-sizing: border-box;
  height: 40px !important;
  line-height: 40px !important;
  border-color: #9e9e9e;
  padding-right: 30px;
}
#createTaskStage .spanShow .txtBox {
  height: 40px !important;
  line-height: 38px !important;
}
#createTaskStage .spanShow:hover {
  border-color: #9e9e9e;
}
#createTaskStage .spanShow .icon-arrow-down-border {
  position: absolute;
  top: 4px;
  right: 10px;
  font-size: 14px;
}
#createTaskStage .csList {
  width: 100%;
  top: 42px !important;
}
#createTaskStage .csList li {
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.createTaskOperator {
  margin-top: 40px;
  font-size: 13px;
  color: #9e9e9e;
  padding-top: 11px;
  height: 30px;
}
#chToFeed {
  cursor: pointer;
}
.createTaskOperator .spanUnChecked {
  display: inline-block;
  vertical-align: top;
}
#createTaskView .viewTo {
  float: none;
  max-width: 200px;
  display: inline-block;
  vertical-align: top;
  margin-top: -5px;
}
#createTaskView .updaterFor {
  max-width: 160px;
  word-wrap: break-word;
  word-break: break-all;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#taskSubmitBtn {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 26px;
  height: 40px;
  line-height: 40px;
  font-size: 15px;
  padding: 0 35px;
  color: #fff;
  cursor: pointer;
}

#createTask .UploadFiles-attachmentProgress {
  display: none;
}

.createTaskConfirm .mui-dialog-scroll-container > .mui-dialog-dialog > .mui-dialog-body {
  padding: 0 0 38px !important;
}

#folderStageBox .Dropdown--border {
  height: 38px;
  border-color: #bdbdbd;
}

#folderStageBox .Dropdown--border:hover {
  border-color: #1e88e5;
}
