﻿<div class="boxSizing p82 pRight26 relative Font14 mTop10 topHeight">
    {{?md.global.Account.projects.length}}
    <div class="createTaskLabel">{{=_l('归属')}}</div>
    <div class="createTaskNetwork" id="createTaskNetwork">
        <span class="createTaskNetworkName">{{!it.companyName}}</span><i class="icon-arrow-down-border"></i>
    </div>
    <ul class="createTaskNetworkList boxShadow5 boderRadAll_3 Hidden" id="createTaskNetworkList">
        {{~md.global.Account.projects:project}}
        <li class="ThemeBGColor3" data-id="{{=project.projectId}}">
            <i class="icon-company"></i>{{!project.companyName}}
        </li>
        {{~}}
        <li class="ThemeBGColor3" data-id=""><i class="icon-charger"></i>{{=_l('个人')}}</li>
    </ul>
    {{?}}
    <div class="createTaskFolder {{=md.global.Account.projects.length ? '' : 'createUpdatePosition'}}">
        <span class="createTaskFolderColor">{{=_l('项目')}}</span>
        <span class="createTaskFolderName">{{!it.folderName ? it.folderName : '...'}}</span>
        <input
            type="text"
            value="{{=it.folderName ? it.folderName : ''}}"
            class="txtTaskFolder boderRadAll_3 Hidden Font14 ThemeBorderColor3"
            id="txtTaskFolder"
        />
        <div class="linkageFolder boderRadAll_3 boxShadow5 Hidden">
            <ul></ul>
            <div class="nullFolder Hidden ThemeColor3" data-folderid="">
                {{=_l('创建“%0”项目', '<span class="folderListName"></span>')}}
            </div>
        </div>
    </div>
</div>
<div class="boxSizing p82 pRight26 relative Font14 createTaskTitle">
    <div class="createTaskLabel">{{=_l('名称')}}</div>
    <input
        type="text"
        id="txtTaskName"
        maxlength="100"
        placeholder="{{=_l('请输入任务名称') }}"
        spellcheck="false"
        class="boxSizing boderRadAll_3 ThemeBorderColor3 Font14"
        value="{{!it.TaskName }}"
    />
</div>
<div class="boxSizing p82 pRight26 relative Font14 taskUpdateChargeMain">
    <div class="createTaskLabel">{{=_l('负责人')}}</div>
    {{var charge = it.ChargeArray[0]; }}
    <span id="taskUserBox" class="circle" data-id="{{=charge.accountId }}">
        <img src="{{=charge.avatar }}" class="imgWidth" alt="{{=_l('负责人头像') }}" />
    </span>
    <span class="taskUpdateCharge icon-task-folder-charge pointer ThemeColor3" id="taskUpdateCharge"></span>
</div>
<div class="boxSizing p82 pRight26 relative Font14 Hidden" id="taskMembersBox">
    <div class="createTaskLabel">{{=_l('任务参与者')}}</div>
    <span class="createTaskAddMemberBox">
        <i class="icon-task-add-member-circle createTaskAddMember ThemeColor3"></i>
    </span>
</div>
<div class="boxSizing p82 pRight26 relative Font14 {{=it.Deadlines ? '' : 'Hidden'}}" id="createTaskDate">
    <div class="createTaskLabel">{{=_l('起止时间')}}</div>
    <!-- <input class="boderRadAll_3 boxSizing ThemeBorderColor3" id="txtLastDate" type="text" placeholder="{{=_l('未指定日期')}}" readonly="readonly" value="{{=it.Deadlines }}" /> -->
    <div class="boderRadAll_3 boxSizing ThemeBorderColor3" id="txtLastDate"></div>
</div>
<div class="boxSizing p82 pRight26 relative Font14 Hidden" style="height: 38px;"  id="createTaskStage">
    <div class="createTaskLabel">{{=_l('看板')}}</div>
    <input type="hidden" id="folderStage" />
    <span class="InlineBlock w100" id="folderStageBox"></span>
</div>
<div class="boxSizing p82 pRight26 relative Font14 {{=it.Description ? '' : 'Hidden'}}" id="createTaskDesc">
    <div class="createTaskLabel">{{=_l('描述和附件')}}</div>
    <textarea
        type="text"
        id="txtDescriptionbox"
        spellcheck="false"
        class="txtDescriptionbox boxSizing boderRadAll_3 ThemeBorderColor3"
        value=""
        placeholder="{{=_l('填写描述…')}}"
    >
{{=it.Description }}</textarea
    >
    <div id="Attachment_updater_createTask"></div>
</div>
<div class="boxSizing p82 relative taskTabs Font14" id="taskTabs">
    <span data-type="member" class="ThemeBorderColor3 ThemeColor3">{{=_l('任务参与者')}}</span>
    {{?!it.Deadlines}}<span data-type="date" class="ThemeBorderColor3 ThemeColor3">{{=_l('设置时间')}}</span>{{?}}
    {{?!it.Description}}<span data-type="desc" class="ThemeBorderColor3 ThemeColor3">{{=_l('描述和附件')}}</span>{{?}}
</div>
<div class="boxSizing pLeft26 relative createTaskOperator">
    <span class="boderRadAll_3 ThemeBGColor3" id="taskSubmitBtn">{{=_l('创建')}}</span>
</div>
