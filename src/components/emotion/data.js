// 默认表情
// 默认表情
var defaultData = [
  ['呵呵', 'wx_thumb.gif'],
  ['哈哈', 'hanx_thumb.gif'],
  ['泪', 'lei_thumb.gif'],
  ['糗', 'qiu_thumb.gif'],
  ['偷笑', 'tx_thumb.gif'],
  ['可爱', 'ka_thumb.gif'],
  ['得意', 'dy_thumb.gif'],
  ['花心', 'se_thumb.gif'],
  ['失望', 'ng_thumb.gif'],
  ['鼓掌', 'gz_thumb.gif'],
  ['疑问', 'yw_thumb.gif'],
  ['吐', 'tu_thumb.gif'],
  ['敲打', 'qiao_thumb.gif'],
  ['发怒', 'fn_qq.gif'],
  ['奋斗', 'fd_qq.gif'],
  ['害羞', 'hx_qq.gif'],
  ['抓狂', 'zk_qq.gif'],
  ['晕', 'yun_qq.gif'],
  ['衰', 'shuai_qq.gif'],
  ['抱拳', 'bq_qq.gif'],
  ['握手', 'handshake.gif'],
  ['耶', 'yeah.gif'],
  ['Good', 'good.gif'],
  ['差劲', 'small.gif'],
  ['OK', 'ok.gif'],
  ['鞭炮', 'bp_qq.gif'],
  ['钞票', 'money_qq.gif'],
  ['吃饭', 'cf_qq.gif'],
  ['灯泡', 'dp_qq.gif'],
  ['喝茶', 'hc_qq.gif'],
  ['猴', 'monkey_qq.gif'],
  ['熊猫', 'panda_qq.gif'],
  ['啤酒', 'pj_qq.gif'],
  ['闪电', 'sd_qq.gif'],
  ['双喜', 'sx_qq.gif'],
  ['雪花', 'xh_qq.gif'],
  ['夜晚', 'yw_qq.gif'],
  ['拥抱', 'yb_qq.gif'],
  ['蛋糕', 'cake.gif'],
  ['心', 'heart.gif'],
  ['心碎', 'unheart.gif'],
  ['玫瑰', 'rose.gif'],
  ['礼物', 'gift.gif'],
  ['太阳', 'sun.gif'],
  ['威武', 'vw_thumb.gif'],
  ['I LOVE MY TEAM', 'team.gif'],
];
// 熊
var bearData = [
  ['x 嗨', '01.png'],
  ['x 哈哈', '02.png'],
  ['x 害羞', '03.png'],
  ['x 傲娇', '04.png'],
  ['x 得意', '05.png'],
  ['x 偷笑', '06.png'],
  ['x 酷', '07.png'],
  ['x 奔泪', '08.png'],
  ['x 可怜', '09.png'],
  ['x 花心', '10.png'],
  ['x 加油', '11.png'],
  ['x 怀疑', '12.png'],
  ['x 囧', '13.png'],
  ['x 灵感', '14.png'],
  ['x 牛逼', '15.png'],
  ['x 生气', '16.png'],
  ['x 纳尼', '17.png'],
  ['x 恶心', '18.png'],
  ['x 流汗', '19.png'],
  ['x 疑问', '20.png'],
  ['x 郁闷', '21.png'],
  ['x 晕', '22.png'],
  ['x 震惊', '23.png'],
  ['x I LOVE MY TEAM', '24.png'],
];
// emoji 表情
var emojiData = {};

emojiData[0] = {
  name: 'Smileys & People',
  content: [
    '😀',
    '😁',
    '😂',
    '😃',
    '😄',
    '😅',
    '😆',
    '😉',
    '😊',
    '😋',
    '😎',
    '😍',
    '😘',
    '😗',
    '😙',
    '😚',
    '☺',
    '😇',
    '😐',
    '😑',
    '😶',
    '😏',
    '😣',
    '😥',
    '😮',
    '😯',
    '😪',
    '😫',
    '😴',
    '😌',
    '😛',
    '😜',
    '😝',
    '😒',
    '😓',
    '😔',
    '😕',
    '😲',
    '😷',
    '😖',
    '😞',
    '😟',
    '😤',
    '😢',
    '😭',
    '😦',
    '😧',
    '😨',
    '😩',
    '😬',
    '😰',
    '😱',
    '😳',
    '😵',
    '😡',
    '😠',
    '😈',
    '👿',
    '👹',
    '👺',
    '💀',
    '👻',
    '👽',
    '💩',
    '😺',
    '😸',
    '😹',
    '😻',
    '😼',
    '😽',
    '🙀',
    '😿',
    '😾',
    '👦',
    '👧',
    '👨',
    '👩',
    '👴',
    '👵',
    '👶',
    '👱',
    '👮',
    '👲',
    '👳',
    '👷',
    '👸',
    '💂',
    '🎅',
    '👰',
    '👼',
    '💆',
    '💇',
    '🙍',
    '🙎',
    '🙅',
    '🙆',
    '💁',
    '🙋',
    '🙇',
    '🙌',
    '🙏',
    '👤',
    '👥',
    '🚶',
    '🏃',
    '👯',
    '💃',
    '💪',
    '👈',
    '👉',
    '☝',
    '👆',
    '👇',
    '✌',
    '✋',
    '👌',
    '👍',
    '👎',
    '✊',
    '👊',
    '👋',
    '👏',
    '👐',
    '💅',
    '👂',
    '👃',
    '👣',
    '👀',
    '👅',
    '👄',
    '💋',
    '👓',
    '👔',
    '👕',
    '👖',
    '👗',
    '👘',
    '👙',
    '👚',
    '👛',
    '👜',
    '👝',
    '🎒',
    '👞',
    '👟',
    '👠',
    '👡',
    '👢',
    '👑',
    '👒',
    '🎩',
    '🎓',
    '💄',
    '💍',
    '🌂',
    '💼',
    '👫',
    '👬',
    '👭',
    '💏',
    '💑',
    '👪',
  ],
};

emojiData[1] = {
  name: 'Animal & Nature',
  content: [
    '🙈',
    '🙉',
    '🙊',
    '🐵',
    '🐒',
    '🐶',
    '🐕',
    '🐩',
    '🐺',
    '🐱',
    '🐈',
    '🐯',
    '🐅',
    '🐆',
    '🐴',
    '🐎',
    '🐮',
    '🐂',
    '🐃',
    '🐄',
    '🐷',
    '🐖',
    '🐗',
    '🐽',
    '🐏',
    '🐑',
    '🐐',
    '🐪',
    '🐫',
    '🐘',
    '🐭',
    '🐁',
    '🐀',
    '🐹',
    '🐰',
    '🐇',
    '🐻',
    '🐨',
    '🐼',
    '🐾',
    '🐔',
    '🐓',
    '🐣',
    '🐤',
    '🐥',
    '🐦',
    '🐧',
    '🐸',
    '🐊',
    '🐢',
    '🐍',
    '🐲',
    '🐉',
    '🐳',
    '🐋',
    '🐬',
    '🐟',
    '🐠',
    '🐡',
    '🐙',
    '🐚',
    '🐌',
    '🐛',
    '🐜',
    '🐝',
    '🐞',
    '💐',
    '🌸',
    '💮',
    '🌹',
    '🌺',
    '🌻',
    '🌼',
    '🌷',
    '🌱',
    '🌲',
    '🌳',
    '🌴',
    '🌵',
    '🌾',
    '🌿',
    '🍀',
    '🍁',
    '🍂',
    '🍃',
    '🌍',
    '🌎',
    '🌏',
    '🌐',
    '🌑',
    '🌒',
    '🌓',
    '🌔',
    '🌕',
    '🌖',
    '🌗',
    '🌘',
    '🌙',
    '🌚',
    '🌛',
    '🌜',
    '☀',
    '🌝',
    '🌞',
    '⭐',
    '🌟',
    '🌠',
    '☁',
    '⛅',
    '💦',
    '💨',
    '☔',
    '⚡',
    '❄',
    '🔥',
    '💧',
    '🌊',
  ],
};

emojiData[2] = {
  name: 'Food & Drink',
  content: [
    '🍇',
    '🍈',
    '🍉',
    '🍊',
    '🍋',
    '🍌',
    '🍍',
    '🍎',
    '🍏',
    '🍐',
    '🍑',
    '🍒',
    '🍓',
    '🍅',
    '🍆',
    '🌽',
    '🍄',
    '🌰',
    '🍞',
    '🍖',
    '🍗',
    '🍔',
    '🍟',
    '🍕',
    '🍳',
    '🍲',
    '🍱',
    '🍘',
    '🍙',
    '🍚',
    '🍛',
    '🍜',
    '🍝',
    '🍠',
    '🍢',
    '🍣',
    '🍤',
    '🍥',
    '🍡',
    '🍦',
    '🍧',
    '🍨',
    '🍩',
    '🍪',
    '🎂',
    '🍰',
    '🍫',
    '🍬',
    '🍭',
    '🍮',
    '🍯',
    '🍼',
    '☕',
    '🍵',
    '🍶',
    '🍷',
    '🍸',
    '🍹',
    '🍺',
    '🍻',
    '🍴',
  ],
};

emojiData[3] = {
  name: 'Objects',
  content: [
    '💌',
    '💣',
    '💎',
    '🔪',
    '💈',
    '🚪',
    '🚽',
    '🚿',
    '🛁',
    '⌛',
    '⌚',
    '🎈',
    '🎉',
    '🎊',
    '🎎',
    '🎏',
    '🎐',
    '🎀',
    '🎁',
    '📯',
    '📻',
    '📱',
    '📲',
    '☎',
    '📞',
    '📟',
    '📠',
    '🔋',
    '🔌',
    '💻',
    '💽',
    '💾',
    '💿',
    '📀',
    '🎥',
    '📺',
    '📷',
    '📹',
    '📼',
    '🔍',
    '🔎',
    '🔬',
    '🔭',
    '📡',
    '💡',
    '🔦',
    '🏮',
    '📔',
    '📕',
    '📖',
    '📗',
    '📘',
    '📙',
    '📚',
    '📓',
    '📃',
    '📜',
    '📄',
    '📰',
    '📑',
    '🔖',
    '💰',
    '💴',
    '💵',
    '💶',
    '💷',
    '💸',
    '💳',
    '✉',
    '📧',
    '📨',
    '📩',
    '📤',
    '📥',
    '📦',
    '📫',
    '📪',
    '📬',
    '📭',
    '📮',
    '✏',
    '✒',
    '📝',
    '📁',
    '📂',
    '📅',
    '📆',
    '📇',
    '📈',
    '📉',
    '📊',
    '📋',
    '📌',
    '📍',
    '📎',
    '📏',
    '📐',
    '✂',
    '🔒',
    '🔓',
    '🔏',
    '🔐',
    '🔑',
    '🔨',
    '🔫',
    '🔧',
    '🔩',
    '🔗',
    '💉',
    '💊',
    '🚬',
    '🗿',
    '🔮',
    '🚩',
    '🎌',
    '🏴',
    '🏳',
  ],
};

var AruData = [
  [null, '01.png'],
  [null, '02.png'],
  [null, '03.png'],
  [null, '04.png'],
  [null, '05.png'],
  [null, '06.png'],
  [null, '07.png'],
  [null, '08.png'],
  [null, '09.png'],
  [null, '10.png'],
  [null, '11.png'],
  [null, '12.png'],
  [null, '13.png'],
  [null, '14.png'],
  [null, '15.png'],
  [null, '16.png'],
  [null, '17.png'],
  [null, '18.png'],
  [null, '19.png'],
  [null, '20.png'],
  [null, '21.png'],
  [null, '22.png'],
  [null, '23.png'],
  [null, '24.png'],
  [null, '25.png'],
  [null, '26.png'],
  [null, '27.png'],
  [null, '28.png'],
  [null, '29.png'],
  [null, '30.png'],
  [null, '31.png'],
  [null, '32.png'],
  [null, '33.png'],
  [null, '34.png'],
  [null, '35.png'],
  [null, '36.png'],
  [null, '37.png'],
  [null, '38.png'],
  [null, '39.png'],
  [null, '40.png'],
  [null, '41.png'],
  [null, '42.png'],
  [null, '43.png'],
  [null, '44.png'],
  [null, '45.png'],
  [null, '46.png'],
  [null, '47.png'],
  [null, '48.png'],
  [null, '49.png'],
  [null, '50.png'],
  [null, '51.png'],
  [null, '52.png'],
  [null, '53.png'],
  [null, '54.png'],
  [null, '55.png'],
  [null, '56.png'],
  [null, '57.png'],
  [null, '58.png'],
  [null, '59.png'],
  [null, '60.png'],
  [null, '61.png'],
  [null, '62.png'],
  [null, '63.png'],
  [null, '64.png'],
  [null, '65.png'],
  [null, '66.png'],
  [null, '67.png'],
  [null, '68.png'],
  [null, '69.png'],
  [null, '70.png'],
  [null, '71.png'],
  [null, '72.png'],
  [null, '73.png'],
  [null, '74.png'],
  [null, '75.png'],
  [null, '76.png'],
  [null, '77.png'],
  [null, '78.png'],
  [null, '79.png'],
  [null, '80.png'],
  [null, '81.png'],
  [null, '82.png'],
  [null, '83.png'],
  [null, '84.png'],
  [null, '85.png'],
  [null, '86.png'],
  [null, '87.png'],
  [null, '88.png'],
  [null, '89.png'],
  [null, '90.png'],
];

var emotionData = [
  {
    tab: {
      name: _l('历史'),
      className: 'tabItem-images-history',
      imageName: 'history',
    },
    content: [],
  },
  {
    tab: {
      name: _l('经典表情'), // 图标的hover
      size: 24, // 设置显示尺寸
      className: 'tabItem-images-default', // tab图标
      imageName: 'default',
      path: 'default/', // 图标路径
      showRetina: true, // 是否显示retina图片
    },
    content: [],
    itemClassName: 'emotion-default',
  },
  {
    tab: {
      name: _l('人物'),
      className: 'tabItem-images-smileys',
      showRetina: true,
      size: 24,
      imageName: 'smileys',
      type: 'emoji',
    },

    content: emojiData[0].content,
  },
  {
    tab: {
      name: _l('动物和大自然'),
      className: 'tabItem-images-animals',
      size: 24,
      imageName: 'animals',
      type: 'emoji',
    },
    content: emojiData[1].content,
  },
  {
    tab: {
      name: _l('食物和饮料'),
      className: 'tabItem-images-foods',
      imageName: 'foods',
      size: 24,
      type: 'emoji',
    },
    content: emojiData[2].content,
  },
  {
    tab: {
      name: _l('物体'),
      className: 'tabItem-images-objects',
      imageName: 'objects',
      size: 24,
      type: 'emoji',
    },
    content: emojiData[3].content,
  },
  {
    tab: {
      name: _l('笨笨熊'),
      className: 'icon-bear',
      imageName: 'bear',
      path: 'bear/',
      showRetina: false,
      size: '',
    },
    content: [],
    itemClassName: 'emotion-default',
  },
  {
    tab: {
      name: 'Aru',
      className: 'tabItem-images-aru',
      imageName: 'aru',
      path: 'aru/',
    },
    content: [],
    itemClassName: 'emotion-default',
  },
];

// 表情转换
// ===========================================================
(function (emotions) {
  for (var i = 0; i < emotions.length; i++) {
    for (var j = 0; j < emotions[i].length; j++) {
      var common = emotions[i][j];
      emotions[i][j] = {
        key: common[0],
        img: common[1],
      };
    }
  }
  defaultData = emotions[0];
  bearData = emotions[1];
  AruData = emotions[2];
  // emojiData = emotions[2];
  // })([defaultData, bearData, emojiData]);
})([defaultData, bearData, AruData]);

emotionData[1].content = defaultData;
emotionData[6].content = bearData;
emotionData[7].content = AruData;

export default emotionData;
