@font-face {
  font-family: 'emotion';
  src: url(data:application/x-font-ttf;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
[class^='emotion'],
[class*=' emotion'] {
  font-family: 'emotion';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.emotion-emoji:before {
  content: '\e601';
}
.emotion-bear:before {
  content: '\e600';
}
.emotion-face:before {
  content: '\31';
}
.emotion-history:before {
  content: '\32';
}
.emotion-twemoji {
  width: 22px;
  height: 22px;
}
.mdEmotion {
  position: absolute;
  background: #fff;
  box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
  border: solid 1px #c7c7c7;
  z-index: 99999;
  height: 240px;
}
.mdEmotion .mdEmotionWrapper {
  padding: 4px;
  width: 380px;
  height: 188px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}
.mdEmotion .arrow,
.mdEmotion .arrow:after {
  width: 0;
  height: 0;
  display: block;
  position: absolute;
  border-color: transparent;
  border-style: solid;
  border-width: 8px;
}
.mdEmotion .arrow:after {
  content: '';
}
.mdEmotion.emotion-bottom {
  margin-top: 8px;
}
.mdEmotion.emotion-bottom .arrow,
.mdEmotion.emotion-bottom .arrow:after {
  border-top-width: 0;
}
.mdEmotion.emotion-bottom .arrow {
  left: 50%;
  margin-left: -8px;
  top: -8px;
  border-bottom-color: #c7c7c7;
}
.mdEmotion.emotion-bottom .arrow:after {
  border-bottom-color: #fff;
  top: 1px;
  margin-left: -8px;
}
.mdEmotion.emotion-top {
  margin-top: -8px;
}
.mdEmotion.emotion-top .arrow,
.mdEmotion.emotion-top .arrow:after {
  border-bottom-width: 0;
}
.mdEmotion.emotion-top .arrow {
  left: 50%;
  margin-left: -8px;
  bottom: -8px;
  border-top-color: #c7c7c7;
}
.mdEmotion.emotion-top .arrow:after {
  border-top-color: #fff;
  bottom: 1px;
  margin-left: -8px;
}
.mdEmotion.emotion-left {
  margin-left: -8px;
}
.mdEmotion.emotion-left .arrow,
.mdEmotion.emotion-left .arrow:after {
  border-right-width: 0;
}
.mdEmotion.emotion-left .arrow {
  top: 50%;
  margin-top: -8px;
  right: -8px;
  border-left-color: #c7c7c7;
}
.mdEmotion.emotion-left .arrow:after {
  border-left-color: #fff;
  right: 1px;
  margin-top: -8px;
}
.mdEmotion.emotion-right {
  margin-left: 8px;
}
.mdEmotion.emotion-right .arrow,
.mdEmotion.emotion-right .arrow:after {
  border-left-width: 0;
}
.mdEmotion.emotion-right .arrow {
  top: 50%;
  margin-top: -8px;
  left: -8px;
  border-right-color: #c7c7c7;
}
.mdEmotion.emotion-right .arrow:after {
  margin-top: -8px;
  border-right-color: #fff;
  left: 1px;
}
.mdEmotion .mdEmotionTab {
  border-top: solid 1px #ececec;
  margin-bottom: -9px;
  padding: 10px 6px;
  height: 50px;
  background: #fff;
  box-sizing: border-box;
}
.mdEmotion .mdEmotionTab .tabItem {
  display: inline-block;
  font-size: 20px;
  line-height: 100%;
  cursor: pointer;
  vertical-align: middle;
  color: #666;
  padding: 4px;
  margin-right: 6px;
}
.mdEmotion .mdEmotionTab .tabItem:hover {
  background: #efefef;
}
.mdEmotion .mdEmotionTab .tabItem-images {
  width: 22px;
  height: 22px;
}
.mdEmotion .mdEmotionTab .tabItem.active {
  color: #08f;
  background: #f1f1f1;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel {
  display: none;
  margin-right: -17px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.panel1 .emotionItem {
  box-sizing: border-box;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.panel1 .emotionItem.emotionItemBear,
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.panel1 .emotionItem.emotionItemAru {
  transition: all 0.3s 0.1s;
  width: 25px;
  height: 25px;
  line-height: 24px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.panel1 .emotionItem.emotionItemBear img,
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.panel1 .emotionItem.emotionItemAru img {
  width: 100%;
  height: auto;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel.active {
  display: block;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem {
  font-size: 0;
  margin: 4px;
  display: inline-block;
  vertical-align: top;
  width: 25px;
  height: 25px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem.emoji {
  padding: 0 1px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem img {
  width: 24px;
  height: 24px;
  margin: 0;
  padding: 0;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem .emotion-twemoji {
  width: 22px;
  height: 22px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem.emotionItemBear {
  width: 53px;
  height: 53px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem.emotionItemBear img {
  width: 53px;
  height: 53px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem.emotionItemAru {
  width: 53px;
  height: 53px;
  line-height: 53px;
}
.mdEmotion .mdEmotionWrapper .mdEmotionPanel .emotionItem.emotionItemAru img {
  width: 100%;
  height: auto;
  vertical-align: middle;
  max-height: 100%;
}
