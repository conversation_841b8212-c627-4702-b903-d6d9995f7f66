.noAuthContentBox {
  align-items: center;
  justify-content: center;
  height: 100%;
  .icon-task-folder-message {
    color: #f78c00;
    font-size: 66px;
  }
  .noAuthJoin {
    height: 36px;
    line-height: 36px;
    color: #fff;
    padding: 0 32px;
    border-radius: 3px;
    display: inline-block;
  }
}

.programErrorBox {
  width: 100%;
  height: 100%;
  position: relative;
  .programError {
    position: absolute;
    top: 20px;
    bottom: 20px;
    left: 20px;
    right: 20px;
    border-radius: 3px;
    box-shadow: 0 3px 10px 2px rgba(0, 0, 0, 0.16), 0 5px 20px 4px rgba(0, 0, 0, 0.1);
    background: #fff;
    align-items: center;
    justify-content: center;
    .programErrorImg {
      background: url(./error.png) no-repeat;
      background-size: 140px 124px;
      width: 140px;
      height: 124px;
    }
    .programRefresh {
      height: 36px;
      line-height: 36px;
      color: #fff;
      padding: 0 32px;
      border-radius: 3px;
      display: inline-block;
    }
  }
}

.programErrorMinBox {
  min-width: 230px;
  max-width: 100%;
  height: 100%;
  position: relative;
  align-items: center;
  justify-content: center;
  color: rgba(150, 150, 150, .7);
  .icon-task-setting_promet {
    color: rgba(150, 150, 150, .5);
  }
}

.errorPageErrorLog {
  color: #c63737;
  white-space: break-spaces;
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
  .copy {
    color: #666;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 2px 6px;
    display: inline-block;
  }
}

.errorPageShowError {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 0px;
  right: 0px;
}
