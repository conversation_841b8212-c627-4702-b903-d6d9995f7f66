﻿.dialogBoxCreateGroup .dialogCloseBtn:not(:hover) {
  color: rgba(255, 255, 255, .6)!important;
}

.dialogBoxCreateGroup .dialogCloseBtn:hover {
  color: rgba(255, 255, 255, .8);
}

.dialogBoxCreateGroup {
  border: none;
}

.dialogBoxCreateGroup .dialogContent {
  margin-top: -18px;
  padding: 0;
}

.dialogBoxCreateGroup .groupType input[type='radio'] {
  margin-top: 0;
  vertical-align: middle;
}

.dialogBoxCreateGroup .groupTopContent .groupAvatar {
  width: 100%;
  height: 100%;
  vertical-align: bottom;
  border-radius: 50%;
}

.dialogBoxCreateGroup .groupTopContent .groupHead {
  width: 88px;
  height: 88px;
  margin-right: auto;
  margin-left: auto;
  border: 3px solid rgba(225, 225, 225, .5);
  border-radius: 50%;
}

.dialogBoxCreateGroup .groupTopContent .groupHead:hover {
  border-color: #fff;
}

.dialogBoxCreateGroup .groupTopContent .groupHead:hover::before {
  line-height: 88px;
  position: absolute;
  width: 88px;
  height: 88px;
  content: attr(titlename);
  color: #fff;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .4);
}

.dialogBoxCreateGroup .groupNameTotal {
  font-size: 16px;
  color: #fff;
}

.dialogBoxCreateGroup p {
  margin: 0;
  padding: 0;
}

.dialogBoxCreateGroup .form-label {
  line-height: 26px;
  float: left;
  min-width: 70px;
}

.dialogBoxCreateGroup .txtGroupName {
  line-height: 26px;
  height: 26px;
}

.dialogBoxCreateGroup .txtProjectName {
  line-height: 30px;
  height: 30px;
  color: #151515;
  outline: none;
}

.dialogBoxCreateGroup .txtGroupAbout {
  min-height: 100px;
  padding-top: 2px;
  padding-left: 7px;
  border: 1px #efefef solid;
}

.dialogBoxCreateGroup .btnCreate {
  font-size: 14px;
  width: 100px;
  height: 35px;
  padding: 7px 20px;
}

.dialogBoxCreateGroup .cancelCreate {
  display: inline-block;
  margin-right: 25px;
  text-decoration: none;
  color: #999;
}

.dialogBoxCreateGroup .TextBoxNew {
  padding-left: 10px;
  border: 1px #efefef solid;
  border-radius: 3px;
  flex: 1;
}

.dialogBoxCreateGroup .mainContent .spaceBetween {
  justify-content: space-between;
}

/* group avatar select */

.createGroupAvatarSelect {
  position: absolute;
  width: 400px;
  text-align: left;
  background: #fff;
  border-radius: 4px;
  z-index: 1;
  left: 50%;
  top: 120px;
  transform: translateX(-200px);
  display: none;
}

.createGroupAvatarSelect .icon-close {
  position: absolute;
  right: 5px;
  top: 5px;
}

.createGroupAvatarSelect .settingPictureLayerTitle {
  font-size: 13px;
  padding: 12px 0 0 15px;
  color: #999;
}

.createGroupAvatarSelect .settingPictureLayerImg {
  padding: 0 0 0 16px;
}

.createGroupAvatarSelect .settingPictureLayerImg img {
  float: left;
  width: 68px;
  height: 68px;
  margin: 7px 7px 0 0;
}

.createGroupAvatarSelect .settingPictureLayerImg img:hover {
  transition: transform .5s;
  transform: scale(1.1);
}

.createGroupAvatarSelect .insertGroupImg {
  font-size: 13px;
  line-height: 45px;
  height: 45px;
  padding-left: 20px;
}

.dialogBoxCreateGroup > .mui-dialog-scroll-container > .mui-dialog-dialog > .mui-dialog-header,
.dialogBoxCreateGroup > .mui-dialog-scroll-container > .mui-dialog-dialog > .mui-dialog-body {
  padding: 0 !important;
}

.dialogBoxCreateGroup > .mui-dialog-scroll-container > .mui-dialog-dialog > .mui-dialog-close-btn .Icon:hover {
  color: hsla(0,0%,100%,.8) !important;
}

#hiddenCompanysBox .Dropdown--border {
  height: 26px;
}
