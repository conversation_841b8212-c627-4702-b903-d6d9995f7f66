
.groupValidate-wrapper {
    width: 1000px;
    height: 600px;
    margin: 20px auto 0;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    >div {
        color: #757575;
        text-align: center;
    }
    .group-name {
        color: #151515;
        font-size: 20px;
        padding-bottom: 8px;
    }
    .group-row, .group-create-tiem {
        /*width: 100%;*/
    }
    .group-row {
        display: flex;
        flex-direction: row;
        padding-top: 20px;
        >div:first-child{
            padding-right: 20px;
        }
    }
    .black {
        color: black;
    }
    .group-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 50px 0 16px 0;
    }
    .group-create-tiem {
        padding: 12px 0 32px 0;
        text-align: left;
    }
    .group-about {
        text-align: left;
        span {
            color: #000;
            margin-bottom: 6px;
            font-weight: bold;
            display: inline-block;
        }
        div {
            width: 400px;
        }
    }
    .group-btns {
        display: flex;
        justify-content: center;
        padding-top: 32px;
    }
    .group-btn {
        font-size: 15px;
        height: 36px;
        width: 100px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 0 5px;
        box-sizing: initial;
        i {
            font-size: 20px;
            margin-right: 7px;
        }
    }
    .group-send {
        margin-right: 10px;
    }
    .group-apply, .group-send {
        color: #fff;
    }
    .group-feed {
        padding: 0 10px;
        background-color: #ddd;
    }
}


