﻿{{  var group = it.data,
        avatar = group.avatar,
        gid = group.groupId,
        name = group.name,
        isVerified = group.isVerified,
        isAdmin = group.isAdmin,
        isGroup = group.isPost;
 }}
<div class="groupSetting">
    <!-- Header -->
    <div class="groupTop TxtCenter">
        <div class="groupTopContent ThemeBGColor3 {{= isGroup ? 'pTop25' : 'pTop45 pBottom50' }}">
        {{? isGroup }}
            <div class="groupHeader">
                {{? isAdmin }}
                <div class="groupHead Hand" titlename="{{= _l('修改头像') }}"><img class="Hand groupAvatar adminAvatar circle" src="{{=avatar}}"/></div>
                {{??}}
                <img src="{{=avatar}}" class="groupAvatar circle"/>
                {{?}}
                <div class="groupNameTotal Font16 mTop10">
                    <span class="groupName" title="{{=name}}">{{!name}}</span>
                    <span class="groupIcon icon-official-group Font16 {{= isVerified ? '' : 'Hidden'}}"></span>
                </div>
            </div>
            <ul class="groupTabList">
                <li class="groupInfoTab commItem">{{= _l('群信息') }}</li>
                <li class="groupMemberTab commItem">{{= _l('群成员') }}</li>
                <li class="groupSettingTab commItem">{{= _l('群设置') }}</li>
            </ul>
        {{??}}
            <img src="{{=avatar}}" class="groupAvatar"/>
            <div class="groupNameTotal Font16 mTop10">
                <span class="groupName" title="{{!name}}">{{!name}}</span>
            </div>
        {{?}}
        </div>
    </div>
    <!-- tabCotent -->
    <div class="groupSettingContent">
        <div class="groupInfo Hidden" data-type="info"></div>
        <div class="groupMember Hidden" data-type="member"></div>
        <div class="groupSettings Hidden" data-type="settings"></div>
    </div>
</div>
