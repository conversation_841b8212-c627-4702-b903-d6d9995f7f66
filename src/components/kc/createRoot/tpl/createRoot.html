﻿{{ var myPermis = it.permission; }}
<div class="folderContent">
    {{? /\/apps\/kc/.test(location.pathname) && it.appName}}
    <div class="relatedToApp">
        <i class="icon-knowledge-message Font16"></i>
        <span>{{! _l('此共享文件夹已被"%0"应用关联', it.appName) }}</span>
    </div>
    {{?}}
    <div class="folderName">
        <span class="folderNameText Font12">{{!_l('文件夹名称')}}</span>
        {{var readOnly = myPermis == 1 || myPermis== 2 ? '' : 'readonly = "readonly"'; }}
        <input type="text" class="txtFolderName boderRadAll_3 boxSizing" maxlength="256" {{=readOnly }}
               placeholder="{{!_l('请输入文件夹名称')}}" data-id="{{=it.id }}" />
        <i class="addFolderStar {{= it.isStared ? 'icon-task-star' : 'icon-star-hollow' }}"></i>
    </div>
    <div class="attribute {{= md.global.Account.projects.length == 0 ? 'Hidden' : ''}}">
        <span class="title Font12">{{!_l('文件夹归属')}}</span>
        <div class="dropBox Font12">
            <span class="seleted ellipsis" title="{{! it.project && it.project.projectId ? it.project.companyName : _l('个人')}}" data-project-id="{{! it.project && it.project.projectId || ''}}">{{! it.project && it.project.projectId ? it.project.companyName : _l('个人') }}</span>
            <span class="icon icon-arrow-down Right"></span>
            <ul id="folderAttributeList" class="z-depth-1-half Hidden"></ul>
        </div>
    </div>
    <div class="folderMembers">
        <div class="folderMemberBox">
            <div class="addMember addUser">
                <span class="ThemeColor3 text addFriends">
                    <i class="icon-addapplication addMemberIcon"></i>
                    <span>{{!_l('添加协作者')}}</span>
                </span>
                <!--<i class="icon-help Font16"></i>-->
            </div>
            <div class="nanoCon">
                <ul class="memberList">
                    {{~it.members:member:index}}
                    <li class="memberItem" {{= member.accountStatus == 3 && !member.accountId ? 'data-account="'+ member.account +'"' : 'data-account-id="'+ member.accountId +'"'}}>
                        <span class="imgMemberBox" data-account-id="{{=member.accountId }}">
                            <img class="imgCharge circle" src="{{=member.avatar }}" />
                        </span>
                        <span class="memberName ellipsis">
                            <span class="added ellipsis {{= member.inviterAccountId && member.memberStatus == 2 ? 'addedMax' : ''}}" title="{{! member.fullname }}">{{! member.fullname }}</span>
                            {{?member.inviterAccountId && member.memberStatus == 2}}
                                <span class="inviter">({{= _l('%0 邀请','<a class="name ellipsis" data-account-id="'+ member.inviterAccountId +'">'+ member.inviterFullName+'</a>')}})</span>
                            {{?}}
                        </span>

                        <span class="permission" {{= member.accountStatus == 3 && !member.accountId ? 'data-account="'+ member.account +'"' : 'data-account-id="'+ member.accountId +'"'}} data-permission="{{= member.permission}}" data-apkname="{{= member.apkName || ''}}">
                            <span class="pointer {{= member.roleName  ? 'isApk' : ''}}">
                                {{?member.permission === 1}}
                                <span class="text owner">{{!_l('拥有者')}}</span>
                                {{??member.permission == 2}}
                                <span class="text">{{!_l('管理员')}}</span>
                                {{??member.permission == 3 }}
                                <span class="text">{{=  member.memberStatus != 2 ? _l('可编辑') : _l('待审核') }}</span>
                                {{??member.permission == 4 }}
                                <span class="text">{{=  member.memberStatus != 2 ? _l('只读') : _l('待审核') }}</span>
                                {{?}}

                                {{? member.permission != 1 && (myPermis == 1 || myPermis == 2) && !member.roleName}}
                                    <i class="icon-arrow-down-border"
                                        data-account-status="{{=member.accountStatus}}"
                                        data-member-status="{{=member.memberStatus}}"></i>
                                {{?}}
                            </span>
                        </span>
                        {{?member.roleName}}
                        {{??member.permission == 1 && member.accountId == md.global.Account.accountId}}
                            <span class="rootTrust">{{!_l('托付文件夹')}}</span>
                        {{??member.permission == 2 }}
                        {{?member.accountId === md.global.Account.accountId}}
                            <span class="remove exit">{{!_l('退出')}}</span>
                        {{??myPermis === 1}}
                        {{? member.accountStatus == 3}}
                             <span class="toInvite ">{{!_l('重新邀请')}}</span>
                        {{?}}
                            <span class="remove">{{!_l('移除')}}</span>
                        {{??}}
                        {{?}}
                        {{??member.permission == 3 }}
                        {{?member.accountId === md.global.Account.accountId}}
                            <span class="remove exit">{{!_l('退出')}}</span>
                        {{??myPermis === 1 || myPermis == 2}}
                        {{? member.accountStatus == 3}}
                            <span class="toInvite ">{{!_l('重新邀请')}}</span>
                        {{?}}
                            <span class="remove">{{!_l('移除')}}</span>
                        {{??}}
                        {{?}}
                        {{??member.permission == 4 }}
                        {{?member.accountId === md.global.Account.accountId}}
                            <span class="remove exit">{{!_l('退出')}}</span>
                        {{??myPermis === 1 || myPermis == 2}}
                        {{? member.accountStatus == 3}}
                            <span class="toInvite ">{{!_l('重新邀请')}}</span>
                        {{?}}
                            <span class="remove">{{!_l('移除')}}</span>
                        {{??}}
                        {{?}}
                        {{?}}
                    </li>
                    {{~}}
                </ul>
            </div>
        </div>

        <ul id="updatePermission" class="taskBoxShadow" style="display:none;">
            <li data-value="2" class="adminItem itemLi">
                <span class="itemText">
                    <span>{{!_l('管理员')}}</span>
                    <i class="icon-ok selectStatus Hidden"></i>
                </span>
                <span class="describe">{{! '可管理共享成员，管理文件'}}</span>
            </li>
            <li class="line"></li>
            <li data-value="3" class="ordinaryItem itemLi">
                <span class="itemText rank">
                    <span>{{! '可编辑'}}</span>
                    <i class="icon-ok selectStatus Hidden"></i>
                </span>
                <span class="describe">{{! '可添加共享者，添加修改文件'}}</span>
            </li>
            <li class="line"></li>
            <li data-value="4" class="readOnlyItem itemLi">
                <span class="itemText">
                    <span>{{! '只读'}}</span>
                    <i class="icon-ok selectStatus Hidden"></i>
                </span>
                <span class="describe">{{! '可查看和下载允许下载的文件'}}</span>
            </li>
        </ul>

        <ul id="checkInviter" class="taskBoxShadow" style="display:none">
            <li class="pass">{{!_l('允许')}}</li>
            <li class="reject">{{!_l('拒绝')}}</li>
        </ul>
    </div>
</div>
