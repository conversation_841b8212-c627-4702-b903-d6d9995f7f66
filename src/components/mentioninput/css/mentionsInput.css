.mentions-input-box {
  position: relative;
  background: #fff;
}

.mentions-input-box textarea {
  display: block;
  overflow: hidden;
  background: transparent;
  position: relative;
  outline: 0;
}

.mentions-input-box .mentions-autocomplete-list {
  display: none;
  background-color: #fff;
  position: absolute;
  left: 0;
  right: 0;
  margin-top: -2px;
  -moz-border-radius: 5px;
  -khtml-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.148438);
  -moz-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.148438);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.148438);
  width: 100%;
  max-width: 350px;
  max-height: 460px;
  overflow: auto;
}

.mentions-input-box .mentions-autocomplete-list ul {
  margin: 0;
  padding: 0;
  margin-top: 5px;
  margin-bottom: 10px;
}

.mentions-input-box .mentions-autocomplete-list li {
  background-color: #fff;
  padding: 0px 20px 0px 5px;
  margin: 0;
  width: auto;
  overflow: hidden;
  cursor: pointer;
  list-style: none;
  white-space: nowrap;
  position: relative;
  box-sizing: initial;
  height: 50px;
}

.mentions-input-box .mentions-autocomplete-list .mentions-autocomplete-list-category {
  height: 36px;
  line-height: 36px;
}

.mentions-input-box .mentions-autocomplete-list li > img,
.mentions-input-box .mentions-autocomplete-list li > div.icon {
  width: 32px;
  height: 32px;
  position: absolute;
  top: 50%;
  margin-top: -16px;
  margin-left: 5px;
  margin-right: 5px;
  -moz-background-origin: 3px;
  border-radius: 50%;
}

.mentions-input-box .mentions-autocomplete-list li em {
  font-weight: bold;
  font-style: none;
}

.mentions-input-box .mentions-autocomplete-list li:hover,
.mentions-input-box .mentions-autocomplete-list li.active {
  background-color: #f5f5f5;
}

.mentions-input-box .mentions-autocomplete-list li b {
  background: #ffff99;
  font-weight: normal;
}

.mentions-input-box .mentions {
  position: absolute;
  left: 1px;
  right: 0;
  top: 1px;
  bottom: 0;
  padding: 9px;
  color: #fff;
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.mentions-input-box .mentions > div {
  color: #fff;
  white-space: pre-wrap;
  width: 100%;
  display: none;
}

.mentions-input-box .mentions > div > strong {
  font-weight: normal;
  background: #fff;
  color: #fff;
}

.mentions-autocomplete-list li[data-ref-id='taskAtUserAll'] .limitWidth {
  max-width: 150px;
}

.mentions-autocomplete-list .itemContent {
  margin-left: 47px;
}
.mentions-autocomplete-list .itemContent > div {
  box-sizing: initial;
  height: 20px;
  line-height: 20px;
}
.mentions-autocomplete-list .itemContent > div:first-child {
  padding-top: 6px;
}
.mentions-autocomplete-list .itemContent > div:last-child {
  padding-bottom: 6px;
}
.mentions-autocomplete-list .itemContent > div > span {
  margin-right: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}
/* .mentions-autocomplete-list .itemContent .fullname {
  max-width: 80px !important;
} */
.mentions-autocomplete-list .title {
  border-top: 1px solid #eaeaea;
  padding: 8px;
  font-size: 12px;
}
.mentions-autocomplete-list li[data-ref-type='user'] > .itemContent > div:first-child > span {
  max-width: 200px;
}
.mentions-autocomplete-list li[data-ref-type='group'] > .itemContent > div:first-child > span {
  max-width: 200px;
}
.mentions-autocomplete-list .itemContent > div:last-child > span {
  max-width: 282px;
}

.mentionNoData {
  text-align: center;
}
.mentionNoData .noOne {
  margin: 20px;
}
.mentionNoData .invite {
  margin: 70px 0;
}
.mentionNoData .icon-invite {
  font-size: 65px;
  color: #9fa0a0;
}
.mentionNoData p {
  margin-top: 20px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}
