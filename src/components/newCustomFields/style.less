.customFieldsContainer {
  background: #fff;
  margin: 0 -12px;
  color: #151515;
  line-height: normal;

  &:after {
    content: '';
    clear: both;
    display: block;
  }

  &.mobileContainer {
    .worksheetRecordCard {
      .deleteRecord {
        display: inline-block !important;
      }
    }

    .signature {
      .remove {
        visibility: visible;
      }
    }

    > .customFormLine:first-child {
      display: none;
    }

    .location {
      .icon-minus-square {
        display: block;
      }
    }

    .customFormItemControl {
      .customFormControlBox {
        font-size: 13px;
        &.customFormControlOCR {
          border-color: #e0e0e0 !important;
          max-width: 100%;
          &:hover {
            background-color: #fff !important;
          }
        }
      }
      .mobileCreateRelateBtn {
        padding: 0 !important;
      }
      .recordsCon {
        padding: 0 !important;
      }
    }
  }

  &.wxContainer {
    .customFormLine {
      display: none !important;
    }
    .customFormControlBox {
      border-color: #e0e0e0 !important;
      &:not(.formBoxNoBorder):not(.relateRecord):not(.customFormControlSwitch):not(.customFormControlScore):not(
          .customFormControlUser
        ):not(.customFormControlDropDown) {
        padding-right: 12px !important;
      }
      &:not([type='tel']):not(.formBoxNoBorder):not(.relateRecord):not(.customFormControlSwitch):not(
          .customFormControlScore
        ):not(.customFormControlUser):not(.customFormTextareaBox):not(.customFormControlDropDown) {
        padding-left: 12px !important;
      }
      &.controlDisabled {
        background: #f9f9f9 !important;
      }
      &:not(.controlDisabled) {
        background: #fff !important;
      }
      &.customFormReadonly {
        background: #f9f9f9 !important;
        border-color: #e0e0e0 !important;
        padding: 8px 20px 6px !important;
      }
      > .icon-arrow-right-border {
        margin-right: -5px;
      }
      .worksheetRecordCard {
        box-shadow: none;
        border-color: #e0e0e0;
      }
    }
    .customFormControlTel {
      .customFormControlBox {
        &.controlDisabled {
          padding-left: 12px !important;
        }
      }
    }
    .customFormControlTelBtn {
      display: none;
    }
  }

  &.List {
    width: 100%;

    .MenuItem {
      height: 36px;
      line-height: 36px;

      .Item-content:not(.disabled):hover {
        background: #f2f2f2 !important;
        color: #151515 !important;
      }
    }
  }

  .customFormItem {
    float: left;
    width: 100%;
    position: relative;
    flex-direction: column;
    padding: 6px 12px;
    box-sizing: border-box;

    .formItemHoverShow {
      display: none;
    }

    &.customFormItemRow {
      flex-direction: row;
      .customFormItemLabel {
        width: auto;
        align-items: flex-start;
      }
      &::after {
        display: none;
      }
    }

    &:after {
      content: '';
      display: block;
      width: 100%;
      clear: both;
    }

    &:hover {
      .formItemHoverShow {
        display: inline-block;
      }
    }
  }

  .customFormLine {
    height: 1px;
    background: #f3f3f3;
    clear: both;
    margin: 0 12px;
  }

  .customFormItemLabel {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    position: relative;
    font-weight: bold;
    width: 100%;
    line-height: 24px;
    min-height: 24px;
    padding-top: 0px;
    padding-bottom: 4px;
    transform: translate3d(0, 0, 0);

    .requiredBtnBox {
      position: absolute;
      left: -13px;
      .requiredBtn {
        color: #f44336;
      }
    }

    .hideTitleLabel {
      display: none;
    }

    .customFormItemLoading {
      display: block;
      animation: rotate 2s linear infinite;
    }
  }

  .descBoxInfo {
    float: left;
    position: relative;
    z-index: 1;
  }

  .customFormItemControlCreate.customFormItemControl {
    .customFormControlBox,
    .RelateRecordDropdown .RelateRecordDropdown-selected:not(.readonly) {
      border: 1px solid #ddd;
      background-color: transparent;
      &:not(.controlDisabled):hover {
        border-color: #ccc;
        background-color: transparent;
      }
      &.active,
      &:focus {
        border-color: #2196f3 !important;
        background: #fff !important;
        box-shadow: none !important;
      }
    }

    .customFormControlMobileHover {
      &:hover .customFormControlBox {
        border-color: #ccc;
        background-color: transparent;
      }
      input.customFormControlBox {
        border: 1px solid #ddd;
        background: transparent;
        &.customFormPhoneBox {
          border: none;
          background: transparent;
        }
        &:not(.controlDisabled):hover {
          border-color: #ccc;
          background-color: transparent;
        }
      }
    }

    .customCascader,
    .customAntSelect {
      &.ant-select-open {
        .ant-select-selector {
          border-color: #2196f3 !important;
          background-color: #fff !important;
        }
      }

      &:not(.ant-select-open):not(.ant-select-disabled) {
        .ant-select-selector {
          border: 1px solid #ddd !important;
          background-color: transparent !important;
        }
        .ant-select-selector {
          &:hover {
            border-color: #ccc !important;
            background-color: transparent !important;
          }
        }
      }
    }
  }

  .customFormItemControl {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    flex: 1;
    min-width: 0;
    &:hover {
      .RefreshBtn {
        display: inline-block;
      }
    }

    .optionDisabled.ant-select-disabled {
      cursor: text;
      .ant-select-selector {
        pointer-events: none;
        .ant-select-selection-item {
          cursor: text !important;
          user-select: all !important;
        }
      }
    }
    .scaleContent .scaleText {
      cursor: text !important;
      user-select: all !important;
    }

    .RefreshBtn {
      position: absolute;
      right: 0;
      top: 12px;
      display: none;
      i {
        display: block;
        &.isLoading {
          animation: rotate 2s linear infinite;
          color: #2196f3;
        }
      }
    }

    .numberControlBox {
      display: flex;
      flex-direction: column;
      align-items: stretch;
      width: 36px;
      &.disabled {
        .iconWrap {
          cursor: not-allowed;
          color: #bdbdbd !important;
        }
      }
      .iconWrap {
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #dddddd;
        border-top: 1px solid #dddddd;
        flex: auto;
        background: #f7f7f7;
        cursor: pointer;
        overflow: hidden;
        transition: all 0.2s linear;
        &:hover {
          i {
            color: #2196f3;
          }
        }
        &:first-child {
          border-top: none;
        }
        i {
          font-size: 13px;
          color: #757575;
        }
      }
    }

    .maskIcon {
      position: absolute;
      right: 12px;
      font-size: 16px;
      top: 9px;
    }

    .customFormControlMobileHover {
      &:hover {
        .customFormControlBox {
          border-color: #f2f2f2;
          background: #f2f2f2;
        }
      }
    }

    .maskHoverTheme {
      cursor: pointer;
      i {
        font-size: 16px;
        vertical-align: middle;
      }
      &:hover {
        color: #1d5786 !important;
        i {
          color: #9e9e9e !important;
        }
      }
    }

    .descBoxInfo {
      position: absolute;
      left: 65px;
      top: 8px;
    }

    .descBox {
      .descTxt {
        position: relative;
        max-height: 48px;
        height: auto;
        overflow-y: hidden;
        display: block;
        line-height: 16px !important;
        width: 100%;
        overflow-x: hidden;
        white-space: pre-wrap;
        word-break: break-all;

        span {
          display: block;
          height: auto;
        }

        .descText::after {
          content: '更多';
          color: #fff;
          padding-left: 6px;
          word-wrap: break-word;
          word-break: break-all;
        }

        .moreDesc {
          position: absolute;
          right: 0;
          bottom: -1px;
          background: #fff;
          color: #2196f3;
          border: 1px solid rgba(0, 0, 0, 0);
          padding: 0 3px;
          line-height: 16px;
        }

        .moreDesc:hover {
          text-decoration: underline;
        }
      }
    }

    .iti--allow-dropdown {
      width: 100%;
    }

    .iti__country-list {
      max-height: 300px;
    }

    .customFormNull {
      width: 22px;
      height: 6px;
      background: #eaeaea;
      margin: 15px 0;
      border-radius: 3px;
    }

    .customFormControlVerify {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin-top: 8px;
      input {
        min-width: 110px;
        max-width: 180px;
        margin-right: 8px;
        &.verifyCodeStyle {
          max-width: 100%;
          margin-right: 0;
          margin-bottom: 8px;
        }
      }
      .Button {
        width: 110px;
        padding: 0;
        font-size: 13px;
      }
    }

    .customFormControlBox,
    .customFormTextarea {
      &::-webkit-input-placeholder {
        color: #bdbdbd;
      }

      &:-moz-placeholder {
        color: #bdbdbd;
      }

      &::-moz-placeholder {
        color: #bdbdbd;
      }

      &:-ms-input-placeholder {
        color: #bdbdbd;
      }
    }

    .customLocationDisabled {
      box-sizing: border-box;
      height: 36px;
      border: 1px solid #e0e0e0;
      font-size: 13px;
      background: #f5f5f5;
      border-radius: 4px;
      padding: 0 12px;
      width: 100%;
      outline: none;
      -webkit-appearance: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: not-allowed;
      color: #bdbdbd;
    }

    .customFormControlUser {
      flex-wrap: wrap;
      min-width: 0;
      align-items: center;
      height: auto !important;
      background: #fff !important;
      border-color: #fff !important;
      padding: 0 !important;
    }

    .customFormControlBox {
      box-sizing: border-box;
      height: 36px;
      border: 1px solid #f7f7f7;
      font-size: 13px;
      background: #f7f7f7;
      border-radius: 4px;
      padding: 0 12px;
      width: 100%;
      outline: none;
      -webkit-appearance: none;
      transform: rotateZ(360deg);

      &.signature {
        transform: unset !important;
      }

      &:not(.controlDisabled):hover {
        border-color: #f2f2f2;
        background: #f2f2f2;
        .maskIcon {
          color: #9e9e9e !important;
        }
      }

      &:focus {
        border-color: #2196f3 !important;
        background: #fff !important;
        box-shadow: none !important;
      }

      &.readOnlyDisabled {
        .Checkbox--disabled,
        .Radio--disabled {
          cursor: pointer !important;
          &.checked {
            .Radio-box {
              border-color: #1e88e5 !important;
              opacity: 0.7;
            }
            .Radio-box-round {
              background-color: #1e88e5 !important;
            }
          }
          .Checkbox-box,
          .Radio-box {
            display: inline-block !important;
          }
        }
      }

      &.controlDisabled {
        background: transparent !important;
        border-color: transparent !important;
        padding-left: 0;
        padding-right: 0;
        .Dropdown--input {
          padding-left: 0 !important;
        }
        .mdEditorContent {
          max-height: inherit !important;
        }
        .Radio--disabled,
        .Checkbox--disabled {
          cursor: unset !important;
        }
      }

      &.customFormFileBox:not(.controlDisabled) {
        color: #757575;
        padding: 8px 10px 7px;
      }

      &.dateTimeIcon {
        .dateClearIcon {
          display: none;
          cursor: pointer;
          color: #bdbdbd;
          &:hover {
            color: #777 !important;
          }
        }
        &:hover,
        &:focus {
          .dateClearIcon {
            display: inline-block;
          }
          .bellScheduleIcon {
            display: none;
          }
        }
      }

      .worksheetRecordCard {
        margin-bottom: 10px;

        &:first-child {
          margin-top: 10px;
        }
      }

      &.mdEditor {
        min-height: 90px;
        height: inherit;

        .mdEditorContent {
          padding: 0 15px;
          margin: 0 -13px;

          img {
            max-width: 100%;
          }
        }
      }

      &.customFormEditor {
        background: #fff !important;
        border-color: #2196f3 !important;
      }

      &.formBoxNoBorder {
        padding: 5px 0 0;
        background: #fff !important;
        border-color: #fff !important;
        width: auto;
      }

      &.customDropdownBox {
        padding: 0;
        height: auto;
        min-height: 36px;
        .Dropdown--border {
          height: auto !important;
          border: none !important;
          min-height: 34px;
          padding-top: 0;
          padding-bottom: 0;
          .value {
            white-space: normal !important;
          }
          .Dropdown--placeholder {
            line-height: 34px;
          }
        }
      }

      &.customFormControlOCR {
        padding: 0 16px;
        display: inline-flex !important;
        align-items: center;
        cursor: pointer;
        border-color: #ddd !important;
        background: #fff;
        justify-content: center;
        max-width: 320px;
        &:hover {
          background: #f5f5f5 !important;
        }
        .customOCRLoading {
          display: block;
          animation: rotate 2s linear infinite;
          color: #2196f3;
        }
      }
      &.customFormSwitchColumn {
        height: auto;
        .customFormCheck {
          flex-wrap: wrap;
          label.Radio {
            max-width: 100%;
            margin-top: 6px;
            margin-bottom: 6px;
          }
        }
      }
      &.customFormControlSwitch {
        height: auto !important;
        .Checkbox {
          margin-top: 8px;
        }
      }
      .mobileFormSwitchDisabled {
        opacity: 0.4;
        &.ming.Switch--off {
          background-color: #ccc !important;
        }
        &.ming.Switch--on {
          background: #01ca83 !important;
        }
      }
      &.customFormControlSwitch,
      &.customFormControlScore {
        background: none !important;
        border: none !important;
        padding-left: 0 !important;
      }
    }

    &.richTextDisabledControl {
      .ck .ck-content {
        min-height: 36px !important;
      }
    }

    .textAreaDisabledControl {
      min-height: 36px !important;
    }

    .customFormTextareaBox {
      padding: 8px 12px 6px;
      height: auto;
      white-space: pre-wrap;
      min-height: 36px;
    }
    .mobileCustomFormTextareaBox {
      overflow: hidden;
    }

    .customFormTextarea {
      box-sizing: border-box;
      padding: 6px 12px;
      vertical-align: top;
      border-color: #2196f3;
      background: #fff;
      font-size: 13px;
    }

    .smallInput {
      width: 1px;
      height: 18px;
      vertical-align: top;
      border: none;
      display: inline-block;
      background: transparent;
      padding: 0;
      margin-left: -1px;
    }

    .customFormButton {
      text-align: left;
      outline: none;
      align-items: center;
      cursor: pointer;

      &:focus {
        border-color: #f2f2f2 !important;
        background: #f2f2f2 !important;
      }

      &:not(.controlDisabled):hover {
        > .ming.Icon:not(.customFormButtoDel) {
          color: #2196f3 !important;
        }
        .customFormButtoDel {
          display: block;
          &:hover {
            color: rgba(0, 0, 0, 0.45) !important;
          }
          & ~ i {
            display: none;
          }
        }
      }

      .customFormButtoDel {
        display: none;
      }
      &.mobileCustomFormButton {
        &:focus {
          background-color: #fff !important;
        }
      }
    }

    .customFormCheck {
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      label.Radio {
        display: inline-block;
        max-width: 50%;
        margin-right: 0;
        padding-right: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &.Checkbox {
        display: flex;
        white-space: normal !important;
      }
      .Checkbox-box {
        flex-shrink: 0;
      }
      &.mobileCustomFormRadio {
        label.Radio {
          max-width: 100%;
          white-space: inherit;
          padding-right: 0;
          margin-right: 10px;
        }
        label {
          .Radio-box--middle {
            display: inline-block;
          }
          .Radio-text {
            vertical-align: top;
            width: (calc(~'100% - 28px'));
            display: inline-block;
          }
        }
      }
      // .mobileDisableChaeckRadio{
      //   overflow: hidden;
      //   white-space: nowrap;
      //   text-overflow: ellipsis;
      // }
    }

    .customFormReadonly {
      border-color: #fff !important;
      background: #fff !important;
      padding-left: 0 !important;
      line-height: 20px;
      &.spacing {
        letter-spacing: 2px;
      }
    }

    .Score-wrapper {
      .StarScore-item {
        i {
          font-size: 20px;
        }
      }
    }

    .index {
      z-index: 1;
    }

    .customFormRelationBtn {
      background: transparent;
      height: 34px;
      display: flex;
      align-items: center;
      outline: none;
      width: max-content;
      padding: 0 16px;
      border: 1px solid #ddd;
      border-radius: 3px;
      font-weight: 500;
      font-size: 13px;
      &:hover {
        background-color: #f5f5f5;
      }
    }

    > .ming.Dropdown {
      &.controlDisabled {
        .Dropdown--border {
          &:not(:hover):not(.active),
          &:hover {
            border-color: #fff !important;
          }
        }

        .icon-arrow-down-border {
          display: none;
        }
      }

      .Dropdown--input {
        > .value {
          max-width: 100%;
        }
      }

      .Dropdown--border {
        &:not(:hover):not(.active) {
          border-color: #f7f7f7 !important;
        }

        &:hover,
        &.active {
          border-color: #f2f2f2 !important;
        }
      }

      .List {
        width: 100%;

        .MenuItem {
          height: 36px;
          line-height: 36px;

          .Item-content:not(.disabled):hover {
            background: #f2f2f2 !important;
            color: #151515 !important;
          }
        }
      }
    }

    .groupRow,
    .groupColumn {
      &.ming.CheckboxGroup {
        flex-direction: column;
        .Checkbox {
          width: 100% !important;
          margin-right: 0 !important;
        }
      }

      .RadioGroupCon {
        flex-direction: column;
        .Radio {
          width: 100% !important;
          margin-right: 0 !important;
        }
      }

      .ellipsis {
        max-width: 100% !important;
        white-space: normal;
        height: auto !important;
        line-height: 20px !important;
        padding-top: 2px;
        padding-bottom: 2px;
      }
      .nowrap {
        white-space: nowrap;
      }
    }

    .groupRow {
      &.ming.CheckboxGroup {
        flex-direction: row !important;
        .Checkbox {
          margin-right: 20px !important;
        }
      }
      .RadioGroupCon {
        flex-direction: row !important;
        .Radio {
          margin-right: 20px !important;
        }
      }
    }

    .ming.RadioGroup2 {
      .RadioGroupCon {
        display: flex;
        flex-wrap: wrap;

        .Radio {
          display: flex;
          min-width: 0;
          margin-bottom: 10px;

          &--disabled {
            color: #151515;

            .Radio-box {
              display: none;
            }
          }

          &-box {
            margin-right: 8px;
            vertical-align: top;
            margin-top: 3px;
          }

          &-text {
            flex: 1;
            min-width: 0;

            .ellipsis {
              display: inline-block;
              height: 24px;
              line-height: 24px;
              max-width: 100%;
            }
          }
        }
      }
      .horizonArrangementRadio {
        flex-direction: row !important;
        .Radio {
          margin-right: 20px !important;
        }
      }
    }

    .ming.CheckboxGroup {
      display: flex;
      flex-wrap: wrap;

      .flexWidth > span:nth-child(2) {
        flex: 1;
        min-width: 0;
      }

      .Checkbox {
        display: flex;
        min-width: 0;
        margin-bottom: 10px;
        margin-right: 20px;

        &--disabled {
          color: #151515;

          .Checkbox-box {
            display: none;
          }
        }

        &-box {
          margin-right: 8px;
          vertical-align: top;
          margin-top: 3px;
          min-width: 18px;
        }

        .ellipsis {
          display: inline-block;
          height: 24px;
          line-height: 24px;
        }
      }
    }

    .addBtn {
      width: 26px;
      height: 26px;
      line-height: 26px;
      border: 1px solid #ddd;
      border-radius: 50%;
      display: inline-flex;
      vertical-align: top;
      margin: 4px 0;
      align-items: center;
      justify-content: center;
    }

    .dropdownTitle {
      height: 24px;
      line-height: 24px;
      border-radius: 12px;
      padding: 0 12px;
      display: inline-block;
      vertical-align: top;
      max-width: 95%;
      box-sizing: border-box;
    }

    .customAntPicker {
      height: 36px;
      padding: 0 12px;
      border: 1px solid #f7f7f7;
      font-size: 13px;
      background: #f7f7f7;
      border-radius: 4px;
      transition: none;
      &.ant-picker-focused {
        box-shadow: none;
      }
      &.ant-picker-focused:not(.controlDisabled) {
        border-color: #2196f3 !important;
        background: #fff !important;
        .ant-picker-clear {
          opacity: 1;
        }
      }
      .ant-picker-input {
        padding: 0;
        height: 34px !important;
      }
    }

    .customCascaderDel {
      top: 11px;
      right: 11px;
      color: #9e9e9e;
      background: #fff;
      cursor: pointer;
      &:hover {
        color: #757575;
      }
    }

    .customTreeSelect {
      .ant-select-selection-placeholder,
      .ant-select-selection-item {
        line-height: 34px;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .customFormControlTel {
      .iti__selected-country {
        display: none;
      }
      .customFormControlBox {
        padding-left: 0 !important;
        &:not(.controlDisabled) {
          padding-left: 12px !important;
        }
      }
    }
  }
}

.customFormErrorMessage {
  position: absolute;
  bottom: 100%;
  transform: translateY(-5px);
  z-index: 1;
  left: 0;
  color: #fff;
  padding: 3px 8px;
  white-space: nowrap;
  background: #f44336;
  font-size: 12px;
  margin: 0 12px;
  right: 0;
  max-width: fit-content;

  &.isChildTable {
    transform: translateY(15px);
  }

  span {
    white-space: normal;
    .delIcon {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.24);
      &:hover {
        color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .customFormErrorArrow {
    position: absolute;
    transform: translateY(-5px);
    z-index: 1;
    left: 0;
    background: transparent;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent transparent #f44336;
    bottom: -11px;
  }
}

.customRadioItem {
  border-radius: 12px;
  box-sizing: border-box;
  display: inline-block;
  height: 24px;
  font-size: 13px;
  align-items: center;
  vertical-align: middle !important;
  max-width: 100%;
  line-height: 24px;
  font-weight: 400;
}

.mobileCheckboxDialog {
  .customRadioItem {
    font-size: 15px;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
  }
  .am-list-body {
    &::after {
      background-color: #fff !important;
    }
  }
}
.horizonArrangementItem {
  max-width: 100% !important;
  white-space: normal !important;
  height: auto !important;
}
.showRadioTxtAll {
  height: auto !important;
  white-space: pre-wrap;
}

.borderRadiusNone {
  border-radius: 0 !important;
}

.ant-cascader-menus {
  overflow: hidden;
  overflow-x: auto;
  max-width: 900px;
  padding-right: 1px;
}

.customDatePicker {
  border-radius: 10px 10px 0 0;
}

.cascaderRadio {
  align-items: center;
  .Radio-text {
    flex: 1;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

input.customFormControlBox:disabled {
  color: #151515;
  opacity: 1;
  -webkit-text-fill-color: #151515;
}
.customCascader {
  .ant-select-selection-placeholder {
    line-height: 34px !important;
  }
}

.customCascader,
.customAntSelect {
  &.ant-select-open {
    .ant-select-selector {
      border-color: #2196f3 !important;
      background-color: #fff !important;
    }
    .ant-select-arrow {
      display: none;
    }
    .ant-select-selection-item {
      opacity: 0.4;
      color: #151515;
    }
  }

  .ant-select-selection-item,
  .ant-select-selection-placeholder {
    line-height: 34px !important;
    font-size: 13px !important;
  }

  &.ant-select-disabled {
    .ant-select-selector {
      color: #151515 !important;
      border-color: #fff !important;
      background: #fff !important;
      padding: 0 !important;
    }
    .ant-select-selection-item {
      color: #151515;
      padding: 0;
    }
    .ant-select-arrow {
      display: none;
    }
  }

  &:not(.ant-select-open):not(.ant-select-disabled) {
    .ant-select-selector {
      &:hover {
        border-color: #f2f2f2 !important;
        background-color: #f2f2f2 !important;
      }
    }
  }

  .ant-select-selector {
    min-height: 36px;
    padding: 0 12px !important;
    height: auto !important;
    border-radius: 4px !important;
    box-shadow: none !important;
    border-color: #f7f7f7 !important;
    background-color: #f7f7f7 !important;
  }

  &.ant-select-multiple {
    .ant-select-selector {
      > span {
        max-width: 95%;
      }
    }
    .ant-select-selection-search {
      left: 0 !important;
      margin-left: -1px;
    }
    .ant-select-selection-search-input {
      margin-left: 0 !important;
    }
    .customAntDropdownTitle,
    .customAntDropdownTitleWithBG {
      max-width: 100%;
      display: inline-flex;
      .icon-close {
        opacity: 0.7;
        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .ant-select-selection-search {
    left: 12px !important;
  }

  .ant-select-selection-search-input {
    padding: 0;
    height: 34px !important;
  }

  .ant-select-selection-item {
    overflow: inherit;
    max-width: 100%;
    white-space: inherit;
  }

  .customAntSelectPlaceHolder {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    box-sizing: initial;
    padding-top: 8px;
    font-size: 13px;
  }

  .anticon {
    color: #9e9e9e;
    background: transparent;
    &:hover {
      color: #757575;
    }
  }
  .ant-select-selection-placeholder {
    color: #bdbdbd;
  }
}
.mobileCustomApiSelect {
  .ant-select-selector {
    background-color: #fff !important;
    border: 1px solid #e0e0e0 !important;
  }
}

.customAntDropdownTitle,
.customAntDropdownTitleWithBG {
  height: 24px;
  line-height: 24px;
  border-radius: 12px;
  display: inline-block;
  vertical-align: top;
  max-width: 95%;
}

.customAntDropdownTitleWithBG {
  padding: 0 12px;
  box-sizing: border-box;
}

.ruleErrorMsgDialog {
  width: 98% !important;
  max-width: 480px;
  .mui-dialog-desc {
    padding-top: 16px !important;
  }
}

.mobileCustomFormTextareaBox {
  border: 1px solid #e0e0e0 !important;
  margin-bottom: 1px !important;
  border-radius: 2px !important;
  margin-top: 0 !important;
  &:focus {
    border-color: #2196f3 !important;
    background: #fff !important;
    box-shadow: none !important;
  }
}

.customFormControlTags {
  height: 26px;
  border-radius: 26px;
  background: #f7f7f7 !important;
  display: inline-flex;
  align-items: center;
  margin: 4px 8px 4px 0;
  padding-right: 10px;
  vertical-align: top;
  position: relative;
  max-width: 100%;
  .departWrap {
    width: 26px;
    height: 26px;
    color: #fff;
    border-radius: 13px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  &:hover,
  &.selected {
    .tagDel {
      opacity: 1;
    }
  }
  &.isDelete {
    .departWrap {
      background-color: #9e9e9e !important;
    }
    & > span {
      color: #757575;
    }
  }
  .tagDel {
    cursor: pointer;
    color: #9e9e9e;
    position: absolute;
    top: -5px;
    right: -5px;
    opacity: 0;
    transition: all 0.2s ease-out;
    &:hover {
      color: #757575;
    }
  }
}
