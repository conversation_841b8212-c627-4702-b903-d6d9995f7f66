
.attachmentFilesWrap.imageFilesWrap {
  &.mobile .attachmentImageCard {
    min-width: 130px;
    .filePanel {
      display: none;
    }
    .closeIcon {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: #fff;
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
      z-index: 1;
    }
  }
  &.smallSize .attachmentImageCard {
    width: 100%;
    min-width: auto;
    flex: auto;
  }
}

.attachmentImageCard {
  height: 130px;
  min-width: 160px;
  flex: 1;
  margin: 0 6px 6px 0;
  background-color: #fff;
  .kcIcon {
    color: #fff;
    height: 26px;
    width: 26px;
    position: absolute;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, .2);
  }
  .attachmentFile {
    height: 100%;
    border: 1px solid #e0e0e0;
    position: relative;
    &:hover, &.hover {
      .filePanel {
        opacity: 1;
      }
    }
    .Progress--circle-content {
      font-size: 12px !important;
    }
  }
  .fileImage {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }
  .fileAccessory {
    height: 100%;
  }
  .fileIconWrap {
    flex: 1;
    background-color: #f5f5f5;
  }
  .fileIcon {
    width: 40px;
    height: 55px;
    background-position: 50%;
    background-size: contain !important;
  }
  .fileName {
    padding: 0 10px;
    margin: 5px 0;
    background-color: #fff;
  }
  .filePanel {
    cursor: pointer;
    height: 100%;
    left: 0;
    opacity: 0;
    padding: 12px;
    position: absolute;
    top: 0;
    transition: opacity .2s;
    width: 100%;
    &.image {
      background-color: rgba(0,0,0,.6);
    }
    &.accessory {
      background-color: #f5f5f5;
    }
    .name {
      font-weight: 700;
    }
  }
  .operateBtns {
    justify-content: space-between;
  }
  .panelBtn {
    height: 24px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 1px 1px #0000001f;
    cursor: pointer;
    position: relative;
    &:hover .icon {
      color: #2196f3 !important;
    }
    &.delete:hover .icon {
      color: red !important;
    }
  }
  .resetNameInput {
    width: 100%;
    display: flex;
    height: 30px;
    border: 1px solid #ccc;
    padding: 0 10px;
    border-radius: 4px;
  }
}
