
.attachmentFilesWrap.smallFilesWrap {
  &.mobile .attachmentSmallCard {
    margin: 0 0 6px 0;
  }
  &.smallSize .attachmentSmallCard {
    width: 100%;
    min-width: auto;
    flex: auto;
  }
}

.attachmentSmallCard {
  height: 56px;
  min-width: 300px;
  flex: 1;
  margin: 0 6px 6px 0;
  border-radius: 4px;
  background: #FAFAFA;
  position: relative;
  &:hover, &.hover {
    background: #F2F2F2;
    .operateBtns {
      display: flex;
    }
    .textEllipsis {
      -webkit-line-clamp: 1;
    }
    .fileSize {
      display: block !important;
    }
  }
  .fileImageWrap {
    width: 56px;
    height: 100%;
    border-radius: 4px 0 0 4px;
    overflow: hidden;
    .fileIcon {
      width: 20px;
      height: 35px;
      background-position: 50%;
      background-size: contain !important;
    }
    .fileImage {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    }
    .kcIcon {
      color: #fff;
      height: 20px;
      width: 20px;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, .2);
    }
  }
  .fileContent {
    font-size: 13px;
    min-width: 0;
    height: 100%;
    padding: 0 10px;
  }
  .fileName {
    font-size: 13px;
    font-weight: bold;
    line-height: 16px;
  }
  .fileSize {
    margin-top: 3px;
  }
  .operateBtns {
    display: none;
    justify-content: space-around;
    height: 100%;
    margin-right: 5px;
    .btnWrap {
      padding: 0 5px;
      .icon:hover {
        color: #2196f3 !important;
      }
    }
    .delete {
      .icon:hover {
        color: #f44336 !important;
      }
    }
    .cancelBtn, .deleteBtn {
      padding: 5px 16px;
    }
  }
  .deleteIcon {
    margin-right: 5px;
  }
}
