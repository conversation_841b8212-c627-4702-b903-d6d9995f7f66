import Radio from './Radio';
import Checkbox from './Checkbox';
import Dropdown from './Dropdown';
import DateWidgets from './Date';
import DateRange from './DateRange';
import Area from './Area';
import Readonly from './Readonly';
import Email from './Email';
import ID from './ID';
import TelPhone from './TelPhone';
import MobilePhone from './MobilePhone';
import Textarea from './Textarea';
import NumberWidgets from './Number';
import Check from './Check';
import Attachment from './Attachment';
import UserSelect from './UserSelect';
import DepartmentSelect from './DepartmentSelect';
import RangeWidgets from './Range';
import Relation from './Relation';
import RelateRecord from './RelateRecord';
import SubList from './SubList';
import DateCalc from './DateCalc';
import RichText from './RichText';
import Signature from './Signature';
import Location from './Location';
import Cascader from './Cascader';
import OCR from './OCR';
import Subtotal from './Subtotal';
import Embed from './Embed';
import Time from './Time';
import BarCode from './BarCode';
import OrgRole from './OrgRole';
import Search from './Search';
import RelationSearch from './RelationSearch';
import Section from './Section';
import SplitLine from './SplitLine';
import FormulaFunc from './FormulaFunc';

export default {
  RADIO: Radio,
  CHECKBOX: Checkbox,
  DROP_DOWN: Dropdown,
  DATE: DateWidgets,
  DATE_RANGE: DateRange,
  AREA: Area,
  READONLY: Readonly,
  EMAIL: Email,
  ID: ID,
  TEL_PHONE: TelPhone,
  MOBILE_PHONE: MobilePhone,
  TEXTAREA: Textarea,
  NUMBER: NumberWidgets,
  CHECK: Check,
  ATTACHMENT: Attachment,
  USER_SELECT: UserSelect,
  DEPARTMENT_SELECT: DepartmentSelect,
  RANGE: RangeWidgets,
  RELATION: Relation,
  RELATE_RECORD: RelateRecord,
  SUBLIST: SubList,
  DATECALC: DateCalc,
  RICH_TEXT: RichText,
  SIGNATURE: Signature,
  LOCATION: Location,
  Cascader: Cascader,
  OCR: OCR,
  SUBTOTAL: Subtotal,
  Embed: Embed,
  Time: Time,
  BarCode: BarCode,
  OrgRole: OrgRole,
  Search: Search,
  RelationSearch: RelationSearch,
  Section: Section,
  SplitLine: SplitLine,
  FormulaFunc: FormulaFunc,
};
