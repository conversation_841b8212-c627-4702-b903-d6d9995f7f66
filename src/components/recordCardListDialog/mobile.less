
.mobileRecordCardListDialog {
  height: 100%;

  .searchWrapper {
    display: flex;
    align-items: center;
    background-color: #F8F8F8;
    border-radius: 24px;
    padding: 7px 10px;
    .icon-h5_search {
      color: #757575;
      font-size: 20px;
    }
    .icon-workflow_cancel {
      color: #BDBDBD;
      font-size: 15px;
    }
    input {
      flex: 1;
      border: 0;
      padding: 0 5px;
      background-color: #F8F8F8;
    }
  }
  .filterWrapper {
    background-color: #fff;
    padding: 10px;
    border-radius: 50%;
    width: 34px;
    height: 34px;
  }

  .worksheetRecordCard {
    .deleteRecord {
      display: none !important;
    }
  }

  .allowNewRecordBtn {
    color: #108ee9;
    height: 50px;
    padding-left: 10px;
  }

  .empty {
    height: 100%;
    justify-content: center;
    .icon {
      font-size: 66px;
      color: #bdbdbd;
    }
  }

  .btnsWrapper {
    height: 50px;
    background-color: #fff;
    padding: 0 10px;
    box-sizing: border-box;
  }
}

.quickFilterStepListWrapper {
  -webkit-overflow-scrolling: touch;
  position: inherit !important;
  .am-drawer-sidebar {
    z-index: 100;
    border-radius: 14px 0 0 14px;
    background-color: #fff;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }
  &.open {
    .am-drawer-overlay, .am-drawer-content {
      position: absolute;
    }
  }
  .am-drawer-overlay, .am-drawer-content {
    position: inherit;
  }
  &.am-drawer-open {
    z-index: 100;
    position: fixed;
  }
  &.bottom50 {
    bottom: 50px;
  }
}

.mobileModal {
  &.full .adm-popup-body-position-bottom {
    height: 100%;
  }
  &.minFull .adm-popup-body-position-bottom {
    height: 95%;
  }
  &.topRadius .adm-popup-body-position-bottom {
    border-top-right-radius: 15px;
    border-top-left-radius: 15px;
    overflow: hidden;
  }
}
