.recordCardListDialog {
  background: #f8f8f8 !important;
  .mui-dialog-header {
    padding-top: 10px !important;
    padding-bottom: 0px !important;
  }
  .recordCardListCon {
    position: relative;
    height: 100%;
  }
  .searchRecord {
    border-bottom: 1px #e0e0e0 solid;
    .icon {
      font-size: 20px;
      line-height: 36px;
    }
    .searchInput {
      padding-left: 6px;
      border: none;
      background: #f8f8f8;
    }
    .clearSearch {
      font-size: 15px;
      color: #9d9d9d;
      margin-right: 9px;
    }
  }
  .recordCardListHeader {
    margin-top: 4px;
    min-height: 21px;
    .icon-ic_title {
      font-size: 17px;
      color: #bdbdbd;
    }
    .controlName {
      margin-left: 16px;
      overflow: hidden;
      display: flex;
      .controlNameValue {
        display: inline-block;
        max-width: calc(~'100% - 14px');
      }
      .orderStatus {
        display: inline-block;
        font-size: 12px;
        vertical-align: middle;
        margin-left: 2px;
        transform: scale(0.9);
        .icon {
          color: #bdbdbd;
          &.hover {
            color: #9e9e9e;
          }
        }
        .icon-arrow-up {
          margin-bottom: -4px;
        }
      }
      &:last-child {
        margin-right: 16px;
      }
      &.title {
        position: absolute;
        margin-left: -6px;
      }
      &:nth-child(2) .controlNameValue {
        margin-left: 20px;
      }
    }
  }
  .recordCardList {
    width: auto !important;
    margin: 0 -24px 10px -24px;
    .MdLoader {
      margin: 20px auto;
    }
    .empty {
      height: 100%;
      text-align: center;
      .emptyIcon {
        position: relative;
        top: 50%;
        margin-top: -50px;
        .icon {
          font-size: 66px;
          color: #bdbdbd;
        }
        .emptyTip {
          color: #9e9e9e;
          top: 50%;
          position: relative;
        }
      }
    }
    .worksheetRecordCard {
      margin: 12px 28px 12px 24px;
      .deleteRecord {
        display: none;
      }
    }
  }
  .clickSearchTip {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #9e9e9e;
    font-size: 15px;
  }
  .recordCardListFooter {
    border-top: 1px solid #e0e0e0;
    padding-top: 14px;
    margin-bottom: -7px;
    .addRecord {
      line-height: 36px;
      color: #fff;
      padding: 0 30px;
      border-radius: 36px;
    }
  }
}
