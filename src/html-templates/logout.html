<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no, viewport-fit=cover" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <meta name="renderer" content="webkit" />
        <meta name="force-rendering" content="webkit" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <title content="登出"></title>
    </head>
    <body>
        <script>
            if (/theportal\.cn$/.test(location.host)) {
                window.__api_server__.main = '/api/';
            }
            var request = new XMLHttpRequest();
            var cookie = document.cookie.match(/md_pss_id=(\w+)/)[1];
            request.open('POST', __api_server__.main + 'Login/LoginOut');
            request.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
            request.setRequestHeader('Authorization', 'md_pss_id ' + cookie);
            request.send();
            request.onload = function () {
                if (this.status >= 200 && this.status < 400) {
                    var resp = this.response;
                    try {
                        resp = JSON.parse(resp);
                        if (resp && resp.data) {
                            window.localStorage.removeItem('LoginCheckList');

                            if (resp.data.redirectUrl) {
                                location.href = resp.data.redirectUrl;
                            } else if (location.href.indexOf('?RedirectUrl') > -1) {
                                location.href = decodeURIComponent(location.search.replace('?RedirectUrl=', ''));
                            } else if (localStorage.getItem('loginFrom') === '2') {
                                location.href = window.subPath ? window.subPath + '/network' : '/network';
                            } else {
                                var loginUrl = window.subPath ? window.subPath + '/login' : '/login';

                                if (location.href.indexOf('?ReturnUrl') > -1) {
                                    location.href = loginUrl + location.search;
                                } else {
                                    location.href = loginUrl;
                                }
                            }
                        }
                    } catch (err) {}
                }
            };
        </script>
    </body>
</html>
